'use client'

import { useState, useMemo } from 'react'
import { Search, Star, TrendingUp, Plus, X, ChevronDown, ChevronUp, ExternalLink as LinkIcon } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import FrontendHeader from '../../components/frontend-header'
import FrontendFooter from '../../components/frontend-footer'

// 定义需求词根接口
interface NeedKeyword {
  keyword: string
  description: string
  isStarred?: boolean
}

interface NeedCategory {
  name: string
  nameEn: string
  description: string
  ageGroup: string
  keywords: NeedKeyword[]
}

export default function NeedsPage() {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [selectedKeywords, setSelectedKeywords] = useState<string[]>([])
  const [collapsedCategories, setCollapsedCategories] = useState<Record<string, boolean>>({})

  // 全年龄段用户需求导向词根数据
  const needCategories: NeedCategory[] = [
    // === 学习教育类 ===
    {
      name: '学习教育',
      nameEn: 'Learning & Education',
      description: '全年龄段学习需求，从基础教育到技能培养',
      ageGroup: '全年龄',
      keywords: [
        // 基础学习
        { keyword: 'learn', description: '学习辅助', isStarred: true },
        { keyword: 'study', description: '学习方法', isStarred: true },
        { keyword: 'homework', description: '作业帮助', isStarred: true },
        { keyword: 'exam', description: '考试准备', isStarred: true },
        { keyword: 'quiz', description: '测验练习', isStarred: true },
        { keyword: 'tutor', description: '辅导教学' },
        { keyword: 'explain', description: '解释说明' },
        { keyword: 'practice', description: '练习训练' },
        // 语言学习
        { keyword: 'english', description: '英语学习' },
        { keyword: 'chinese', description: '中文学习' },
        { keyword: 'spanish', description: '西班牙语' },
        { keyword: 'french', description: '法语学习' },
        { keyword: 'japanese', description: '日语学习' },
        { keyword: 'korean', description: '韩语学习' },
        { keyword: 'translate', description: '翻译需求' },
        { keyword: 'pronunciation', description: '发音练习' },
        // 技能培养
        { keyword: 'skill', description: '技能提升' },
        { keyword: 'course', description: '课程学习' },
        { keyword: 'training', description: '培训教育' },
        { keyword: 'certificate', description: '证书考试' },
        { keyword: 'coding', description: '编程学习' },
        { keyword: 'math', description: '数学学习' },
        { keyword: 'science', description: '科学知识' },
        { keyword: 'history', description: '历史学习' }
      ]
    },

    // === 工作职场类 ===
    {
      name: '工作职场',
      nameEn: 'Work & Career',
      description: '职场人士工作效率与职业发展需求',
      ageGroup: '18-60岁',
      keywords: [
        // 求职就业
        { keyword: 'resume', description: '简历制作', isStarred: true },
        { keyword: 'interview', description: '面试准备', isStarred: true },
        { keyword: 'job', description: '求职相关', isStarred: true },
        { keyword: 'career', description: '职业规划', isStarred: true },
        { keyword: 'portfolio', description: '作品集' },
        { keyword: 'linkedin', description: '职业社交' },
        { keyword: 'networking', description: '人脉建立' },
        { keyword: 'salary', description: '薪资谈判' },
        // 工作效率
        { keyword: 'email', description: '邮件处理' },
        { keyword: 'meeting', description: '会议管理' },
        { keyword: 'presentation', description: '演示制作' },
        { keyword: 'report', description: '报告撰写' },
        { keyword: 'plan', description: '计划制定' },
        { keyword: 'task', description: '任务管理' },
        { keyword: 'deadline', description: '截止日期' },
        { keyword: 'productivity', description: '生产力' },
        // 职业发展
        { keyword: 'business', description: '商务相关' },
        { keyword: 'proposal', description: '提案撰写' },
        { keyword: 'leadership', description: '领导力' },
        { keyword: 'management', description: '管理技能' },
        { keyword: 'promotion', description: '晋升发展' },
        { keyword: 'teamwork', description: '团队合作' },
        { keyword: 'communication', description: '沟通技巧' },
        { keyword: 'negotiation', description: '谈判技巧' }
      ]
    },

    // === 生活管理类 ===
    {
      name: '生活管理',
      nameEn: 'Life Management',
      description: '日常生活管理与健康养生需求',
      ageGroup: '全年龄',
      keywords: [
        // 健康管理
        { keyword: 'health', description: '健康管理', isStarred: true },
        { keyword: 'fitness', description: '健身运动', isStarred: true },
        { keyword: 'diet', description: '饮食管理', isStarred: true },
        { keyword: 'nutrition', description: '营养搭配', isStarred: true },
        { keyword: 'sleep', description: '睡眠改善' },
        { keyword: 'meditation', description: '冥想放松' },
        { keyword: 'exercise', description: '运动锻炼' },
        { keyword: 'weight', description: '体重管理' },
        { keyword: 'yoga', description: '瑜伽练习' },
        { keyword: 'doctor', description: '医疗咨询' },
        // 财务管理
        { keyword: 'budget', description: '预算管理' },
        { keyword: 'money', description: '资金管理' },
        { keyword: 'expense', description: '支出跟踪' },
        { keyword: 'saving', description: '储蓄计划' },
        { keyword: 'investment', description: '投资理财' },
        { keyword: 'bill', description: '账单管理' },
        // 日常管理
        { keyword: 'shopping', description: '购物助手' },
        { keyword: 'calendar', description: '日程安排' },
        { keyword: 'reminder', description: '提醒事项' },
        { keyword: 'habit', description: '习惯养成' },
        { keyword: 'routine', description: '日常例行' },
        { keyword: 'organization', description: '整理收纳' },
        // 家庭生活
        { keyword: 'family', description: '家庭管理' },
        { keyword: 'parenting', description: '育儿指导' },
        { keyword: 'kids', description: '儿童相关' },
        { keyword: 'baby', description: '婴儿护理' },
        { keyword: 'elderly', description: '老人照护' },
        { keyword: 'pet', description: '宠物照料' },
        { keyword: 'home', description: '家居管理' },
        { keyword: 'cleaning', description: '清洁整理' }
      ]
    },

    // === 娱乐休闲类 ===
    {
      name: '娱乐休闲',
      nameEn: 'Entertainment & Leisure',
      description: '游戏娱乐与兴趣爱好需求',
      ageGroup: '全年龄',
      keywords: [
        // 游戏娱乐
        { keyword: 'game', description: '游戏相关', isStarred: true },
        { keyword: 'puzzle', description: '益智游戏', isStarred: true },
        { keyword: 'word', description: '文字游戏', isStarred: true },
        { keyword: 'number', description: '数字游戏' },
        { keyword: 'memory', description: '记忆游戏' },
        { keyword: 'brain', description: '脑力训练' },
        { keyword: 'trivia', description: '知识竞赛' },
        { keyword: 'fun', description: '趣味内容' },
        // 兴趣爱好
        { keyword: 'music', description: '音乐相关' },
        { keyword: 'movie', description: '电影推荐' },
        { keyword: 'book', description: '阅读推荐' },
        { keyword: 'art', description: '艺术创作' },
        { keyword: 'craft', description: '手工制作' },
        { keyword: 'cooking', description: '烹饪美食' },
        { keyword: 'garden', description: '园艺种植' },
        { keyword: 'travel', description: '旅行规划' },
        // 社交娱乐
        { keyword: 'party', description: '聚会活动' },
        { keyword: 'gift', description: '礼物推荐' },
        { keyword: 'celebration', description: '庆祝活动' },
        { keyword: 'holiday', description: '节日相关' },
        { keyword: 'birthday', description: '生日庆祝' },
        { keyword: 'wedding', description: '婚礼策划' },
        { keyword: 'anniversary', description: '纪念日' },
        { keyword: 'festival', description: '节庆活动' }
      ]
    },

    // === 创意表达类 ===
    {
      name: '创意表达',
      nameEn: 'Creative Expression',
      description: '内容创作与创意表达需求',
      ageGroup: '全年龄',
      keywords: [
        // 内容创作
        { keyword: 'write', description: '写作创作', isStarred: true },
        { keyword: 'story', description: '故事创作', isStarred: true },
        { keyword: 'poem', description: '诗歌创作', isStarred: true },
        { keyword: 'blog', description: '博客写作', isStarred: true },
        { keyword: 'diary', description: '日记记录' },
        { keyword: 'letter', description: '书信写作' },
        { keyword: 'script', description: '脚本编写' },
        { keyword: 'caption', description: '文案配图' },
        // 视觉创作
        { keyword: 'photo', description: '照片处理' },
        { keyword: 'draw', description: '绘画创作' },
        { keyword: 'design', description: '设计制作' },
        { keyword: 'logo', description: '标志设计' },
        { keyword: 'avatar', description: '头像制作' },
        { keyword: 'poster', description: '海报设计' },
        { keyword: 'card', description: '卡片制作' },
        { keyword: 'album', description: '相册整理' },
        // 多媒体创作
        { keyword: 'video', description: '视频制作' },
        { keyword: 'animation', description: '动画创作' },
        { keyword: 'voice', description: '语音处理' },
        { keyword: 'podcast', description: '播客制作' },
        { keyword: 'slideshow', description: '幻灯片' },
        { keyword: 'meme', description: '表情包' },
        { keyword: 'gif', description: '动图制作' },
        { keyword: 'filter', description: '滤镜效果' }
      ]
    },

    // === 情感社交类 ===
    {
      name: '情感社交',
      nameEn: 'Social & Emotional',
      description: '人际交往与情感支持需求',
      ageGroup: '全年龄',
      keywords: [
        // 人际交往
        { keyword: 'friend', description: '交友社交', isStarred: true },
        { keyword: 'chat', description: '聊天对话', isStarred: true },
        { keyword: 'dating', description: '约会交友', isStarred: true },
        { keyword: 'relationship', description: '人际关系', isStarred: true },
        { keyword: 'social', description: '社交技巧' },
        { keyword: 'conversation', description: '对话技巧' },
        { keyword: 'community', description: '社区交流' },
        // 情感支持
        { keyword: 'love', description: '恋爱情感' },
        { keyword: 'advice', description: '建议咨询' },
        { keyword: 'comfort', description: '安慰陪伴' },
        { keyword: 'therapy', description: '心理疏导' },
        { keyword: 'mood', description: '情绪管理' },
        { keyword: 'stress', description: '压力缓解' },
        { keyword: 'anxiety', description: '焦虑处理' },
        { keyword: 'depression', description: '抑郁帮助' },
        // 特殊群体需求
        { keyword: 'senior', description: '老年人专用' },
        { keyword: 'teen', description: '青少年专用' },
        { keyword: 'parent', description: '父母专用' },
        { keyword: 'student', description: '学生专用' },
        { keyword: 'worker', description: '职场专用' },
        { keyword: 'retired', description: '退休人员' },
        { keyword: 'disabled', description: '残障辅助' },
        { keyword: 'caregiver', description: '照护者' }
      ]
    },

    // === 工具聚合类 ===
    {
      name: '工具聚合',
      nameEn: 'Tool Aggregation',
      description: '各类实用工具与便民服务',
      ageGroup: '全年龄',
      keywords: [
        // 基础工具
        { keyword: 'tool', description: '工具集合', isStarred: true },
        { keyword: 'calculator', description: '计算器', isStarred: true },
        { keyword: 'converter', description: '转换工具', isStarred: true },
        { keyword: 'generator', description: '生成器', isStarred: true },
        { keyword: 'checker', description: '检查工具' },
        { keyword: 'maker', description: '制作工具' },
        { keyword: 'builder', description: '构建工具' },
        { keyword: 'helper', description: '辅助工具' },
        // 便民服务
        { keyword: 'weather', description: '天气查询' },
        { keyword: 'map', description: '地图导航' },
        { keyword: 'time', description: '时间工具' },
        { keyword: 'currency', description: '货币换算' },
        { keyword: 'unit', description: '单位转换' },
        { keyword: 'qr', description: '二维码' },
        { keyword: 'barcode', description: '条形码' },
        { keyword: 'search', description: '搜索工具' },
        // 技术简化
        { keyword: 'simple', description: '简单易用' },
        { keyword: 'easy', description: '容易操作' },
        { keyword: 'quick', description: '快速完成' },
        { keyword: 'auto', description: '自动化' },
        { keyword: 'smart', description: '智能化' },
        { keyword: 'instant', description: '即时生成' },
        { keyword: 'free', description: '免费使用' },
        { keyword: 'online', description: '在线服务' }
      ]
    },

    // === 特殊场景类 ===
    {
      name: '特殊场景',
      nameEn: 'Special Scenarios',
      description: '特定场景与紧急需求',
      ageGroup: '全年龄',
      keywords: [
        // 节日季节
        { keyword: 'christmas', description: '圣诞节', isStarred: true },
        { keyword: 'valentine', description: '情人节', isStarred: true },
        { keyword: 'halloween', description: '万圣节', isStarred: true },
        { keyword: 'graduation', description: '毕业典礼', isStarred: true },
        { keyword: 'summer', description: '夏季活动' },
        { keyword: 'winter', description: '冬季需求' },
        // 紧急需求
        { keyword: 'emergency', description: '紧急情况' },
        { keyword: 'urgent', description: '紧急处理' },
        { keyword: 'last-minute', description: '临时需求' },
        { keyword: 'quick-fix', description: '快速解决' },
        { keyword: 'backup', description: '备份方案' },
        { keyword: 'recovery', description: '恢复处理' },
        { keyword: 'repair', description: '修复问题' },
        { keyword: 'troubleshoot', description: '故障排除' },
        // 移动优先
        { keyword: 'mobile', description: '移动设备' },
        { keyword: 'phone', description: '手机专用' },
        { keyword: 'tablet', description: '平板设备' },
        { keyword: 'touch', description: '触摸操作' },
        { keyword: 'offline', description: '离线使用' },
        { keyword: 'sync', description: '同步功能' },
        { keyword: 'cloud', description: '云端服务' }
      ]
    },

    // === 新兴趋势类 ===
    {
      name: '新兴趋势',
      nameEn: 'Emerging Trends',
      description: '前瞻性需求与新兴技术应用',
      ageGroup: '全年龄',
      keywords: [
        // AI智能化
        { keyword: 'ai', description: '人工智能', isStarred: true },
        { keyword: 'predict', description: '预测分析', isStarred: true },
        { keyword: 'recommend', description: '推荐系统', isStarred: true },
        { keyword: 'personalize', description: '个性化', isStarred: true },
        { keyword: 'optimize', description: '优化改进' },
        { keyword: 'assistant', description: '智能助手' },
        // 数字化生活
        { keyword: 'digital', description: '数字化' },
        { keyword: 'virtual', description: '虚拟现实' },
        { keyword: 'augmented', description: '增强现实' },
        { keyword: 'metaverse', description: '元宇宙' },
        { keyword: 'nft', description: '数字藏品' },
        { keyword: 'crypto', description: '加密货币' },
        { keyword: 'blockchain', description: '区块链' },
        { keyword: 'web3', description: '去中心化' },
        // 可持续发展
        { keyword: 'eco', description: '环保生态' },
        { keyword: 'green', description: '绿色环保' },
        { keyword: 'sustainable', description: '可持续' },
        { keyword: 'recycle', description: '回收利用' },
        { keyword: 'energy', description: '能源管理' },
        { keyword: 'carbon', description: '碳足迹' },
        { keyword: 'climate', description: '气候变化' },
        { keyword: 'organic', description: '有机天然' }
      ]
    },

    // === 垂直领域类 ===
    {
      name: '垂直领域',
      nameEn: 'Vertical Domains',
      description: '专业细分领域的深度需求',
      ageGroup: '专业人士',
      keywords: [
        // 医疗健康
        { keyword: 'symptom', description: '症状查询', isStarred: true },
        { keyword: 'medicine', description: '药物信息', isStarred: true },
        { keyword: 'diagnosis', description: '诊断辅助', isStarred: true },
        { keyword: 'treatment', description: '治疗方案', isStarred: true },
        { keyword: 'prevention', description: '预防保健' },
        { keyword: 'mental', description: '心理健康' },
        { keyword: 'wellness', description: '健康养生' },
        // 教育培训
        { keyword: 'curriculum', description: '课程设计' },
        { keyword: 'assessment', description: '评估测试' },
        { keyword: 'pedagogy', description: '教学方法' },
        { keyword: 'e-learning', description: '在线教育' },
        { keyword: 'certification', description: '认证考试' },
        { keyword: 'scholarship', description: '奖学金' },
        { keyword: 'research', description: '学术研究' },
        { keyword: 'thesis', description: '论文写作' },
        // 商业金融
        { keyword: 'startup', description: '创业指导' },
        { keyword: 'marketing', description: '市场营销' },
        { keyword: 'sales', description: '销售技巧' },
        { keyword: 'accounting', description: '会计财务' },
        { keyword: 'tax', description: '税务处理' },
        { keyword: 'insurance', description: '保险规划' },
        { keyword: 'retirement', description: '退休规划' }
      ]
    }
  ]

  // 统计信息
  const totalKeywords = useMemo(() => {
    return needCategories.reduce((total, cat) => total + cat.keywords.length, 0)
  }, [])

  const starredKeywords = useMemo(() => {
    return needCategories.reduce((total, cat) =>
      total + cat.keywords.filter(k => k.isStarred).length, 0)
  }, [])

  // 获取所有分类名称
  const categories = ['all', ...needCategories.map(cat => cat.name)]

  // 过滤需求词根
  const filteredNeeds = needCategories.filter(category => {
    if (selectedCategory !== 'all' && category.name !== selectedCategory) {
      return false
    }
    
    if (searchTerm) {
      return category.keywords.some(item => 
        item.keyword.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.description.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }
    
    return true
  }).map(category => ({
    ...category,
    keywords: category.keywords.filter(item =>
      !searchTerm || 
      item.keyword.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.description.toLowerCase().includes(searchTerm.toLowerCase())
    )
  }))

  // 生成Google Trends链接
  const generateTrendsUrl = (keywords: string[]) => {
    if (keywords.length === 1) {
      // 单个关键词的情况
      const query = `${keywords[0]},gpts`
      return `https://trends.google.com/trends/explore?date=now%207-d&geo=US&q=${encodeURIComponent(query)}&hl=en-US`
    } else {
      // 多个关键词组合成一个短语
      const combinedKeyword = keywords.join(' ')
      const query = `${combinedKeyword},gpts`
      return `https://trends.google.com/trends/explore?date=now%207-d&geo=US&q=${encodeURIComponent(query)}&hl=en-US`
    }
  }

  // 添加关键词到组合
  const addToCombo = (keyword: string) => {
    if (!selectedKeywords.includes(keyword) && selectedKeywords.length < 5) {
      setSelectedKeywords([...selectedKeywords, keyword])
    }
  }

  // 从组合中移除关键词
  const removeFromCombo = (keyword: string) => {
    setSelectedKeywords(selectedKeywords.filter(k => k !== keyword))
  }

  // 切换分类折叠状态
  const toggleCategoryCollapse = (categoryName: string) => {
    setCollapsedCategories(prev => ({
      ...prev,
      [categoryName]: !prev[categoryName]
    }))
  }

  // 批量打开分类下所有关键词的Google Trends链接
  const openAllTrendsInCategory = (category: NeedCategory) => {
    category.keywords.forEach((item, index) => {
      // 随机间隔500ms-1500ms，避免触发谷歌频率限制
      const randomDelay = Math.random() * 1000 + 500 + (index * 200) // 基础随机500-1500ms + 递增间隔
      setTimeout(() => {
        window.open(generateTrendsUrl([item.keyword]), '_blank', 'noopener,noreferrer')
      }, randomDelay)
    })
  }

  return (
    <div className="min-h-screen bg-background">
      {/* 使用统一前台顶部组件 */}
      <FrontendHeader />

      {/* 搜索和筛选区域 */}
      <section className="py-6 px-4 bg-background border-b">
        <div className="container mx-auto">
          <div className="max-w-4xl mx-auto">
            <div className="bg-card rounded-xl shadow-sm border p-6">
              {/* 页面标题 */}
              <div className="text-center mb-6">
                <h1 className="text-3xl font-bold text-foreground mb-2">需求导航</h1>
                <p className="text-muted-foreground">全年龄段用户需求导向关键词挖掘，已验证Google Trends热度，涵盖9大分类200+需求词根</p>
              </div>

              {/* 搜索框 */}
              <div className="flex justify-center mb-6">
                <div className="relative w-full max-w-md">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-muted-foreground" />
                  <Input
                    type="text"
                    placeholder="搜索需求词根..."
                    className="pl-10 h-12 text-lg border-2 border-purple-200 focus:border-purple-500 focus:ring-purple-500 hover:border-purple-300 transition-colors"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
              </div>

              {/* 分类筛选 */}
              <div className="flex flex-wrap justify-center gap-2 mb-4">
                {categories.map((category) => (
                  <Button
                    key={category}
                    variant={selectedCategory === category ? "default" : "outline"}
                    size="sm"
                    onClick={() => setSelectedCategory(category)}
                    className={selectedCategory === category
                      ? "bg-purple-600 hover:bg-purple-700 border-purple-600 text-white"
                      : "border-2 border-purple-200 hover:border-purple-500 hover:bg-purple-50 transition-colors"
                    }
                  >
                    {category === 'all' ? '全部' : category}
                  </Button>
                ))}
              </div>


            </div>
          </div>
        </div>
      </section>

      {/* 需求词根内容区域 */}
      <section className="py-8 px-4">
        <div className="container mx-auto">
          {filteredNeeds.length === 0 ? (
            <Card className="text-center py-12">
              <CardContent>
                <Search className="h-16 w-16 mx-auto text-gray-400 mb-4" />
                <h3 className="text-xl font-semibold text-foreground mb-2">未找到相关需求词根</h3>
                <p className="text-muted-foreground">
                  试试调整搜索关键词或选择其他分类
                </p>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-8">
              {filteredNeeds.map((category) => (
                <div key={category.name}>
                  <div className="flex items-center mb-6">
                    <div className="p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg mr-3">
                      <TrendingUp className="h-5 w-5 text-white" />
                    </div>
                    <div className="flex-1">
                      <h2 className="text-2xl font-bold text-foreground">{category.name}</h2>
                      <p className="text-sm text-muted-foreground">{category.nameEn} • {category.ageGroup}</p>
                      {category.description && (
                        <p className="text-sm text-blue-600 mt-1 font-medium">{category.description}</p>
                      )}
                    </div>
                    <div className="flex items-center gap-2 ml-auto">
                      <Badge variant="secondary">
                        {category.keywords.length} 个词根
                      </Badge>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => openAllTrendsInCategory(category)}
                        className="text-xs border-2 border-purple-200 hover:border-purple-500 hover:bg-purple-50 transition-colors flex items-center gap-1"
                      >
                        <LinkIcon className="h-3 w-3" />
                        新窗口
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => toggleCategoryCollapse(category.name)}
                        className="text-xs"
                      >
                        {collapsedCategories[category.name] ? (
                          <>
                            <ChevronDown className="h-4 w-4 mr-1" />
                            展开
                          </>
                        ) : (
                          <>
                            <ChevronUp className="h-4 w-4 mr-1" />
                            收起
                          </>
                        )}
                      </Button>
                    </div>
                  </div>

                  {!collapsedCategories[category.name] && (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                      {/* 先显示标星的词根 */}
                      {category.keywords
                        .filter(item => item.isStarred)
                        .map((item, index) => (
                        <Card key={`starred-${index}`} className="group hover:shadow-lg transition-all duration-200 hover:scale-105 border-border hover:border-primary/30 bg-gradient-to-br from-yellow-50 to-orange-50 border-yellow-200">
                          <CardContent className="p-4">
                            <div className="flex items-center justify-between mb-2">
                              <span className="font-semibold text-foreground group-hover:text-primary transition-colors">
                                {item.keyword}
                              </span>
                              <Star className="h-4 w-4 text-yellow-500 fill-current" />
                            </div>
                            <p className="text-xs text-muted-foreground mb-3">{item.description}</p>
                            <div className="flex gap-2">
                              <Button
                                asChild
                                size="sm"
                                className="flex-1 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white border-0 transition-all duration-200"
                              >
                                <a
                                  href={generateTrendsUrl([item.keyword])}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="flex items-center justify-center"
                                >
                                  <TrendingUp className="h-3 w-3 mr-1" />
                                  趋势
                                </a>
                              </Button>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => addToCombo(item.keyword)}
                                disabled={selectedKeywords.includes(item.keyword) || selectedKeywords.length >= 5}
                              >
                                <Plus className="h-3 w-3" />
                              </Button>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    
                    {/* 然后显示普通词根 */}
                    {category.keywords
                      .filter(item => !item.isStarred)
                      .map((item, index) => (
                        <Card key={`normal-${index}`} className="group hover:shadow-lg transition-all duration-200 hover:scale-105 border-border hover:border-primary/30">
                          <CardContent className="p-4">
                            <div className="flex items-center justify-between mb-2">
                              <span className="font-semibold text-foreground group-hover:text-primary transition-colors">
                                {item.keyword}
                              </span>
                            </div>
                            <p className="text-xs text-muted-foreground mb-3">{item.description}</p>
                            <div className="flex gap-2">
                              <Button
                                asChild
                                size="sm"
                                className="flex-1 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white border-0 transition-all duration-200"
                              >
                                <a
                                  href={generateTrendsUrl([item.keyword])}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="flex items-center justify-center"
                                >
                                  <TrendingUp className="h-3 w-3 mr-1" />
                                  趋势
                                </a>
                              </Button>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => addToCombo(item.keyword)}
                                disabled={selectedKeywords.includes(item.keyword) || selectedKeywords.length >= 5}
                              >
                                <Plus className="h-3 w-3" />
                              </Button>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      </section>

      {/* 教程示范区域 */}
      <section className="py-8 px-4 bg-muted/30">
        <div className="container mx-auto">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-8">
              <h2 className="text-2xl font-bold text-foreground mb-2">实际应用案例</h2>
              <p className="text-muted-foreground">按年龄段的词根组合示例，助您快速上手关键词挖掘</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {/* 儿童 (6-12岁) */}
              <div className="bg-card rounded-lg p-6 border">
                <h3 className="text-lg font-semibold text-foreground mb-4 flex items-center">
                  <span className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center text-lg mr-3">🧒</span>
                  儿童
                </h3>
                <div className="space-y-3">
                  <div className="flex items-center justify-between p-2 bg-muted/50 rounded">
                    <span className="text-sm"><code>kids + game</code></span>
                    <span className="text-xs text-muted-foreground">儿童游戏</span>
                  </div>
                  <div className="flex items-center justify-between p-2 bg-muted/50 rounded">
                    <span className="text-sm"><code>learn + fun</code></span>
                    <span className="text-xs text-muted-foreground">趣味学习</span>
                  </div>
                  <div className="flex items-center justify-between p-2 bg-muted/50 rounded">
                    <span className="text-sm"><code>draw + simple</code></span>
                    <span className="text-xs text-muted-foreground">简单绘画</span>
                  </div>
                  <div className="flex items-center justify-between p-2 bg-muted/50 rounded">
                    <span className="text-sm"><code>story + bedtime</code></span>
                    <span className="text-xs text-muted-foreground">睡前故事</span>
                  </div>
                </div>
              </div>

              {/* 青少年 (13-17岁) */}
              <div className="bg-card rounded-lg p-6 border">
                <h3 className="text-lg font-semibold text-foreground mb-4 flex items-center">
                  <span className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center text-lg mr-3">👦</span>
                  青少年
                </h3>
                <div className="space-y-3">
                  <div className="flex items-center justify-between p-2 bg-muted/50 rounded">
                    <span className="text-sm"><code>homework + help</code></span>
                    <span className="text-xs text-muted-foreground">作业辅助</span>
                  </div>
                  <div className="flex items-center justify-between p-2 bg-muted/50 rounded">
                    <span className="text-sm"><code>friend + chat</code></span>
                    <span className="text-xs text-muted-foreground">社交聊天</span>
                  </div>
                  <div className="flex items-center justify-between p-2 bg-muted/50 rounded">
                    <span className="text-sm"><code>music + create</code></span>
                    <span className="text-xs text-muted-foreground">音乐创作</span>
                  </div>
                  <div className="flex items-center justify-between p-2 bg-muted/50 rounded">
                    <span className="text-sm"><code>exam + study</code></span>
                    <span className="text-xs text-muted-foreground">考试复习</span>
                  </div>
                </div>
              </div>

              {/* 大学生 (18-22岁) */}
              <div className="bg-card rounded-lg p-6 border">
                <h3 className="text-lg font-semibold text-foreground mb-4 flex items-center">
                  <span className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center text-lg mr-3">🎓</span>
                  大学生
                </h3>
                <div className="space-y-3">
                  <div className="flex items-center justify-between p-2 bg-muted/50 rounded">
                    <span className="text-sm"><code>resume + builder</code></span>
                    <span className="text-xs text-muted-foreground">简历制作</span>
                  </div>
                  <div className="flex items-center justify-between p-2 bg-muted/50 rounded">
                    <span className="text-sm"><code>essay + writer</code></span>
                    <span className="text-xs text-muted-foreground">论文写作</span>
                  </div>
                  <div className="flex items-center justify-between p-2 bg-muted/50 rounded">
                    <span className="text-sm"><code>budget + student</code></span>
                    <span className="text-xs text-muted-foreground">学生理财</span>
                  </div>
                  <div className="flex items-center justify-between p-2 bg-muted/50 rounded">
                    <span className="text-sm"><code>internship + finder</code></span>
                    <span className="text-xs text-muted-foreground">实习查找</span>
                  </div>
                </div>
              </div>

              {/* 职场新人 (23-30岁) */}
              <div className="bg-card rounded-lg p-6 border">
                <h3 className="text-lg font-semibold text-foreground mb-4 flex items-center">
                  <span className="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center text-lg mr-3">💼</span>
                  职场新人
                </h3>
                <div className="space-y-3">
                  <div className="flex items-center justify-between p-2 bg-muted/50 rounded">
                    <span className="text-sm"><code>email + professional</code></span>
                    <span className="text-xs text-muted-foreground">职业邮件</span>
                  </div>
                  <div className="flex items-center justify-between p-2 bg-muted/50 rounded">
                    <span className="text-sm"><code>skill + development</code></span>
                    <span className="text-xs text-muted-foreground">技能发展</span>
                  </div>
                  <div className="flex items-center justify-between p-2 bg-muted/50 rounded">
                    <span className="text-sm"><code>networking + career</code></span>
                    <span className="text-xs text-muted-foreground">职业社交</span>
                  </div>
                  <div className="flex items-center justify-between p-2 bg-muted/50 rounded">
                    <span className="text-sm"><code>apartment + budget</code></span>
                    <span className="text-xs text-muted-foreground">租房预算</span>
                  </div>
                </div>
              </div>

              {/* 职场精英 (31-45岁) */}
              <div className="bg-card rounded-lg p-6 border">
                <h3 className="text-lg font-semibold text-foreground mb-4 flex items-center">
                  <span className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center text-lg mr-3">👔</span>
                  职场精英
                </h3>
                <div className="space-y-3">
                  <div className="flex items-center justify-between p-2 bg-muted/50 rounded">
                    <span className="text-sm"><code>leadership + training</code></span>
                    <span className="text-xs text-muted-foreground">领导力培训</span>
                  </div>
                  <div className="flex items-center justify-between p-2 bg-muted/50 rounded">
                    <span className="text-sm"><code>investment + portfolio</code></span>
                    <span className="text-xs text-muted-foreground">投资组合</span>
                  </div>
                  <div className="flex items-center justify-between p-2 bg-muted/50 rounded">
                    <span className="text-sm"><code>family + schedule</code></span>
                    <span className="text-xs text-muted-foreground">家庭日程</span>
                  </div>
                  <div className="flex items-center justify-between p-2 bg-muted/50 rounded">
                    <span className="text-sm"><code>health + executive</code></span>
                    <span className="text-xs text-muted-foreground">高管健康</span>
                  </div>
                </div>
              </div>

              {/* 银发族 (60岁+) */}
              <div className="bg-card rounded-lg p-6 border">
                <h3 className="text-lg font-semibold text-foreground mb-4 flex items-center">
                  <span className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center text-lg mr-3">👴</span>
                  银发族
                </h3>
                <div className="space-y-3">
                  <div className="flex items-center justify-between p-2 bg-muted/50 rounded">
                    <span className="text-sm"><code>health + senior</code></span>
                    <span className="text-xs text-muted-foreground">老年健康</span>
                  </div>
                  <div className="flex items-center justify-between p-2 bg-muted/50 rounded">
                    <span className="text-sm"><code>technology + simple</code></span>
                    <span className="text-xs text-muted-foreground">简化科技</span>
                  </div>
                  <div className="flex items-center justify-between p-2 bg-muted/50 rounded">
                    <span className="text-sm"><code>grandchildren + activities</code></span>
                    <span className="text-xs text-muted-foreground">隔代活动</span>
                  </div>
                  <div className="flex items-center justify-between p-2 bg-muted/50 rounded">
                    <span className="text-sm"><code>memory + exercise</code></span>
                    <span className="text-xs text-muted-foreground">记忆训练</span>
                  </div>
                </div>
              </div>
            </div>

            {/* 使用提示 */}
            <div className="mt-8 p-6 bg-blue-50 dark:bg-blue-950/20 rounded-lg border border-blue-200 dark:border-blue-800">
              <h3 className="text-lg font-semibold text-blue-900 dark:text-blue-100 mb-3">💡 使用提示</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-blue-800 dark:text-blue-200">
                <div>
                  <strong>组合策略：</strong>
                  <ul className="mt-1 space-y-1 list-disc list-inside">
                    <li>核心需求 + 目标用户</li>
                    <li>功能词根 + 场景词根</li>
                    <li>问题词根 + 解决方案</li>
                  </ul>
                </div>
                <div>
                  <strong>趋势分析：</strong>
                  <ul className="mt-1 space-y-1 list-disc list-inside">
                    <li>查看7天短期趋势</li>
                    <li>对比gpts基准热度</li>
                    <li>关注飙升关键词</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 浮动组合显示 */}
      {selectedKeywords.length > 0 && (
        <div className="fixed bottom-6 right-6 bg-white dark:bg-gray-800 rounded-lg shadow-lg border p-4 max-w-sm z-50">
          <div className="flex items-center justify-between mb-2">
            <h4 className="font-semibold text-sm">当前组合</h4>
            <Button
              size="sm"
              variant="ghost"
              onClick={() => setSelectedKeywords([])}
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
          <div className="flex flex-wrap gap-1 mb-3">
            {selectedKeywords.map((keyword) => (
              <Badge key={keyword} variant="secondary" className="text-xs">
                {keyword}
              </Badge>
            ))}
          </div>
          <Button
            size="sm"
            className="w-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700"
            asChild
          >
            <a
              href={generateTrendsUrl(selectedKeywords)}
              target="_blank"
              rel="noopener noreferrer"
            >
              <TrendingUp className="h-3 w-3 mr-1 text-white" />
              <span className="text-white">查看趋势</span>
            </a>
          </Button>
        </div>
      )}

      {/* 使用统一前台底部组件 */}
      <FrontendFooter />
    </div>
  )
}
