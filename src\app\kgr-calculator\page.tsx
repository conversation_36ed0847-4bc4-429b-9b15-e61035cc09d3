'use client'

import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card'
import { Badge } from '../../components/ui/badge'
import { Button } from '../../components/ui/button'
import { Input } from '../../components/ui/input'
import { Label } from '../../components/ui/label'
import { Textarea } from '../../components/ui/textarea'
import { Calculator, TrendingUp, Search, Target, AlertCircle, CheckCircle, XCircle, Download, Plus, Trash2, FileText } from 'lucide-react'
import FrontendHeader from '../../components/frontend-header'
import FrontendFooter from '../../components/frontend-footer'

interface KGRItem {
  keyword: string
  allintitle: number
  searchVolume: number
  kgr: number
  level: 'easy' | 'medium' | 'hard'
  description: string
}

export default function KGRCalculatorPage() {
  // 单位换算函数
  const parseNumberWithUnit = (input: string): { value: number; explanation: string; hasUnit: boolean } => {
    if (!input.trim()) return { value: 0, explanation: '', hasUnit: false }

    const cleanInput = input.trim().toLowerCase()
    const numberMatch = cleanInput.match(/^(\d*\.?\d+)([kmb]?)$/)

    if (!numberMatch) {
      // 如果不匹配格式，尝试解析为普通数字
      const plainNumber = parseFloat(cleanInput.replace(/,/g, ''))
      if (!isNaN(plainNumber)) {
        return {
          value: plainNumber,
          explanation: '',
          hasUnit: false
        }
      }
      return { value: 0, explanation: '格式错误', hasUnit: false }
    }

    const [, numberStr, unit] = numberMatch
    const baseNumber = parseFloat(numberStr)

    if (isNaN(baseNumber)) return { value: 0, explanation: '数字格式错误', hasUnit: false }

    let multiplier = 1
    let unitName = ''
    let hasUnit = false

    switch (unit) {
      case 'k':
        multiplier = 1000
        unitName = '千'
        hasUnit = true
        break
      case 'm':
        multiplier = 1000000
        unitName = '百万'
        hasUnit = true
        break
      case 'b':
        multiplier = 1000000000
        unitName = '十亿'
        hasUnit = true
        break
      default:
        multiplier = 1
        unitName = ''
        hasUnit = false
    }

    const actualValue = baseNumber * multiplier
    const explanation = hasUnit
      ? `${baseNumber}${unitName} = ${actualValue.toLocaleString()} 次`
      : ''

    return { value: actualValue, explanation, hasUnit }
  }

  // 单个计算状态
  const [keyword, setKeyword] = useState<string>('')
  const [allintitleResults, setAllintitleResults] = useState<string>('')
  const [searchVolume, setSearchVolume] = useState<string>('')
  const [allintitleParsed, setAllintitleParsed] = useState<{ value: number; explanation: string; hasUnit: boolean }>({ value: 0, explanation: '', hasUnit: false })
  const [searchVolumeParsed, setSearchVolumeParsed] = useState<{ value: number; explanation: string; hasUnit: boolean }>({ value: 0, explanation: '', hasUnit: false })
  const [kgrResult, setKgrResult] = useState<{
    value: number
    level: 'easy' | 'medium' | 'hard'
    description: string
    color: string
    warnings: string[]
  } | null>(null)

  // 批量计算状态
  const [batchMode, setBatchMode] = useState<boolean>(false)
  const [batchInput, setBatchInput] = useState<string>('')
  const [batchResults, setBatchResults] = useState<KGRItem[]>([])

  // 获取KGR等级信息和警告
  const getKGRLevel = (kgr: number, searchVolume: number, allintitle: number) => {
    const warnings: string[] = []

    // 数据合理性检查
    if (searchVolume < 50) {
      warnings.push('⚠️ 月搜索量过低（<50），此关键词可能没有优化价值')
    }

    if (searchVolume < 10) {
      warnings.push('💡 请确认搜索量数据准确性，过低的搜索量可能表明关键词拼写错误或工具数据不准确')
    }

    if (kgr > 10) {
      warnings.push('🔍 KGR值异常高，建议检查输入数据是否正确')
    }

    if (allintitle < 10 && searchVolume < 100) {
      warnings.push('📊 竞争和搜索量都很低，建议寻找更有价值的关键词')
    }

    let level: 'easy' | 'medium' | 'hard'
    let description: string
    let color: string

    if (kgr < 0.25) {
      level = 'easy'
      description = '极易排名 - 新网站应优先选择此类关键词'
      color = 'text-green-600'
    } else if (kgr <= 1) {
      level = 'medium'
      description = '中等竞争 - 需要一定努力才能获得排名'
      color = 'text-yellow-600'
    } else {
      level = 'hard'
      description = '高度竞争 - 新网站很难在此关键词上竞争'
      color = 'text-red-600'
    }

    return { level, description, color, warnings }
  }

  // 单个计算KGR
  const calculateKGR = () => {
    // 解析输入的数值
    const allintitleData = parseNumberWithUnit(allintitleResults)
    const volumeData = parseNumberWithUnit(searchVolume)

    setAllintitleParsed(allintitleData)
    setSearchVolumeParsed(volumeData)

    if (!keyword.trim()) {
      alert('请输入关键词')
      return
    }

    if (volumeData.value <= 0) {
      alert('请输入有效的搜索量')
      return
    }

    if (allintitleData.value < 0) {
      alert('请输入有效的allintitle结果数量')
      return
    }

    const kgr = allintitleData.value / volumeData.value
    const levelInfo = getKGRLevel(kgr, volumeData.value, allintitleData.value)

    setKgrResult({
      value: kgr,
      level: levelInfo.level,
      description: levelInfo.description,
      color: levelInfo.color,
      warnings: levelInfo.warnings
    })
  }

  // 批量计算KGR
  const calculateBatchKGR = () => {
    if (!batchInput.trim()) {
      alert('请输入批量数据')
      return
    }

    const lines = batchInput.trim().split('\n')
    const results: KGRItem[] = []

    for (const line of lines) {
      const parts = line.split(',').map(part => part.trim())
      if (parts.length !== 3) continue

      const [keyword, allintitleStr, volumeStr] = parts
      const allintitleData = parseNumberWithUnit(allintitleStr)
      const volumeData = parseNumberWithUnit(volumeStr)

      if (volumeData.value <= 0 || allintitleData.value < 0) continue

      const kgr = allintitleData.value / volumeData.value
      const levelInfo = getKGRLevel(kgr, volumeData.value, allintitleData.value)

      results.push({
        keyword,
        allintitle: allintitleData.value,
        searchVolume: volumeData.value,
        kgr,
        level: levelInfo.level,
        description: levelInfo.description
      })
    }

    setBatchResults(results)
  }

  // 导出CSV
  const exportToCSV = () => {
    if (batchResults.length === 0) {
      alert('没有数据可导出')
      return
    }

    const now = new Date()
    const timestamp = now.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })

    // CSV头部信息
    const header = [
      '# KGR关键词黄金比例计算结果',
      `# 导出时间: ${timestamp}`,
      '# 数据来源: CatchIdeas.com - 专业的关键词分析工具',
      '# 网站地址: https://catchideas.com',
      '# KGR公式: allintitle结果数量 ÷ 月搜索量',
      '# KGR < 0.25: 极易排名 | 0.25-1: 中等竞争 | > 1: 高度竞争',
      '',
      '关键词,allintitle结果数量,月搜索量,KGR值,竞争程度,建议'
    ].join('\n')

    // CSV数据行
    const csvData = batchResults.map(item => {
      const suggestion = item.level === 'easy' ? '立即创建内容' :
                        item.level === 'medium' ? '需要优质内容' : '寻找替代关键词'
      return [
        item.keyword,
        item.allintitle,
        item.searchVolume,
        item.kgr.toFixed(4),
        item.level === 'easy' ? '极易排名' : item.level === 'medium' ? '中等竞争' : '高度竞争',
        suggestion
      ].join(',')
    }).join('\n')

    const csvContent = header + '\n' + csvData

    // 创建下载链接
    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', `KGR分析结果_${now.getFullYear()}${(now.getMonth()+1).toString().padStart(2,'0')}${now.getDate().toString().padStart(2,'0')}.csv`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  // 重置计算器
  const resetCalculator = () => {
    setKeyword('')
    setAllintitleResults('')
    setSearchVolume('')
    setAllintitleParsed({ value: 0, explanation: '', hasUnit: false })
    setSearchVolumeParsed({ value: 0, explanation: '', hasUnit: false })
    setKgrResult(null)
    setBatchInput('')
    setBatchResults([])
  }

  // 切换计算模式
  const toggleMode = (mode: boolean) => {
    setBatchMode(mode)
    resetCalculator()
  }

  return (
    <div className="min-h-screen bg-background">
      {/* 使用统一前台顶部组件 */}
      <FrontendHeader />

      {/* 页面标题区域 */}
      <section className="py-6 px-4 bg-background border-b">
        <div className="container mx-auto">
          <div className="max-w-4xl mx-auto text-center">
            <div className="bg-card rounded-xl shadow-sm border p-6">
              <div className="flex items-center justify-center mb-4">
                <div className="p-3 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg mr-3">
                  <Calculator className="h-8 w-8 text-white" />
                </div>
                <h1 className="text-3xl font-bold text-foreground">KGR 关键词黄金比例计算器</h1>
              </div>
              <p className="text-muted-foreground text-lg">
                基于数据驱动的SEO策略，快速识别易排名关键词，提升搜索排名效率
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* 模式切换区域 */}
      <section className="py-4 px-4 bg-gray-50">
        <div className="container mx-auto">
          <div className="max-w-4xl mx-auto">
            <div className="flex justify-center gap-2">
              <Button
                variant={!batchMode ? "default" : "outline"}
                onClick={() => toggleMode(false)}
                className={!batchMode
                  ? "bg-gradient-to-r from-blue-500 to-purple-600 text-white hover:from-blue-600 hover:to-purple-700 border-0"
                  : "bg-white/80 backdrop-blur-sm border-gray-200 text-gray-700 hover:bg-blue-50 hover:text-blue-600 hover:border-blue-300"
                }
              >
                <Calculator className="h-4 w-4 mr-2" />
                单个计算
              </Button>
              <Button
                variant={batchMode ? "default" : "outline"}
                onClick={() => toggleMode(true)}
                className={batchMode
                  ? "bg-gradient-to-r from-green-500 to-teal-600 text-white hover:from-green-600 hover:to-teal-700 border-0"
                  : "bg-white/80 backdrop-blur-sm border-gray-200 text-gray-700 hover:bg-green-50 hover:text-green-600 hover:border-green-300"
                }
              >
                <FileText className="h-4 w-4 mr-2" />
                批量计算
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* 计算器主体区域 */}
      <section className="py-8 px-4">
        <div className="container mx-auto">
          <div className="max-w-6xl mx-auto">
            {!batchMode ? (
              // 单个计算模式
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              
              {/* 左侧：输入区域 */}
              <Card className="shadow-lg border-2 border-blue-100">
                <CardHeader className="bg-gradient-to-r from-blue-50 to-purple-50">
                  <CardTitle className="flex items-center text-xl">
                    <Target className="h-5 w-5 mr-2 text-blue-600" />
                    输入关键词数据
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-6 space-y-6">

                  {/* 关键词输入 */}
                  <div className="space-y-2">
                    <Label htmlFor="keyword" className="text-sm font-semibold flex items-center">
                      <Target className="h-4 w-4 mr-2 text-purple-500" />
                      目标关键词
                    </Label>
                    <Input
                      id="keyword"
                      type="text"
                      placeholder="例如：AI写作工具"
                      value={keyword}
                      onChange={(e) => setKeyword(e.target.value)}
                      className="h-12 text-lg border-2 border-purple-200 focus:border-purple-500"
                    />
                    {keyword.trim() && (
                      <div className="flex items-center gap-2 mt-2">
                        <span className="text-xs text-muted-foreground">快速搜索：</span>
                        <a
                          href={`https://www.google.com/search?q=allintitle:${encodeURIComponent(keyword.trim())}`}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="inline-flex items-center px-3 py-1 text-xs bg-blue-100 hover:bg-blue-200 text-blue-700 rounded-full transition-colors"
                        >
                          <Search className="h-3 w-3 mr-1" />
                          allintitle:{keyword.trim()}
                        </a>
                      </div>
                    )}
                  </div>

                  {/* allintitle结果数量 */}
                  <div className="space-y-2">
                    <Label htmlFor="allintitle" className="text-sm font-semibold flex items-center">
                      <Search className="h-4 w-4 mr-2 text-blue-500" />
                      allintitle 搜索结果数量
                    </Label>
                    <Input
                      id="allintitle"
                      type="text"
                      placeholder="例如：1500 或 1.5K 或 0.02M"
                      value={allintitleResults}
                      onChange={(e) => {
                        setAllintitleResults(e.target.value)
                        const parsed = parseNumberWithUnit(e.target.value)
                        setAllintitleParsed(parsed)
                      }}
                      className="h-12 text-lg border-2 border-blue-200 focus:border-blue-500"
                    />
                    <div className="space-y-1">
                      <p className="text-xs text-muted-foreground">
                        支持格式：普通数字(1500)、K(1.5K)、M(0.02M)、B(0.001B)
                      </p>
                      {allintitleParsed.hasUnit && allintitleParsed.explanation && (
                        <div className="bg-blue-50 rounded px-2 py-1 border border-blue-200">
                          <p className="text-xs text-blue-700">
                            📊 换算结果：{allintitleParsed.explanation}
                          </p>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* 搜索量 */}
                  <div className="space-y-2">
                    <Label htmlFor="volume" className="text-sm font-semibold flex items-center">
                      <TrendingUp className="h-4 w-4 mr-2 text-green-500" />
                      月搜索量
                    </Label>
                    <Input
                      id="volume"
                      type="text"
                      placeholder="例如：5000 或 5K 或 85.9K 或 1.2M"
                      value={searchVolume}
                      onChange={(e) => {
                        setSearchVolume(e.target.value)
                        const parsed = parseNumberWithUnit(e.target.value)
                        setSearchVolumeParsed(parsed)
                      }}
                      className="h-12 text-lg border-2 border-green-200 focus:border-green-500"
                    />
                    <div className="space-y-1">
                      <p className="text-xs text-muted-foreground">
                        使用Keywords Everywhere工具获取，支持K/M/B单位
                      </p>
                      {searchVolumeParsed.hasUnit && searchVolumeParsed.explanation && (
                        <div className="bg-green-50 rounded px-2 py-1 border border-green-200">
                          <p className="text-xs text-green-700">
                            📊 换算结果：{searchVolumeParsed.explanation}
                          </p>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* 操作按钮 */}
                  <div className="flex gap-3 pt-4">
                    <Button 
                      onClick={calculateKGR}
                      className="flex-1 h-12 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white font-semibold"
                    >
                      <Calculator className="h-4 w-4 mr-2" />
                      计算 KGR
                    </Button>
                    <Button 
                      onClick={resetCalculator}
                      variant="outline"
                      className="h-12 border-2 border-gray-300 hover:border-gray-500"
                    >
                      重置
                    </Button>
                  </div>
                </CardContent>
              </Card>

              {/* 右侧：结果区域 */}
              <Card className="shadow-lg border-2 border-green-100">
                <CardHeader className="bg-gradient-to-r from-green-50 to-blue-50">
                  <CardTitle className="flex items-center text-xl">
                    <Calculator className="h-5 w-5 mr-2 text-green-600" />
                    KGR 计算结果
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-6">
                  {kgrResult ? (
                    <div className="space-y-6">
                      
                      {/* KGR值 */}
                      <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-6 border-2 border-blue-200 text-center">
                        <div className="mb-2">
                          <span className="text-sm font-semibold text-gray-600">KGR 值</span>
                        </div>
                        <div className="text-4xl font-bold text-blue-600 mb-2">
                          {kgrResult.value.toFixed(4)}
                        </div>
                        <div className="text-sm text-gray-500 space-y-1">
                          <div>计算公式：{allintitleResults} ÷ {searchVolume}</div>
                          {(allintitleParsed.hasUnit || searchVolumeParsed.hasUnit) && (
                            <div className="bg-white/70 rounded px-2 py-1 text-xs">
                              <div>实际数值：{allintitleParsed.value?.toLocaleString()} ÷ {searchVolumeParsed.value?.toLocaleString()}</div>
                              {allintitleParsed.hasUnit && allintitleParsed.explanation && (
                                <div className="text-blue-600">allintitle: {allintitleParsed.explanation}</div>
                              )}
                              {searchVolumeParsed.hasUnit && searchVolumeParsed.explanation && (
                                <div className="text-green-600">搜索量: {searchVolumeParsed.explanation}</div>
                              )}
                            </div>
                          )}
                        </div>
                      </div>

                      {/* 竞争程度 */}
                      <div className={`rounded-lg p-4 border-2 ${
                        kgrResult.level === 'easy' ? 'bg-green-50 border-green-200' :
                        kgrResult.level === 'medium' ? 'bg-yellow-50 border-yellow-200' :
                        'bg-red-50 border-red-200'
                      }`}>
                        <div className="flex items-center mb-2">
                          {kgrResult.level === 'easy' ? (
                            <CheckCircle className="h-5 w-5 text-green-600 mr-2" />
                          ) : kgrResult.level === 'medium' ? (
                            <AlertCircle className="h-5 w-5 text-yellow-600 mr-2" />
                          ) : (
                            <XCircle className="h-5 w-5 text-red-600 mr-2" />
                          )}
                          <span className="font-semibold text-gray-800">竞争程度</span>
                        </div>
                        <p className={`${kgrResult.color} font-medium`}>
                          {kgrResult.description}
                        </p>
                      </div>

                      {/* 数据质量警告 */}
                      {kgrResult.warnings && kgrResult.warnings.length > 0 && (
                        <div className="bg-orange-50 rounded-lg p-4 border-2 border-orange-200">
                          <div className="flex items-center mb-2">
                            <AlertCircle className="h-5 w-5 text-orange-600 mr-2" />
                            <span className="font-semibold text-orange-800">数据质量提醒</span>
                          </div>
                          <div className="space-y-1">
                            {kgrResult.warnings.map((warning, index) => (
                              <p key={index} className="text-orange-700 text-sm">
                                {warning}
                              </p>
                            ))}
                          </div>
                        </div>
                      )}

                      {/* 建议策略 */}
                      <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                        <h4 className="font-semibold text-gray-800 mb-3">SEO 策略建议</h4>
                        <div className="space-y-2 text-sm">
                          {kgrResult.level === 'easy' && (
                            <div className="space-y-1">
                              <p className="text-green-700">✅ 立即创建针对此关键词的内容</p>
                              <p className="text-green-700">✅ 优化页面标题包含完整关键词</p>
                              <p className="text-green-700">✅ 预期在较短时间内获得排名</p>
                            </div>
                          )}
                          {kgrResult.level === 'medium' && (
                            <div className="space-y-1">
                              <p className="text-yellow-700">⚠️ 需要高质量内容和适当的SEO优化</p>
                              <p className="text-yellow-700">⚠️ 建议结合长尾关键词策略</p>
                              <p className="text-yellow-700">⚠️ 预期需要中等时间获得排名</p>
                            </div>
                          )}
                          {kgrResult.level === 'hard' && (
                            <div className="space-y-1">
                              <p className="text-red-700">❌ 新网站不建议直接竞争此关键词</p>
                              <p className="text-red-700">❌ 考虑寻找相关的长尾关键词</p>
                              <p className="text-red-700">❌ 需要强大的域名权威度才能竞争</p>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="text-center py-12">
                      <Calculator className="h-16 w-16 mx-auto text-gray-400 mb-4" />
                      <h3 className="text-xl font-semibold text-foreground mb-2">等待计算</h3>
                      <p className="text-muted-foreground">
                        请输入allintitle结果数量和搜索量，然后点击"计算KGR"按钮
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
            ) : (
              // 批量计算模式
              <div className="space-y-8">
                {/* 批量输入区域 */}
                <Card className="shadow-lg border-2 border-green-100">
                  <CardHeader className="bg-gradient-to-r from-green-50 to-teal-50">
                    <CardTitle className="flex items-center text-xl">
                      <FileText className="h-5 w-5 mr-2 text-green-600" />
                      批量输入数据
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="p-6">
                    <div className="space-y-4">
                      <div>
                        <Label htmlFor="batchInput" className="text-sm font-semibold text-green-800 mb-2 block">
                          输入格式：关键词,allintitle结果数量,月搜索量（每行一个）
                        </Label>
                        <Textarea
                          id="batchInput"
                          placeholder={`AI写作工具,1.2K,8K\nPDF转换器,2.5K,12K\n背景移除工具,800,5K\n视频编辑器,0.5M,85.9K\n代码生成器,1.2M,1.2M`}
                          value={batchInput}
                          onChange={(e) => setBatchInput(e.target.value)}
                          className="min-h-[200px] text-sm border-2 border-green-200 focus:border-green-500"
                        />
                        <div className="mt-2 space-y-2">
                          <p className="text-xs text-muted-foreground">
                            每行格式：关键词,allintitle结果数量,月搜索量。支持批量粘贴Excel数据。
                          </p>
                          <div className="bg-green-50 rounded-lg p-3 border border-green-200">
                            <p className="text-xs font-semibold text-green-800 mb-2">📊 支持的数值格式：</p>
                            <div className="grid grid-cols-2 gap-2 text-xs text-green-700">
                              <div>• 普通数字：1500, 8000</div>
                              <div>• K(千)：1.5K, 85.9K</div>
                              <div>• M(百万)：1.2M, 0.02M</div>
                              <div>• B(十亿)：3.9B, 0.001B</div>
                            </div>
                            <p className="text-xs text-green-600 mt-2">
                              💡 示例：12.5K = 12,500 | 1.2M = 1,200,000 | 0.85K = 850
                            </p>
                          </div>
                        </div>
                      </div>

                      <div className="flex gap-3">
                        <Button
                          onClick={calculateBatchKGR}
                          className="flex-1 h-12 bg-gradient-to-r from-green-500 to-teal-600 hover:from-green-600 hover:to-teal-700 text-white font-semibold"
                        >
                          <Calculator className="h-4 w-4 mr-2" />
                          批量计算 KGR
                        </Button>
                        <Button
                          onClick={exportToCSV}
                          disabled={batchResults.length === 0}
                          className="h-12 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white font-semibold disabled:opacity-50"
                        >
                          <Download className="h-4 w-4 mr-2" />
                          导出 CSV
                        </Button>
                        <Button
                          onClick={resetCalculator}
                          variant="outline"
                          className="h-12 border-2 border-gray-300 hover:border-gray-500"
                        >
                          重置
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* 批量结果区域 */}
                {batchResults.length > 0 && (
                  <Card className="shadow-lg border-2 border-blue-100">
                    <CardHeader className="bg-gradient-to-r from-blue-50 to-purple-50">
                      <CardTitle className="flex items-center text-xl justify-between">
                        <div className="flex items-center">
                          <Target className="h-5 w-5 mr-2 text-blue-600" />
                          批量计算结果
                        </div>
                        <Badge className="bg-blue-100 text-blue-700">
                          {batchResults.length} 个关键词
                        </Badge>
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="p-6">
                      <div className="overflow-x-auto">
                        <table className="w-full border-collapse border border-gray-300">
                          <thead>
                            <tr className="bg-gray-100">
                              <th className="border border-gray-300 px-4 py-2 text-left font-semibold">关键词</th>
                              <th className="border border-gray-300 px-4 py-2 text-left font-semibold">allintitle</th>
                              <th className="border border-gray-300 px-4 py-2 text-left font-semibold">搜索量</th>
                              <th className="border border-gray-300 px-4 py-2 text-left font-semibold">KGR值</th>
                              <th className="border border-gray-300 px-4 py-2 text-left font-semibold">竞争程度</th>
                            </tr>
                          </thead>
                          <tbody>
                            {batchResults.map((item, index) => (
                              <tr key={index} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                                <td className="border border-gray-300 px-4 py-2 font-medium">{item.keyword}</td>
                                <td className="border border-gray-300 px-4 py-2">{item.allintitle.toLocaleString()}</td>
                                <td className="border border-gray-300 px-4 py-2">{item.searchVolume.toLocaleString()}</td>
                                <td className="border border-gray-300 px-4 py-2 font-mono">{item.kgr.toFixed(4)}</td>
                                <td className="border border-gray-300 px-4 py-2">
                                  <Badge className={
                                    item.level === 'easy' ? 'bg-green-100 text-green-800' :
                                    item.level === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                                    'bg-red-100 text-red-800'
                                  }>
                                    {item.level === 'easy' ? '极易排名' :
                                     item.level === 'medium' ? '中等竞争' : '高度竞争'}
                                  </Badge>
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </CardContent>
                  </Card>
                )}
              </div>
            )}
          </div>
        </div>
      </section>

      {/* KGR公式说明区域 */}
      <section className="py-8 px-4 bg-gray-50">
        <div className="container mx-auto">
          <div className="max-w-4xl mx-auto">
            <Card className="shadow-lg">
              <CardHeader className="bg-gradient-to-r from-blue-500 to-purple-600 text-white">
                <CardTitle className="text-2xl flex items-center">
                  <Target className="h-6 w-6 mr-3" />
                  KGR 关键词黄金比例详解
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6">

                {/* 基础公式 */}
                <div className="mb-8">
                  <h3 className="text-xl font-bold text-foreground mb-4">核心公式</h3>
                  <div className="bg-blue-50 rounded-lg p-6 border-l-4 border-blue-500 mb-4">
                    <p className="font-semibold text-blue-800 mb-3 text-center text-lg">KGR 公式：</p>
                    <div className="text-center">
                      <code className="text-2xl bg-white px-6 py-3 rounded border font-bold text-blue-600">
                        KGR = allintitle 搜索结果数量 ÷ 月搜索量
                      </code>
                    </div>
                  </div>
                  <div className="bg-green-50 rounded-lg p-4 border-l-4 border-green-500">
                    <p className="font-semibold text-green-800 mb-2">什么是 allintitle？</p>
                    <p className="text-green-700 text-sm mb-2">
                      allintitle 是Google搜索运算符，用于查找标题中包含所有指定关键词的页面。
                    </p>
                    <p className="text-green-700 text-sm">
                      使用方法：在Google中搜索 <code className="bg-white px-2 py-1 rounded">allintitle:你的关键词</code>
                    </p>
                  </div>
                </div>

                {/* 单位换算说明 */}
                <div className="mb-8">
                  <h3 className="text-xl font-bold text-foreground mb-4">🔢 单位含义与换算</h3>
                  <div className="bg-blue-50 rounded-lg p-6 border border-blue-200">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                      <div className="text-center">
                        <div className="font-bold text-blue-800 text-lg">K</div>
                        <div className="text-sm text-blue-600">Thousand (千)</div>
                        <div className="text-xs text-blue-500">1K = 1,000</div>
                      </div>
                      <div className="text-center">
                        <div className="font-bold text-blue-800 text-lg">M</div>
                        <div className="text-sm text-blue-600">Million (百万)</div>
                        <div className="text-xs text-blue-500">1M = 1,000,000</div>
                      </div>
                      <div className="text-center">
                        <div className="font-bold text-blue-800 text-lg">B</div>
                        <div className="text-sm text-blue-600">Billion (十亿)</div>
                        <div className="text-xs text-blue-500">1B = 1,000,000,000</div>
                      </div>
                    </div>

                    <div className="bg-white rounded-lg p-4 border border-blue-300">
                      <h4 className="font-semibold text-blue-800 mb-3">🎯 示例换算说明：</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                        <div className="space-y-1">
                          <div><span className="font-mono bg-blue-100 px-1 rounded">85.90K</span> → 每月约 <span className="font-semibold">85,900</span> 次搜索流量</div>
                          <div><span className="font-mono bg-blue-100 px-1 rounded">1.2M</span> → 约 <span className="font-semibold">1,200,000</span> 次</div>
                          <div><span className="font-mono bg-blue-100 px-1 rounded">0.85K</span> → 约 <span className="font-semibold">850</span> 次</div>
                        </div>
                        <div className="space-y-1">
                          <div><span className="font-mono bg-blue-100 px-1 rounded">812.99M</span> → 约 <span className="font-semibold">812,990,000</span> 次（8.13亿）</div>
                          <div><span className="font-mono bg-blue-100 px-1 rounded">3.9B</span> → 约 <span className="font-semibold">3,900,000,000</span> 条反向链接</div>
                          <div><span className="font-mono bg-blue-100 px-1 rounded">0.02M</span> → 约 <span className="font-semibold">20,000</span> 次</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* KGR数值解读 */}
                <div className="mb-8">
                  <h3 className="text-xl font-bold text-foreground mb-4">KGR 数值解读</h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="bg-green-50 rounded-lg p-4 border-2 border-green-200">
                      <div className="flex items-center mb-3">
                        <CheckCircle className="h-6 w-6 text-green-600 mr-2" />
                        <h4 className="font-bold text-green-800">KGR &lt; 0.25</h4>
                      </div>
                      <p className="text-green-700 text-sm mb-2 font-semibold">极易排名</p>
                      <p className="text-green-600 text-sm">新网站应优先选择此类关键词，通常能在较短时间内获得良好排名。</p>
                    </div>

                    <div className="bg-yellow-50 rounded-lg p-4 border-2 border-yellow-200">
                      <div className="flex items-center mb-3">
                        <AlertCircle className="h-6 w-6 text-yellow-600 mr-2" />
                        <h4 className="font-bold text-yellow-800">0.25 ≤ KGR ≤ 1</h4>
                      </div>
                      <p className="text-yellow-700 text-sm mb-2 font-semibold">中等竞争</p>
                      <p className="text-yellow-600 text-sm">需要优质内容和适当的SEO优化策略才能获得排名。</p>
                    </div>

                    <div className="bg-red-50 rounded-lg p-4 border-2 border-red-200">
                      <div className="flex items-center mb-3">
                        <XCircle className="h-6 w-6 text-red-600 mr-2" />
                        <h4 className="font-bold text-red-800">KGR &gt; 1</h4>
                      </div>
                      <p className="text-red-700 text-sm mb-2 font-semibold">高度竞争</p>
                      <p className="text-red-600 text-sm">新网站很难在此类关键词上竞争，建议寻找替代关键词。</p>
                    </div>
                  </div>
                </div>

                {/* 实例演示 */}
                <div className="mb-8">
                  <h3 className="text-xl font-bold text-foreground mb-4">计算实例</h3>
                  <div className="bg-yellow-50 rounded-lg p-6 border border-yellow-200">
                    <p className="font-semibold text-yellow-800 mb-4">示例：关键词 "AI写作工具"</p>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="bg-white rounded-lg p-4 border">
                        <h4 className="font-semibold text-blue-800 mb-2">步骤1：获取数据</h4>
                        <ul className="text-sm text-gray-700 space-y-1">
                          <li>• 在Google搜索：allintitle:AI写作工具</li>
                          <li>• 结果数量：约 1,200 个</li>
                          <li>• 使用工具查询月搜索量：8,000</li>
                        </ul>
                      </div>
                      <div className="bg-white rounded-lg p-4 border">
                        <h4 className="font-semibold text-green-800 mb-2">步骤2：计算KGR</h4>
                        <div className="text-sm text-gray-700 space-y-1">
                          <p>KGR = 1,200 ÷ 8,000 = 0.15</p>
                          <p className="text-green-600 font-semibold">结论：KGR &lt; 0.25，极易排名！</p>
                          <p className="text-green-600">建议立即创建相关内容</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* 使用工具推荐 */}
                <div>
                  <h3 className="text-xl font-bold text-foreground mb-4">推荐工具</h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="bg-blue-50 rounded-lg p-4 border border-blue-200">
                      <h4 className="font-semibold text-blue-800 mb-2">搜索量查询</h4>
                      <ul className="text-sm text-blue-700 space-y-1">
                        <li>• Ubersuggest</li>
                        <li>• Keywords Everywhere</li>
                        <li>• Google Keyword Planner</li>
                        <li>• Ahrefs Keywords Explorer</li>
                      </ul>
                    </div>

                    <div className="bg-green-50 rounded-lg p-4 border border-green-200">
                      <h4 className="font-semibold text-green-800 mb-2">allintitle 查询</h4>
                      <ul className="text-sm text-green-700 space-y-1">
                        <li>• Google 搜索</li>
                        <li>• 直接在搜索框输入</li>
                        <li>• allintitle:关键词</li>
                        <li>• 查看结果数量</li>
                      </ul>
                    </div>

                    <div className="bg-purple-50 rounded-lg p-4 border border-purple-200">
                      <h4 className="font-semibold text-purple-800 mb-2">KGR 应用</h4>
                      <ul className="text-sm text-purple-700 space-y-1">
                        <li>• 内容规划</li>
                        <li>• 标题优化</li>
                        <li>• SEO策略制定</li>
                        <li>• 竞争分析</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* 使用统一前台底部组件 */}
      <FrontendFooter />
    </div>
  )
}
