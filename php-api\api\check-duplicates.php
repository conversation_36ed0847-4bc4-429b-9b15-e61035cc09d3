<?php
/**
 * 检查重复关键词API
 * 检查提供的关键词列表中哪些在数据库中不存在
 */

require_once '../config/database.php';
require_once '../models/Keyword.php';
require_once '../utils/Response.php';

// 设置CORS头
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');
header('Content-Type: application/json; charset=utf-8');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    Response::error('不支持的请求方法，请使用POST', 405);
}

try {
    // 获取请求数据
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        Response::validationError(['message' => '无效的JSON数据']);
    }
    
    // 验证关键词数组
    if (!isset($input['keywords']) || !is_array($input['keywords'])) {
        Response::validationError(['keywords' => '请提供有效的关键词数组']);
    }
    
    $keywords = $input['keywords'];
    
    if (empty($keywords)) {
        Response::validationError(['keywords' => '关键词数组不能为空']);
    }
    
    if (count($keywords) > 1000) {
        Response::validationError(['keywords' => '单次检查最多支持1000个关键词']);
    }
    
    // 过滤和清理关键词
    $cleanKeywords = [];
    foreach ($keywords as $keyword) {
        $cleaned = trim($keyword);
        if (!empty($cleaned) && strlen($cleaned) <= 255) {
            $cleanKeywords[] = $cleaned;
        }
    }
    
    if (empty($cleanKeywords)) {
        Response::validationError(['keywords' => '没有有效的关键词']);
    }
    
    // 连接数据库
    $database = new Database();
    $db = $database->getConnection();
    
    if (!$db) {
        Response::serverError('数据库连接失败');
    }
    
    $keywordModel = new Keyword($db);
    
    // 检查重复
    $duplicateCheck = $keywordModel->checkDuplicates($cleanKeywords);
    
    Response::success([
        'total_checked' => count($cleanKeywords),
        'existing_keywords' => $duplicateCheck['existing'],
        'new_keywords' => $duplicateCheck['new'],
        'existing_count' => count($duplicateCheck['existing']),
        'new_count' => count($duplicateCheck['new'])
    ], '重复检查完成');
    
} catch (Exception $e) {
    Response::serverError('检查重复关键词失败: ' . $e->getMessage());
}
?>
