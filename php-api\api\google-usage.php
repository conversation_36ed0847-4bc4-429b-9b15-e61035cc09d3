<?php
/**
 * Google API使用统计接口
 * 用于记录和查询Google搜索API的使用次数
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once '../config/database.php';

try {
    $database = new Database();
    $conn = $database->getConnection();
    
    $method = $_SERVER['REQUEST_METHOD'];
    
    switch ($method) {
        case 'GET':
            handleGetUsage($conn);
            break;
        case 'POST':
            handleRecordUsage($conn);
            break;
        case 'PUT':
            handleUpdateUsage($conn);
            break;
        default:
            http_response_code(405);
            echo json_encode([
                'success' => false,
                'error' => '不支持的请求方法'
            ]);
            break;
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => '服务器内部错误'
    ]);
}

/**
 * 获取API使用统计
 */
function handleGetUsage($conn) {
    try {
        $date = $_GET['date'] ?? date('Y-m-d');
        $keyIndex = $_GET['key_index'] ?? null;
        
        if ($keyIndex) {
            // 获取单个密钥的使用统计
            $query = "SELECT * FROM google_api_usage 
                     WHERE api_key_index = :key_index AND usage_date = :date";
            $stmt = $conn->prepare($query);
            $stmt->bindParam(':key_index', $keyIndex, PDO::PARAM_INT);
            $stmt->bindParam(':date', $date);
            $stmt->execute();
            
            $result = $stmt->fetch();
            
            echo json_encode([
                'success' => true,
                'data' => $result ?: [
                    'api_key_index' => (int)$keyIndex,
                    'usage_date' => $date,
                    'usage_count' => 0
                ]
            ]);
        } else {
            // 获取所有密钥的使用统计
            $query = "SELECT * FROM google_api_usage
                     WHERE usage_date = :date
                     ORDER BY api_key_index";
            $stmt = $conn->prepare($query);
            $stmt->bindParam(':date', $date);
            $stmt->execute();

            $results = $stmt->fetchAll();

            // 检查是否需要为新日期创建初始记录
            if (count($results) < 20) {
                // 自动为新日期创建20个初始记录
                initializeDailyRecords($conn, $date);

                // 重新查询获取完整记录
                $stmt->execute();
                $results = $stmt->fetchAll();
            }

            // 确保所有20个密钥都有记录（双重保险）
            $usage = [];
            for ($i = 1; $i <= 20; $i++) {
                $found = false;
                foreach ($results as $result) {
                    if ($result['api_key_index'] == $i) {
                        $usage[] = $result;
                        $found = true;
                        break;
                    }
                }
                if (!$found) {
                    // 如果仍然缺少记录，动态返回0值（不应该发生，但作为备用）
                    $usage[] = [
                        'api_key_index' => $i,
                        'usage_date' => $date,
                        'usage_count' => 0,
                        'created_at' => date('Y-m-d H:i:s'),
                        'updated_at' => date('Y-m-d H:i:s')
                    ];
                }
            }
            
            echo json_encode([
                'success' => true,
                'data' => $usage,
                'summary' => [
                    'date' => $date,
                    'total_usage' => array_sum(array_column($usage, 'usage_count')),
                    'active_keys' => count(array_filter($usage, function($u) { return $u['usage_count'] > 0; }))
                ]
            ]);
        }
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'error' => '查询失败'
        ]);
    }
}

/**
 * 记录API使用（增加使用次数）
 */
function handleRecordUsage($conn) {
    try {
        $input = json_decode(file_get_contents('php://input'), true);
        $keyIndex = $input['key_index'] ?? null;
        $count = $input['count'] ?? 1; // 支持自定义使用次数，默认1
        $date = $input['date'] ?? date('Y-m-d');

        if (!$keyIndex || $keyIndex < 1 || $keyIndex > 20) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'error' => '无效的密钥索引'
            ]);
            return;
        }

        // 使用INSERT ... ON DUPLICATE KEY UPDATE来处理记录不存在的情况
        $query = "INSERT INTO google_api_usage (api_key_index, usage_date, usage_count)
                 VALUES (:key_index, :date, :count)
                 ON DUPLICATE KEY UPDATE
                 usage_count = usage_count + :count,
                 updated_at = CURRENT_TIMESTAMP";
        
        $stmt = $conn->prepare($query);
        $stmt->bindParam(':key_index', $keyIndex, PDO::PARAM_INT);
        $stmt->bindParam(':count', $count, PDO::PARAM_INT);
        $stmt->bindParam(':date', $date);
        $stmt->execute();
        
        // 获取更新后的记录
        $selectQuery = "SELECT * FROM google_api_usage 
                       WHERE api_key_index = :key_index AND usage_date = :date";
        $selectStmt = $conn->prepare($selectQuery);
        $selectStmt->bindParam(':key_index', $keyIndex, PDO::PARAM_INT);
        $selectStmt->bindParam(':date', $date);
        $selectStmt->execute();
        
        $result = $selectStmt->fetch();
        
        echo json_encode([
            'success' => true,
            'message' => '使用次数记录成功',
            'data' => $result
        ]);
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'error' => '记录失败'
        ]);
    }
}

/**
 * 更新API使用统计（设置具体数值）
 */
function handleUpdateUsage($conn) {
    try {
        $input = json_decode(file_get_contents('php://input'), true);
        $keyIndex = $input['key_index'] ?? null;
        $usageCount = $input['usage_count'] ?? null;
        $date = $input['date'] ?? date('Y-m-d');
        
        if (!$keyIndex || $keyIndex < 1 || $keyIndex > 20 || $usageCount === null) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'error' => '参数不完整'
            ]);
            return;
        }
        
        $query = "INSERT INTO google_api_usage (api_key_index, usage_date, usage_count) 
                 VALUES (:key_index, :date, :usage_count)
                 ON DUPLICATE KEY UPDATE 
                 usage_count = :usage_count, 
                 updated_at = CURRENT_TIMESTAMP";
        
        $stmt = $conn->prepare($query);
        $stmt->bindParam(':key_index', $keyIndex, PDO::PARAM_INT);
        $stmt->bindParam(':date', $date);
        $stmt->bindParam(':usage_count', $usageCount, PDO::PARAM_INT);
        $stmt->execute();
        
        echo json_encode([
            'success' => true,
            'message' => '使用统计更新成功'
        ]);
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'error' => '更新失败'
        ]);
    }
}

/**
 * 为指定日期初始化20个API密钥的使用记录
 * @param PDO $conn 数据库连接
 * @param string $date 日期 (YYYY-MM-DD)
 */
function initializeDailyRecords($conn, $date) {
    try {
        // 使用事务确保数据一致性
        $conn->beginTransaction();

        // 批量插入20个密钥的初始记录
        $query = "INSERT IGNORE INTO google_api_usage (api_key_index, usage_date, usage_count) VALUES ";
        $values = [];
        $params = [];

        for ($i = 1; $i <= 20; $i++) {
            $values[] = "(?, ?, 0)";
            $params[] = $i;
            $params[] = $date;
        }

        $query .= implode(', ', $values);
        $stmt = $conn->prepare($query);
        $stmt->execute($params);

        $conn->commit();

    } catch (Exception $e) {
        $conn->rollback();
        throw $e;
    }
}
?>
