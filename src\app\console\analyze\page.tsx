'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../../components/ui/card'
import { But<PERSON> } from '../../../components/ui/button'
import { Badge } from '../../../components/ui/badge'
import { Progress } from '../../../components/ui/progress'
import { Alert, AlertDescription } from '../../../components/ui/alert'

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../../components/ui/select'

import { Checkbox } from '../../../components/ui/checkbox'
import { Skeleton } from '../../../components/ui/skeleton'
import { toast } from 'sonner'
import {
  Brain,
  Play,

  RotateCcw,
  CheckCircle,
  XCircle,

  Globe,
  Target,
  AlertTriangle,
  Zap,

  BarChart3,
  <PERSON>,
  <PERSON>,
  Filter,
  Loader2,

} from 'lucide-react'
import ConsoleLayout from '../../../components/console-layout'

interface UnanalyzedKeyword {
  id: string
  keyword: string
  created_at: string
  source: string
}

interface AnalysisProgress {
  current: number
  total: number
  keyword: string
  percentage: number
}

interface AnalysisResult {
  keyword: string
  analysis: {
    user_intent: string
    user_pain_point: string
    competition_level: string
    competition_score: number
    competition_color: string
    competition_description: string
    category: string
    serp_analysis: string
    recommended_domains: Array<{
      domain: string
      checkUrl: string
      alternativeCheckUrl: string
    }>
  }
}

interface BatchAnalysisResult {
  summary: {
    total: number
    successful: number
    failed: number
    processed: number
  }
  results: AnalysisResult[]
  errors: Array<{
    keyword: string
    error: string
  }>
  cleanup?: {
    attempted: number
    deleted: number
    errors: string[]
  }
}

export default function AnalyzePage() {
  const { data: session, status } = useSession()
  const router = useRouter()

  const [unanalyzedKeywords, setUnanalyzedKeywords] = useState<UnanalyzedKeyword[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [isAnalyzing, setIsAnalyzing] = useState(false)

  const [progress, setProgress] = useState<AnalysisProgress | null>(null)
  const [analysisResult, setAnalysisResult] = useState<BatchAnalysisResult | null>(null)
  const [selectedKeywords, setSelectedKeywords] = useState<string[]>([])
  const [batchSize, setBatchSize] = useState(10)
  const [activeTab, setActiveTab] = useState('keywords')



  // 设置页面标题
  useEffect(() => {
    document.title = 'AI分析 - CatchIdeas'
  }, [])

  // 获取未分析的关键词
  const fetchUnanalyzedKeywords = async () => {
    setIsLoading(true)
    setSelectedKeywords([]) // 清空选择
    try {
      // 添加时间戳防止缓存
      const timestamp = Date.now()
      const apiUrl = `/api/keywords/analyze?limit=200&t=${timestamp}`

      const response = await fetch(apiUrl, {
        cache: 'no-store', // 禁用缓存
        headers: {
          'Cache-Control': 'no-cache'
        }
      })
      const data = await response.json()

      if (data.success) {
        const keywords = data.data.keywords || []

        setUnanalyzedKeywords(keywords)
      } else {
        setUnanalyzedKeywords([])
      }
    } catch {
      setUnanalyzedKeywords([])
    } finally {
      setIsLoading(false)

    }
  }

  // 开始批量分析
  const startBatchAnalysis = async () => {
    if (selectedKeywords.length === 0) {
      alert('请选择要分析的关键词')
      return
    }

    setIsAnalyzing(true)

    setProgress(null)
    setAnalysisResult(null)

    try {
      const keywordsToAnalyze = selectedKeywords.slice(0, batchSize)
      
      const response = await fetch('/api/keywords/analyze', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          keywords: keywordsToAnalyze.map((id: string) => {
            const keyword = unanalyzedKeywords.find(k => k.id === id)
            return { keyword: keyword?.keyword || id }
          }),
          mode: 'batch'
        })
      })

      const result = await response.json()
      
      if (result.success) {
        setAnalysisResult(result.data)

        // 显示DuckDuckGo调试信息
        if (result.data.results && result.data.results.length > 0) {
          result.data.results.forEach((item: any) => {

          })
        }

        // 检查是否所有关键词都分析完成
        const totalAnalyzed = result.data.summary.successful + result.data.summary.failed
        const totalSelected = selectedKeywords.length
        const actualProcessed = keywordsToAnalyze.length

        // 无论成功失败，只要处理完当前批次就刷新列表
        // 延迟刷新，确保数据库已更新
        setTimeout(async () => {
          await fetchUnanalyzedKeywords()

          // 清空选择
          setSelectedKeywords([])
        }, 2000) // 增加延迟时间确保数据库更新完成

      } else {
        toast.error('分析失败: ' + result.error)
      }
    } catch {
      toast.error('批量分析失败，请稍后重试')
    } finally {
      setIsAnalyzing(false)
      setProgress(null)
    }
  }

  // 选择/取消选择关键词
  const toggleKeywordSelection = (keywordId: string) => {
    setSelectedKeywords(prev => 
      prev.includes(keywordId) 
        ? prev.filter(id => id !== keywordId)
        : [...prev, keywordId]
    )
  }

  // 全选/取消全选
  const toggleSelectAll = () => {
    if (selectedKeywords.length === unanalyzedKeywords.length) {
      setSelectedKeywords([])
    } else {
      setSelectedKeywords(unanalyzedKeywords.map(k => k.id))
    }
  }

  // 检查是否全选
  const isAllSelected = unanalyzedKeywords.length > 0 && selectedKeywords.length === unanalyzedKeywords.length



  // 获取竞争难度颜色和显示文本 - 统一的分数区间标准
  const getCompetitionBadgeColor = (level: string, score: number) => {
    // 统一的分数区间标准：简单(1-3)、中等(4-7)、困难(8-10)
    const normalizedScore = score || 5 // 默认分数

    // 优先根据分数判断，确保一致性
    if (normalizedScore <= 3) {
      return 'bg-green-600 text-white border-green-600 font-semibold'
    } else if (normalizedScore <= 7) {
      return 'bg-yellow-600 text-white border-yellow-600 font-semibold'
    } else {
      return 'bg-red-600 text-white border-red-600 font-semibold'
    }
  }

  // 获取标准化的竞争难度文本
  const getCompetitionLevelText = (level: string, score: number) => {
    const normalizedScore = score || 5

    // 根据分数返回标准化文本，确保一致性
    if (normalizedScore <= 3) {
      return '简单'
    } else if (normalizedScore <= 7) {
      return '中等'
    } else {
      return '困难'
    }
  }

  // 权限检查
  useEffect(() => {
    if (status === 'loading') return // 还在加载中

    if (!session) {
      router.push('/auth/signin') // 未登录跳转到登录页
      return
    }

    // 普通用户和管理员都可以访问analyze页面
  }, [session, status, router])

  useEffect(() => {
    if (session) { // 只有登录后才获取数据
      fetchUnanalyzedKeywords()
    }
  }, [session])

  if (status === 'loading') {
    return <div className="min-h-screen flex items-center justify-center">加载中...</div>
  }

  if (!session) {
    return null // 正在重定向
  }

  return (
    <ConsoleLayout
      title="AI智能分析中心"
    >
      <div className="space-y-8">
        {/* 分析概览 - 统一卡片风格 */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card className="data-card-enhanced stats-card-primary">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-primary/80">AI分析</CardTitle>
              <Brain className="h-5 w-5 text-primary" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">智能处理</div>
              <p className="text-xs text-muted-foreground">
                深度分析关键词
              </p>
            </CardContent>
          </Card>

          <Card className="data-card-enhanced stats-card-success">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-success/80">用户意图</CardTitle>
              <Target className="h-5 w-5 text-success" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">精准识别</div>
              <p className="text-xs text-muted-foreground">
                分析用户需求
              </p>
            </CardContent>
          </Card>

          <Card className="data-card-enhanced stats-card-warning">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-warning/80">竞争分析</CardTitle>
              <BarChart3 className="h-5 w-5 text-warning" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">市场评估</div>
              <p className="text-xs text-muted-foreground">
                竞争难度分析
              </p>
            </CardContent>
          </Card>

          <Card className="data-card-enhanced stats-card-secondary">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-secondary/80">域名推荐</CardTitle>
              <Globe className="h-5 w-5 text-secondary" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">智能建议</div>
              <p className="text-xs text-muted-foreground">
                域名推荐服务
              </p>
            </CardContent>
          </Card>
        </div>

        {/* 智能控制面板 */}
        <Card className="data-card-enhanced">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Zap className="h-6 w-6 text-primary" />
              <span>智能分析控制台</span>
            </CardTitle>
            <CardDescription>
              选择关键词进行AI深度分析，获取用户意图、竞争情报和市场洞察
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {/* 控制栏 */}
              <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
                <div className="flex items-center space-x-4">
                  <Button
                    onClick={toggleSelectAll}
                    variant="outline"
                    size="sm"
                    disabled={unanalyzedKeywords.length === 0}
                    className="btn-outline-enhanced"
                  >
                    <Checkbox
                      checked={isAllSelected}
                      className="mr-2"
                    />
                    {isAllSelected ? '取消全选' : '全选'}
                  </Button>
                  <Badge className="badge-trend">
                    已选择 {selectedKeywords.length} / {unanalyzedKeywords.length} 个关键词
                  </Badge>
                </div>

                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-2">
                    <label className="text-sm font-medium text-foreground">批次大小:</label>
                    <Select value={batchSize.toString()} onValueChange={(value) => setBatchSize(Number(value))}>
                      <SelectTrigger className="w-20 h-9 bg-card border border-border/50 hover:border-primary/30 focus:border-primary/50 transition-all duration-300 hover:shadow-md focus:shadow-lg">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent className="bg-card border border-border/50 shadow-xl rounded-lg">
                        <SelectItem value="5" className="hover:bg-muted/50 focus:bg-primary/10 rounded-md transition-colors">5</SelectItem>
                        <SelectItem value="10" className="hover:bg-muted/50 focus:bg-primary/10 rounded-md transition-colors">10</SelectItem>
                        <SelectItem value="20" className="hover:bg-muted/50 focus:bg-primary/10 rounded-md transition-colors">20</SelectItem>
                        <SelectItem value="50" className="hover:bg-muted/50 focus:bg-primary/10 rounded-md transition-colors">50</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <Button
                    onClick={startBatchAnalysis}
                    disabled={isAnalyzing || selectedKeywords.length === 0}
                    className="btn-primary-enhanced"
                  >
                    {isAnalyzing ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        分析中...
                      </>
                    ) : (
                      <>
                        <Play className="h-4 w-4 mr-2" />
                        开始分析
                      </>
                    )}
                  </Button>

                  <Button
                    onClick={fetchUnanalyzedKeywords}
                    variant="outline"
                    disabled={isLoading}
                    className="hover:bg-muted/50"
                  >
                    <RotateCcw className="h-4 w-4 mr-2" />
                    刷新
                  </Button>
                </div>
              </div>

              {/* 智能进度显示 */}
              {isAnalyzing && progress && (
                <div className="p-6 bg-gradient-to-r from-primary/5 to-secondary/5 rounded-xl border border-primary/20">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-3">
                      <div className="p-2 bg-primary/10 rounded-lg">
                        <Brain className="h-5 w-5 animate-pulse text-primary" />
                      </div>
                      <div>
                        <h4 className="font-semibold text-foreground">AI正在分析</h4>
                        <p className="text-sm text-muted-foreground">当前关键词: {progress.keyword}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-2xl font-bold text-primary">{progress.percentage}%</div>
                      <div className="text-xs text-muted-foreground">{progress.current} / {progress.total}</div>
                    </div>
                  </div>
                  <Progress value={progress.percentage} className="h-3" />
                  <div className="mt-3 flex items-center justify-between text-xs text-muted-foreground">
                    <span>分析用户意图 → 评估竞争难度 → 生成域名建议</span>
                    <span>预计剩余: {Math.ceil((progress.total - progress.current) * 0.5)} 分钟</span>
                  </div>
                </div>
              )}


            </div>
          </CardContent>
        </Card>

        {/* 分析工作区 */}
        <div className="space-y-6">
          {/* 自定义切换按钮 */}
          <div className="flex justify-center mb-6">
            <div className="inline-flex bg-slate-100/50 p-1 rounded-2xl">
              <button
                onClick={() => setActiveTab('keywords')}
                className={`flex items-center space-x-2 px-8 py-3 mx-1 rounded-xl font-medium transition-all duration-300 hover:scale-[1.02] active:scale-[0.98] ${
                  activeTab === 'keywords'
                    ? 'bg-orange-500 text-white shadow-lg shadow-orange-500/25'
                    : 'text-muted-foreground hover:text-foreground hover:bg-white/70'
                }`}
              >
                <Search className="h-4 w-4" />
                <span>待分析关键词</span>
              </button>
              <button
                onClick={() => setActiveTab('results')}
                className={`flex items-center space-x-2 px-8 py-3 mx-1 rounded-xl font-medium transition-all duration-300 hover:scale-[1.02] active:scale-[0.98] ${
                  activeTab === 'results'
                    ? 'bg-green-500 text-white shadow-lg shadow-green-500/25'
                    : 'text-muted-foreground hover:text-foreground hover:bg-white/70'
                }`}
              >
                <BarChart3 className="h-4 w-4" />
                <span>分析结果</span>
              </button>
            </div>
          </div>

          {/* 内容区域 */}
          {activeTab === 'keywords' && (
            <div className="space-y-6">
            <Card className="data-card-enhanced">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Filter className="h-5 w-5 text-primary" />
                  <span>待分析关键词库</span>
                </CardTitle>
                <CardDescription>
                  选择需要进行AI深度分析的关键词，获取市场洞察
                </CardDescription>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <div className="space-y-3">
                    {Array.from({ length: 5 }).map((_, index) => (
                      <div key={index} className="flex items-center space-x-3 p-3 border rounded-lg">
                        <Skeleton className="h-4 w-4" />
                        <Skeleton className="h-4 flex-1" />
                      </div>
                    ))}
                  </div>
                ) : unanalyzedKeywords.length === 0 ? (
                  <div className="text-center py-12">
                    <CheckCircle className="h-16 w-16 mx-auto mb-4 text-success" />
                    <h3 className="text-lg font-semibold text-foreground mb-2">分析完成</h3>
                    <p className="text-muted-foreground">所有关键词都已完成AI分析</p>
                  </div>
                ) : (
                  <div className="space-y-3 max-h-96 overflow-y-auto">
                    {unanalyzedKeywords.map((keyword) => (
                      <div
                        key={keyword.id}
                        className={`p-4 border rounded-lg cursor-pointer transition-all duration-200 ${
                          selectedKeywords.includes(keyword.id)
                            ? 'border-primary bg-primary/5 shadow-sm'
                            : 'border-border hover:border-primary/50 hover:bg-muted/30'
                        }`}
                        onClick={() => toggleKeywordSelection(keyword.id)}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <Checkbox
                              checked={selectedKeywords.includes(keyword.id)}
                              onChange={() => {}}
                              className="pointer-events-none"
                            />
                            <div>
                              <span className="font-medium text-foreground">{keyword.keyword}</span>
                              <div className="flex items-center space-x-2 mt-1">
                                <Badge className="badge-trend text-xs">{keyword.source}</Badge>
                                <span className="text-xs text-muted-foreground">
                                  {new Date(keyword.created_at).toLocaleDateString()}
                                </span>
                              </div>
                            </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Target className="h-4 w-4 text-muted-foreground" />
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
            </div>
          )}

          {activeTab === 'results' && (
            <div className="space-y-6">
            <Card className="data-card-enhanced">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <BarChart3 className="h-5 w-5 text-primary" />
                  <span>AI分析结果</span>
                </CardTitle>
                <CardDescription>
                  查看AI深度分析完成的关键词详情和市场洞察
                </CardDescription>
              </CardHeader>
              <CardContent>
                {!analysisResult ? (
                  <div className="text-center py-16">
                    <div className="w-20 h-20 mx-auto mb-6 rounded-full bg-muted/50 flex items-center justify-center">
                      <Brain className="h-10 w-10 text-muted-foreground" />
                    </div>
                    <h3 className="text-lg font-semibold text-foreground mb-2">等待分析结果</h3>
                    <p className="text-muted-foreground">开始分析后，AI洞察将显示在这里</p>
                  </div>
                ) : (
                  <div className="space-y-6">
                    {/* 分析摘要 - 统一设计 */}
                    <div className="grid grid-cols-2 gap-6">
                      <div className="text-center p-6 bg-card rounded-xl border border-success/30 shadow-sm">
                        <div className="text-3xl font-bold text-success mb-2">
                          {analysisResult?.summary?.successful || 0}
                        </div>
                        <div className="text-sm font-medium text-foreground">分析成功</div>
                        <CheckCircle className="h-6 w-6 mx-auto mt-2 text-success" />
                      </div>
                      <div className="text-center p-6 bg-card rounded-xl border border-destructive/30 shadow-sm">
                        <div className="text-3xl font-bold text-destructive mb-2">
                          {analysisResult?.summary?.failed || 0}
                        </div>
                        <div className="text-sm font-medium text-foreground">分析失败</div>
                        <XCircle className="h-6 w-6 mx-auto mt-2 text-destructive" />
                      </div>
                    </div>

                    {/* 清理结果信息 */}
                    {analysisResult?.cleanup && analysisResult.cleanup.attempted > 0 && (
                      <Alert className="border-orange-200 bg-orange-50 dark:border-orange-800 dark:bg-orange-900/20">
                        <AlertTriangle className="h-4 w-4 text-orange-600 dark:text-orange-400" />
                        <AlertDescription className="text-foreground">
                          <span className="font-medium">自动清理：</span>
                          已尝试清理 {analysisResult.cleanup.attempted} 个失败关键词，
                          成功删除 {analysisResult.cleanup.deleted} 个
                          {analysisResult.cleanup.errors.length > 0 && (
                            <span className="text-red-600 dark:text-red-400">
                              ，{analysisResult.cleanup.errors.length} 个清理失败
                            </span>
                          )}
                        </AlertDescription>
                      </Alert>
                    )}

                    {/* 成功分析的关键词 - 现代化设计 */}
                    {analysisResult?.results && analysisResult.results.length > 0 && (
                      <div className="space-y-4">
                        <h4 className="font-semibold text-foreground flex items-center space-x-2">
                          <CheckCircle className="h-5 w-5 text-success" />
                          <span>成功分析的关键词</span>
                        </h4>
                        <div className="space-y-4">
                          {analysisResult.results.slice(0, 3).map((result, index) => (
                            <Card key={index} className="data-card-enhanced">
                              <CardContent className="p-4">
                                <div className="flex items-center justify-between mb-3">
                                  <h5 className="font-semibold text-lg text-foreground">{result.keyword}</h5>
                                  <Badge className={getCompetitionBadgeColor(result.analysis.competition_level, result.analysis.competition_score)}>
                                    {getCompetitionLevelText(result.analysis.competition_level, result.analysis.competition_score)} ({result.analysis.competition_score}/10)
                                  </Badge>
                                </div>

                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                  <div className="space-y-2">
                                    <div className="flex items-center space-x-2">
                                      <Target className="h-4 w-4 text-primary" />
                                      <span className="text-sm font-medium text-foreground">分类</span>
                                    </div>
                                    <p className="text-sm text-muted-foreground pl-6">{result.analysis.category}</p>
                                  </div>

                                  <div className="space-y-2">
                                    <div className="flex items-center space-x-2">
                                      <Users className="h-4 w-4 text-secondary" />
                                      <span className="text-sm font-medium text-foreground">用户意图</span>
                                    </div>
                                    <p className="text-sm text-muted-foreground pl-6">{result.analysis.user_intent}</p>
                                  </div>
                                </div>

                                {result.analysis.recommended_domains && (
                                  <div className="mt-4 pt-4 border-t border-border">
                                    <div className="flex items-center space-x-2 mb-2">
                                      <Globe className="h-4 w-4 text-warning" />
                                      <span className="text-sm font-medium text-foreground">推荐域名</span>
                                    </div>
                                    <div className="flex flex-wrap gap-2">
                                      {result.analysis.recommended_domains.slice(0, 3).map((domain, idx) => (
                                        <a
                                          key={idx}
                                          href={domain.checkUrl}
                                          target="_blank"
                                          rel="noopener noreferrer"
                                          className="text-xs px-2 py-1 bg-primary/10 text-primary rounded hover:bg-primary/20 transition-colors"
                                        >
                                          {domain.domain}
                                        </a>
                                      ))}
                                    </div>
                                  </div>
                                )}
                              </CardContent>
                            </Card>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* 失败的关键词 - 现代化设计 */}
                    {analysisResult?.errors && analysisResult.errors.length > 0 && (
                      <div className="space-y-4">
                        <h4 className="font-semibold text-foreground flex items-center space-x-2">
                          <XCircle className="h-5 w-5 text-destructive" />
                          <span>分析失败的关键词</span>
                        </h4>
                        <div className="space-y-3">
                          {analysisResult.errors.slice(0, 3).map((error, index) => (
                            <Alert key={index} className="border-destructive/20 bg-destructive/5">
                              <AlertTriangle className="h-4 w-4 text-destructive" />
                              <AlertDescription className="text-foreground">
                                <span className="font-medium">{error.keyword}:</span> 分析失败，请重试
                              </AlertDescription>
                            </Alert>
                          ))}
                        </div>
                      </div>
                    )}
                </div>
              )}
            </CardContent>
            </Card>
            </div>
          )}
        </div>
      </div>
    </ConsoleLayout>
  )
}
