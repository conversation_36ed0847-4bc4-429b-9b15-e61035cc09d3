export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ 
      success: false, 
      message: 'Method not allowed' 
    })
  }

  try {
    const apiBaseUrl = process.env.NEXT_PUBLIC_PHP_API_URL || 'https://api.xstty.com/api'
    
    // 调用PHP API获取分类数据
    const response = await fetch(`${apiBaseUrl}/categories.php`)
    const data = await response.json()

    if (data.success) {
      res.status(200).json({
        success: true,
        data: data.data || []
      })
    } else {
      res.status(500).json({
        success: false,
        message: data.message || '获取分类失败'
      })
    }

  } catch (error) {
    console.error('Categories API error:', error)
    res.status(500).json({
      success: false,
      message: '服务器内部错误: ' + error.message
    })
  }
}
