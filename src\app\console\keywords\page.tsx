'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../../components/ui/card'
import { Button } from '../../../components/ui/button'
import { Input } from '../../../components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../../components/ui/select'
import { Badge } from '../../../components/ui/badge'
import { Checkbox } from '../../../components/ui/checkbox'
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '../../../components/ui/alert-dialog'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../../../components/ui/table'
import { Skeleton } from '../../../components/ui/skeleton'
import { Toaster, toast } from 'sonner'
import {
  Search,
  RefreshCw,
  Loader2,
  Database,
  Plus,
  Trash2,
  Upload,
  Target,
  CheckCircle,
  AlertCircle,
  Calendar,
  Tag
} from 'lucide-react'
import ConsoleLayout from '../../../components/console-layout'
import Link from 'next/link'

interface Keyword {
  id: string
  keyword: string
  user_intent?: string
  user_pain_point?: string
  competition_level?: 'easy' | 'medium' | 'hard'
  competition_score?: number
  competition_color?: string
  category?: string
  source?: string
  import_date?: string
  analyzed_at?: string
  created_at?: string
  is_analyzed: boolean
}

interface Statistics {
  total: number
  analyzed: number
  unanalyzed: number
  category_count: number
}

interface Category {
  id: string
  name: string
  english_name: string
}

export default function KeywordsPage() {
  const { data: session, status } = useSession()
  const router = useRouter()

  const [keywords, setKeywords] = useState<Keyword[]>([])
  const [categories, setCategories] = useState<Category[]>([])
  const [statistics, setStatistics] = useState<Statistics>({
    total: 0,
    analyzed: 0,
    unanalyzed: 0,
    category_count: 0
  })
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [deletingId, setDeletingId] = useState<string | null>(null)
  const [deleteKeyword, setDeleteKeyword] = useState<Keyword | null>(null)

  // 批量选择相关状态
  const [selectedKeywords, setSelectedKeywords] = useState<string[]>([])
  const [selectAll, setSelectAll] = useState(false)
  const [batchDeleting, setBatchDeleting] = useState(false)

  // 设置页面标题
  useEffect(() => {
    document.title = '关键词管理 - CatchIdeas'
  }, [])

  // 权限检查
  useEffect(() => {
    if (status === 'loading') return // 还在加载中

    if (!session) {
      router.push('/auth/signin') // 未登录跳转到登录页
      return
    }

    if ((session.user as any)?.role !== 'admin') {
      router.push('/unauthorized') // 普通用户无权访问关键词管理
      return
    }
  }, [session, status, router])

  // 获取数据
  useEffect(() => {
    if (session && (session.user as any)?.role === 'admin') { // 只有管理员才获取数据
      fetchData()
      fetchCategories()
    }
  }, [session])

  // 当搜索条件改变时重新获取数据
  useEffect(() => {
    if (session && (session.user as any)?.role === 'admin') {
      fetchData()
    }
  }, [searchTerm, session])

  const fetchData = async () => {
    setLoading(true)
    try {
      const params = new URLSearchParams({
        page: '1',
        limit: '10',
        search: searchTerm,
        sort_by: 'created_at',
        sort_order: 'desc'
      })

      // 添加时间戳防止缓存
      const timestamp = Date.now()
      const response = await fetch(`/api/keywords/list?${params}&t=${timestamp}`, {
        cache: 'no-store',
        headers: {
          'Cache-Control': 'no-cache'
        }
      })
      const data = await response.json()

      if (data.success) {
        setKeywords(data.data.keywords || [])
        setStatistics(data.data.statistics || {
          total: 0,
          analyzed: 0,
          unanalyzed: 0,
          category_count: 0
        })

      }
    } catch {
      // 静默处理错误
    } finally {
      setLoading(false)
    }
  }

  // 获取分类数据
  const fetchCategories = async () => {
    try {
      // 添加时间戳防止缓存
      const timestamp = Date.now()
      const response = await fetch(`/api/categories/list?t=${timestamp}`, {
        cache: 'no-store',
        headers: {
          'Cache-Control': 'no-cache'
        }
      })
      const data = await response.json()

      if (data.success && data.data.categories) {
        setCategories(data.data.categories || [])
      }
    } catch (error) {
      console.error('获取分类失败:', error)
    }
  }









  // 批量选择相关函数
  const handleSelectAll = (checked: boolean) => {
    setSelectAll(checked)
    if (checked) {
      setSelectedKeywords(keywords.map(k => k.id))
    } else {
      setSelectedKeywords([])
    }
  }

  const handleSelectKeyword = (keywordId: string, checked: boolean) => {
    if (checked) {
      setSelectedKeywords(prev => [...prev, keywordId])
    } else {
      setSelectedKeywords(prev => prev.filter(id => id !== keywordId))
      setSelectAll(false)
    }
  }

  // 批量删除
  const handleBatchDelete = async () => {
    if (selectedKeywords.length === 0) return

    setBatchDeleting(true)
    try {
      const response = await fetch('/api/keywords/batch-delete', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache'
        },
        cache: 'no-store',
        body: JSON.stringify({ ids: selectedKeywords })
      })

      const data = await response.json()

      if (data.success) {
        // 从本地状态中移除已删除的关键词
        setKeywords(prev => prev.filter(k => !selectedKeywords.includes(k.id)))

        // 更新统计数据
        setStatistics(prev => ({
          ...prev,
          total: Math.max(0, prev.total - selectedKeywords.length)
        }))

        toast.success(`成功删除 ${selectedKeywords.length} 个关键词`)
        setSelectedKeywords([])
        setSelectAll(false)

        // 延迟重新获取数据
        setTimeout(() => {
          fetchData()
        }, 500)
      } else {
        toast.error('批量删除失败: ' + data.message)
      }
    } catch (error) {
      console.error('批量删除失败:', error)
      toast.error('批量删除失败，请重试')
    } finally {
      setBatchDeleting(false)
    }
  }

  // 删除关键词
  const handleDelete = async () => {
    if (!deleteKeyword) return

    setDeletingId(deleteKeyword.id)
    try {
      const response = await fetch(`/api/keywords/delete`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ id: deleteKeyword.id })
      })

      const data = await response.json()

      if (data.success) {
        // 立即从本地状态中移除已删除的关键词
        setKeywords(prev => prev.filter(k => k.id !== deleteKeyword.id))

        // 更新统计数据
        setStatistics(prev => ({
          ...prev,
          total: Math.max(0, prev.total - 1)
        }))

        toast.success('关键词删除成功')
        setDeleteKeyword(null) // 关闭对话框

        // 延迟重新获取数据以确保服务器端同步
        setTimeout(() => {
          fetchData()
        }, 500)
      } else {
        console.error('删除失败:', data.message)
        toast.error('删除失败: ' + data.message)
      }
    } catch (error) {
      console.error('删除关键词失败:', error)
      toast.error('删除失败，请重试')
    } finally {
      setDeletingId(null)
    }
  }

  if (status === 'loading') {
    return <div className="min-h-screen flex items-center justify-center">加载中...</div>
  }

  if (!session) {
    return null // 正在重定向
  }

  if ((session.user as any)?.role !== 'admin') {
    return null // 正在重定向到unauthorized页面
  }

  return (
    <ConsoleLayout
      title="智能关键词管理"
    >
      <div className="space-y-6">
        {/* 统计概览 - 与后台首页保持一致 */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card className="data-card-enhanced stats-card-primary">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-primary/80">总关键词</CardTitle>
              <Database className="h-5 w-5 text-primary" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{statistics.total}</div>
              <p className="text-xs text-muted-foreground">
                关键词数据库
              </p>
            </CardContent>
          </Card>

          <Card className="data-card-enhanced stats-card-success">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-success/80">已分析</CardTitle>
              <CheckCircle className="h-5 w-5 text-success" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{statistics.analyzed}</div>
              <p className="text-xs text-muted-foreground">
                完成AI分析
              </p>
            </CardContent>
          </Card>

          <Card className="data-card-enhanced stats-card-warning">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-warning/80">待分析</CardTitle>
              <AlertCircle className="h-5 w-5 text-warning" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{statistics.unanalyzed}</div>
              <p className="text-xs text-muted-foreground">
                等待处理
              </p>
            </CardContent>
          </Card>

          <Card className="data-card-enhanced stats-card-secondary">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-secondary/80">分类数</CardTitle>
              <Tag className="h-5 w-5 text-secondary" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{statistics.category_count}</div>
              <p className="text-xs text-muted-foreground">
                数据分类
              </p>
            </CardContent>
          </Card>
        </div>

        {/* 智能搜索区域 - 简化设计 */}
        <Card className="data-card-enhanced">
          <CardContent className="p-6">
            <div className="flex flex-col lg:flex-row gap-4">
              {/* 搜索输入框 - 统一样式 */}
              <div className="flex-1">
                <div className="relative group">
                  <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-muted-foreground h-5 w-5 transition-colors group-hover:text-primary" />
                  <Input
                    placeholder="搜索关键词内容..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-12 pr-4 h-12 text-base border border-border/50 focus:border-primary/50 bg-card rounded-lg shadow-sm hover:shadow-md focus:shadow-lg transition-all duration-300 placeholder:text-muted-foreground/60"
                  />
                </div>
              </div>

              {/* 刷新按钮 */}
              <div className="flex gap-3">
                <Button
                  variant="outline"
                  onClick={fetchData}
                  className="btn-outline-enhanced h-12 px-4"
                >
                  <RefreshCw className="h-5 w-5" />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 关键词数据表 - 现代化设计 */}
        <Card className="data-card-enhanced">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center space-x-2">
                  <Database className="h-5 w-5 text-primary" />
                  <span>关键词数据表</span>
                </CardTitle>
                <CardDescription className="mt-1">
                  {statistics.total > 0 ? `共 ${statistics.total} 个关键词，显示最新 10 条记录` : '暂无关键词数据，请先上传关键词'}
                </CardDescription>
              </div>
              <div className="flex items-center space-x-2">
                {selectedKeywords.length > 0 && (
                  <div className="flex items-center space-x-3">
                    <Badge className="bg-primary/10 text-primary border-primary/20 px-3 py-1">
                      已选择 {selectedKeywords.length} 项
                    </Badge>
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button
                          variant="destructive"
                          size="sm"
                          disabled={batchDeleting}
                          className="bg-red-500 hover:bg-red-600 text-white border-0 shadow-sm hover:shadow-md transition-all duration-200 hover:scale-105 active:scale-95 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                        >
                          {batchDeleting ? (
                            <Loader2 className="h-4 w-4 animate-spin mr-2" />
                          ) : (
                            <Trash2 className="h-4 w-4 mr-2" />
                          )}
                          批量删除
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent className="bg-white dark:bg-gray-900 border-2 border-gray-200 dark:border-gray-700 shadow-2xl max-w-md rounded-xl">
                        <AlertDialogHeader className="space-y-3">
                          <AlertDialogTitle className="text-xl font-bold text-gray-900 dark:text-white">确认批量删除</AlertDialogTitle>
                          <AlertDialogDescription className="text-gray-600 dark:text-gray-300 text-base leading-relaxed">
                            您确定要删除选中的 <span className="font-semibold text-red-600 dark:text-red-400">{selectedKeywords.length}</span> 个关键词吗？此操作不可撤销。
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter className="space-x-3 pt-6">
                          <AlertDialogCancel className="btn-outline-enhanced bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700">
                            取消
                          </AlertDialogCancel>
                          <AlertDialogAction
                            onClick={handleBatchDelete}
                            className="bg-red-500 hover:bg-red-600 text-white border-0 shadow-sm hover:shadow-md transition-all duration-200 hover:scale-105 active:scale-95 px-6"
                          >
                            确认删除
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  </div>
                )}
                <Button asChild className="btn-primary-enhanced">
                  <Link href="/console/upload">
                    <Plus className="h-4 w-4 mr-2" />
                    添加关键词
                  </Link>
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="space-y-4">
                {Array.from({ length: 5 }).map((_, index) => (
                  <div key={index} className="flex items-center space-x-4 p-4 border rounded-lg">
                    <Skeleton className="h-4 w-4" />
                    <Skeleton className="h-4 flex-1" />
                    <Skeleton className="h-4 w-20" />
                    <Skeleton className="h-4 w-20" />
                    <Skeleton className="h-4 w-20" />
                    <Skeleton className="h-4 w-20" />
                  </div>
                ))}
              </div>
            ) : keywords.length === 0 ? (
              <div className="text-center py-16">
                <Database className="h-20 w-20 text-primary/50 mx-auto mb-6" />
                <h3 className="text-2xl font-bold text-foreground mb-3">开始构建关键词库</h3>
                <p className="text-muted-foreground mb-8 max-w-md mx-auto">
                  还没有关键词数据？上传您的第一批关键词，开始使用智能分析功能。
                </p>
                <div className="flex flex-col sm:flex-row gap-3 justify-center">
                  <Button asChild className="btn-primary-enhanced">
                    <Link href="/console/upload">
                      <Upload className="h-4 w-4 mr-2" />
                      上传关键词
                    </Link>
                  </Button>
                  <Button asChild variant="outline" className="btn-outline-enhanced">
                    <Link href="/console/analyze">
                      <Target className="h-4 w-4 mr-2" />
                      了解分析功能
                    </Link>
                  </Button>
                </div>
              </div>
            ) : (
              <div className="space-y-4">
                {/* 现代化数据表格 - 统一样式 */}
                <div className="border border-border/50 rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-shadow duration-300 bg-card">
                  <Table>
                    <TableHeader className="bg-muted/30">
                      <TableRow className="hover:bg-muted/50 transition-colors border-b border-border/30">
                        <TableHead className="w-[50px] py-4">
                          <Checkbox
                            checked={selectAll}
                            onCheckedChange={handleSelectAll}
                            aria-label="全选"
                            className="border-2 border-primary/30 data-[state=checked]:bg-primary data-[state=checked]:border-primary"
                          />
                        </TableHead>
                        <TableHead className="w-[400px] font-bold text-foreground py-4">关键词</TableHead>
                        <TableHead className="font-bold text-foreground py-4">分类</TableHead>
                        <TableHead className="font-bold text-foreground py-4">竞争难度</TableHead>
                        <TableHead className="font-bold text-foreground py-4">创建时间</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {keywords.map((keyword) => (
                        <TableRow key={keyword.id} className="hover:bg-muted/30 transition-all duration-300 border-b border-border/20 last:border-b-0">
                          <TableCell className="py-4">
                            <Checkbox
                              checked={selectedKeywords.includes(keyword.id)}
                              onCheckedChange={(checked: boolean) => handleSelectKeyword(keyword.id, checked)}
                              aria-label={`选择 ${keyword.keyword}`}
                              className="border-2 border-primary/30 data-[state=checked]:bg-primary data-[state=checked]:border-primary"
                            />
                          </TableCell>
                          <TableCell className="py-4">
                            <div className="space-y-2">
                              <div className="font-semibold text-foreground text-base">{keyword.keyword}</div>
                              <div className="flex items-center space-x-2">
                                {keyword.is_analyzed ? (
                                  <Badge className="badge-analyzed text-xs px-2 py-1">已分析</Badge>
                                ) : (
                                  <Badge className="badge-pending text-xs px-2 py-1">待分析</Badge>
                                )}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell className="py-4">
                            {keyword.category ? (
                              <Badge variant="outline" className="border-primary/30 text-primary bg-primary/5 px-3 py-1">
                                {keyword.category}
                              </Badge>
                            ) : (
                              <span className="text-muted-foreground text-sm bg-muted/30 px-2 py-1 rounded-md">未分类</span>
                            )}
                          </TableCell>
                          <TableCell className="py-4">
                            {keyword.competition_score ? (
                              <Badge className={`px-3 py-1 font-semibold ${
                                keyword.competition_score <= 3 ? 'badge-competition-easy' :
                                keyword.competition_score <= 7 ? 'badge-competition-medium' :
                                'badge-competition-hard'
                              }`}>
                                {keyword.competition_score <= 3 ? '简单' :
                                 keyword.competition_score <= 7 ? '中等' : '困难'} ({keyword.competition_score}/10)
                              </Badge>
                            ) : (
                              <span className="text-muted-foreground text-sm bg-muted/30 px-2 py-1 rounded-md">未分析</span>
                            )}
                          </TableCell>
                          <TableCell className="py-4">
                            <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                              <div className="p-1 bg-muted/30 rounded-md">
                                <Calendar className="h-3 w-3" />
                              </div>
                              <span className="font-medium">{keyword.created_at ? new Date(keyword.created_at).toLocaleDateString() : '-'}</span>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>


              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* 删除确认对话框 */}
      <AlertDialog open={!!deleteKeyword} onOpenChange={() => setDeleteKeyword(null)}>
        <AlertDialogContent className="bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 shadow-2xl max-w-md">
          <AlertDialogHeader className="space-y-3">
            <AlertDialogTitle className="text-xl font-semibold text-gray-900 dark:text-gray-100 flex items-center gap-2">
              <div className="w-8 h-8 rounded-full bg-red-100 dark:bg-red-900/30 flex items-center justify-center">
                <svg className="w-4 h-4 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
              </div>
              确认删除关键词
            </AlertDialogTitle>
            <AlertDialogDescription className="text-gray-600 dark:text-gray-300 text-base leading-relaxed">
              您即将删除关键词{' '}
              <span className="font-semibold text-gray-900 dark:text-gray-100 bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded">
                "{deleteKeyword?.keyword}"
              </span>
              <br />
              <span className="text-red-600 dark:text-red-400 font-medium mt-2 block">
                ⚠️ 此操作无法撤销，请谨慎操作。
              </span>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter className="gap-3 pt-6">
            <AlertDialogCancel className="bg-gray-100 hover:bg-gray-200 text-gray-900 border-gray-300 dark:bg-gray-800 dark:hover:bg-gray-700 dark:text-gray-100 dark:border-gray-600">
              取消
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              disabled={deletingId === deleteKeyword?.id}
              className="bg-red-600 hover:bg-red-700 text-white border-red-600 hover:border-red-700 disabled:opacity-50 disabled:cursor-not-allowed min-w-[100px]"
            >
              {deletingId === deleteKeyword?.id ? (
                <div className="flex items-center gap-2">
                  <svg className="animate-spin h-4 w-4" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  删除中...
                </div>
              ) : (
                '确认删除'
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      <Toaster position="top-right" richColors closeButton duration={5000} />
    </ConsoleLayout>
  )
}
