# 🚀 CatchIdeas v3.57 - 智能关键词分析平台

基于Next.js + PHP的全栈智能关键词分析平台，集成多AI模型和Google API，提供专业的SEO关键词分析和市场洞察。

[![Next.js](https://img.shields.io/badge/Next.js-14-black?style=flat-square&logo=next.js)](https://nextjs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5-blue?style=flat-square&logo=typescript)](https://www.typescriptlang.org/)
[![Tailwind CSS](https://img.shields.io/badge/Tailwind-3-38B2AC?style=flat-square&logo=tailwind-css)](https://tailwindcss.com/)
[![MySQL](https://img.shields.io/badge/MySQL-8-4479A1?style=flat-square&logo=mysql)](https://www.mysql.com/)

## 🏗️ 系统架构

### 技术栈概览
```
前端层: Next.js 14 + TypeScript + Tailwind CSS + shadcn/ui
API层: Next.js API Routes + PHP API
AI层: SiliconFlow (DeepSeek-V3, Qwen系列) + 智谱AI
数据层: MySQL + Google APIs
部署: Vercel + Cloudflare DNS
```

### 架构设计
- **前后端分离**：Next.js处理前端和部分API，PHP处理数据库操作
- **多AI模型集成**：智能轮换20个SiliconFlow API密钥
- **Google API负载均衡**：支持20个Google Search API密钥轮换
- **微服务化API**：模块化的API设计，易于扩展和维护

## 📁 项目目录结构

### 根目录文件
```
CatchIdeas/
├── package.json              # 项目依赖和脚本配置
├── next.config.js           # Next.js配置文件
├── tailwind.config.js       # Tailwind CSS配置
├── tsconfig.json           # TypeScript配置
├── .env.example            # 环境变量模板
└── README.md               # 项目文档
```

### 前端源码 (`src/`)
```
src/
├── app/                    # Next.js App Router
│   ├── layout.tsx         # 全局布局组件
│   ├── page.tsx           # 首页
│   ├── globals.css        # 全局样式
│   ├── api/               # Next.js API Routes
│   │   ├── domains/       # 域名生成API
│   │   └── logo/          # Logo生成API
│   ├── auth/              # 认证相关页面
│   ├── console/           # 后台管理页面
│   │   ├── analyze/       # AI分析页面
│   │   ├── upload/        # 数据导入页面
│   │   └── keywords/      # 关键词管理
│   ├── keyword/           # 关键词详情页面
│   └── filter/            # 关键词筛选页面
├── components/            # React组件库
│   ├── ui/               # shadcn/ui基础组件
│   ├── auth/             # 认证组件
│   ├── console-layout.tsx # 后台布局
│   ├── frontend-header.tsx # 前台头部
│   └── keyword-display.tsx # 关键词展示
├── hooks/                # 自定义React Hooks
│   ├── useAuth.ts        # 认证状态管理
│   └── use-mobile.ts     # 移动端检测
├── lib/                  # 工具函数
│   └── utils.ts          # 通用工具函数
└── middleware.ts         # Next.js中间件
```

### API路由 (`pages/api/`)
```
pages/api/
├── auth/                 # NextAuth.js认证
│   └── [...nextauth].ts  # OAuth配置
├── keywords/             # 关键词相关API
│   ├── analyze.js        # 关键词分析
│   ├── list.js          # 关键词列表
│   ├── [id].js          # 关键词详情
│   ├── generate-complete-report.js # 完整报告生成
│   ├── generate-section.js # 段落生成
│   └── update.js        # 关键词更新
├── trends/              # 数据导入API
│   ├── import.js        # CSV导入
│   └── manual-import.js # 手动导入
├── google/              # Google API
│   └── suggestions.js   # 搜索建议
├── categories/          # 分类管理
│   └── list.js         # 分类列表
├── dashboard/           # 仪表板
│   └── stats.js        # 统计数据
└── test/               # 测试接口
    ├── google-search.js # Google API测试
    └── siliconflow-balance.js # AI余额查询
```

### 核心业务逻辑 (`lib/`)
```
lib/
├── ai-processor.js       # AI处理器核心类
│   ├── 关键词分析流程控制
│   ├── 多AI模型调用管理
│   ├── Google API轮换逻辑
│   └── 错误处理和重试机制
├── algorithm-utils.js    # 算法工具函数
│   ├── SEO难度计算
│   ├── 搜索意图分析
│   ├── 商业价值评估
│   └── 综合评分算法
├── google-api.js        # Google API封装
│   ├── 搜索结果获取
│   ├── 竞争对手分析
│   └── SERP数据解析
├── google-usage-tracker.js # Google API使用统计
│   ├── 密钥使用量跟踪
│   ├── 智能密钥选择
│   └── 配额管理
├── csv-parser.js        # CSV解析器
│   ├── 文件解析和验证
│   ├── AI智能过滤
│   └── 批量数据处理
├── client-report-generator.js # 客户端报告生成
├── api-client.js        # API客户端封装
└── date-utils.js        # 日期工具函数
```

### PHP后端API (`php-api/`)
```
php-api/
├── api/                 # PHP API接口
│   ├── keywords.php     # 关键词CRUD操作
│   ├── import.php       # 数据导入处理
│   ├── analyze.php      # 分析状态管理
│   ├── google-usage.php # Google API使用统计
│   ├── categories.php   # 分类管理
│   ├── keyword_reports.php # 报告管理
│   └── batch-delete.php # 批量删除
├── config/              # 配置文件
│   ├── database.php     # 数据库连接配置
│   └── cors.php         # CORS跨域配置
├── models/              # 数据模型
│   ├── Keyword.php      # 关键词模型
│   ├── KeywordReport.php # 报告模型
│   ├── KeywordSuggestion.php # 建议模型
│   └── ImportLog.php    # 导入日志模型
├── utils/               # 工具类
│   └── Response.php     # 统一响应格式
└── database/            # 数据库相关
    └── data.sql         # 数据库结构和初始数据
```

## 🎯 AI模型配置

### SiliconFlow AI平台配置
```bash
# 方案A（高质量快速模型）- 2025年7月最新优化
SILICONFLOW_SIMILARITY_MODEL=Qwen/Qwen3-Embedding-8B          # 相似度分析：关键词语义相似度计算和聚类分析
SILICONFLOW_COMPETITION_MODEL=deepseek-ai/DeepSeek-V3         # 竞争分析：SERP竞争对手分析和难度评估
SILICONFLOW_REPORT_MODEL=deepseek-ai/DeepSeek-V3              # 报告生成：完整关键词分析报告和策略建议
SILICONFLOW_DOMAIN_MODEL=Qwen/Qwen2.5-32B-Instruct           # 域名推荐：SEO友好域名生成和品牌建议
SILICONFLOW_LOGO_TEXT_MODEL=Qwen/Qwen2.5-14B-Instruct        # Logo文案：Logo设计提示词生成和创意描述
SILICONFLOW_LOGO_IMAGE_MODEL=Kwai-Kolors/Kolors              # Logo图像：AI Logo图像生成和视觉设计
SILICONFLOW_FILTER_MODEL=deepseek-ai/DeepSeek-V3              # 智能过滤：CSV导入时过滤低质量和违规关键词
```

### 智谱AI配置
```bash
ZHIPU_AI_API_KEY=                                             # 智谱AI密钥：用于快速关键词分析
ZHIPU_AI_BASE_URL=https://open.bigmodel.cn/api/paas/v4       # 智谱AI接口地址
ZHIPU_AI_MODEL=glm-4-flash-250414                            # 高速模型：批量关键词快速分析和分类
```

### 生成sitemap.txt
https://api.xstty.com/api/keyword-urls.php