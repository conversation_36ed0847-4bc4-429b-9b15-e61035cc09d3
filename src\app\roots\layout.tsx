import type { Metadata } from "next";

export const metadata: Metadata = {
  title: "词根导航 | CatchIdeas",
  description: "精选AI工具站和小游戏词根关键词导航，已验证Google Trends热度。涵盖现象级旗舰模型、AI生成设计、开发者工具、游戏玩法、视觉风格等14大分类，300+精选词根，助力您的项目关键词挖掘。",
  authors: [{ name: "CatchIdeas团队" }],
  creator: "CatchIdeas",
  publisher: "CatchIdeas",
  robots: "index, follow",
  openGraph: {
    type: "website",
    locale: "zh_CN",
    url: "https://catchideas.com/roots",
    title: "词根导航 | CatchIdeas",
    description: "精选AI工具站和小游戏词根关键词导航，已验证Google Trends热度。涵盖现象级旗舰模型、AI生成设计、开发者工具、游戏玩法、视觉风格等14大分类，300+精选词根，助力您的项目关键词挖掘。",
    siteName: "CatchIdeas",
  },
  twitter: {
    card: "summary_large_image",
    title: "词根导航 | CatchIdeas",
    description: "精选AI工具站和小游戏词根关键词导航，已验证Google Trends热度。涵盖现象级旗舰模型、AI生成设计、开发者工具、游戏玩法、视觉风格等14大分类，300+精选词根，助力您的项目关键词挖掘。",
  },
  alternates: {
    canonical: "https://catchideas.com/roots",
  },
};

export default function RootsLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}
