<?php
/**
 * 数据库配置文件
 * MySQL连接配置 - SugarHosts
 */

class Database {
    private $host = "localhost";
    private $port = 3306;
    private $db_name = "ffkjdfhu_CatchIdeas";
    private $username = "ffkjdfhu_CatchIdeas";
    private $password = "d(p%MJau%6m";
    private $charset = "utf8mb4";
    public $conn;

    /**
     * 获取数据库连接
     */
    public function getConnection() {
        $this->conn = null;

        try {
            $dsn = "mysql:host=" . $this->host . ";port=" . $this->port . ";dbname=" . $this->db_name . ";charset=" . $this->charset;
            $this->conn = new PDO($dsn, $this->username, $this->password);
            $this->conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            $this->conn->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        } catch(PDOException $exception) {
            throw new Exception("数据库连接失败");
        }

        return $this->conn;
    }

    /**
     * 关闭数据库连接
     */
    public function closeConnection() {
        $this->conn = null;
    }

    /**
     * 测试数据库连接
     */
    public function testConnection() {
        try {
            $conn = $this->getConnection();
            if ($conn) {
                return [
                    'success' => true,
                    'message' => '数据库连接成功',
                    'server_info' => $conn->getAttribute(PDO::ATTR_SERVER_VERSION)
                ];
            }
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => '数据库连接失败: ' . $e->getMessage()
            ];
        }
    }
}
?>
