import type { Metadata } from "next";

export const metadata: Metadata = {
  title: "落地页SEO优化指南 - 标题优化、关键词布局与内容架构完整教程 | CatchIdeas",
  description: "掌握落地页SEO核心策略，学习标题优化公式、关键词布局技巧、H标签架构设计和技术优化要点。通过科学的SEO方法提升搜索排名，实现流量增长和转化提升。",
  authors: [{ name: "CatchIdeas团队" }],
  creator: "CatchIdeas",
  publisher: "CatchIdeas",
  robots: "index, follow",
  keywords: [
    "落地页SEO",
    "SEO优化指南",
    "标题优化",
    "关键词布局",
    "H标签优化",
    "内容架构",
    "技术SEO",
    "搜索引擎优化",
    "SEO策略",
    "网页优化",
    "搜索排名",
    "SEO教程",
    "落地页优化",
    "关键词密度",
    "元标签优化"
  ],
  openGraph: {
    type: "website",
    locale: "zh_CN",
    url: "https://catchideas.com/landing-page-seo-guide",
    title: "落地页SEO优化指南 - 标题优化、关键词布局与内容架构完整教程 | CatchIdeas",
    description: "掌握落地页SEO核心策略，学习标题优化公式、关键词布局技巧、H标签架构设计和技术优化要点。通过科学的SEO方法提升搜索排名，实现流量增长和转化提升。",
    siteName: "CatchIdeas",
  },
  twitter: {
    card: "summary_large_image",
    title: "落地页SEO优化指南 - 标题优化、关键词布局与内容架构完整教程 | CatchIdeas",
    description: "掌握落地页SEO核心策略，学习标题优化公式、关键词布局技巧、H标签架构设计和技术优化要点。通过科学的SEO方法提升搜索排名，实现流量增长和转化提升。",
  },
  alternates: {
    canonical: "https://catchideas.com/landing-page-seo-guide",
  },
};

export default function LandingPageSEOGuideLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}
