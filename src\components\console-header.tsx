'use client'

import { usePathname } from 'next/navigation'
import { useSession, signOut } from 'next-auth/react'
import Link from 'next/link'
import { Badge } from './ui/badge'
import { Button } from './ui/button'

import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from './ui/breadcrumb'
import { Settings, Home, LogOut } from 'lucide-react'

interface ConsoleHeaderProps {
  title?: string
  description?: string
  actions?: React.ReactNode
  className?: string
}

export default function ConsoleHeader({
  title = "后台管理",
  description,
  actions,
  className = ''
}: ConsoleHeaderProps) {
  const pathname = usePathname()
  const { data: session } = useSession()

  // 生成面包屑导航
  const generateBreadcrumbs = () => {
    const pathSegments = pathname?.split('/').filter(Boolean) || []
    const breadcrumbs = []

    // 首页链接
    breadcrumbs.push({
      href: '/',
      label: '首页',
      isHome: true,
      target: '_blank'
    })

    // 控制台根目录
    if (pathSegments.length > 0 && pathSegments[0] === 'console') {
      breadcrumbs.push({
        href: '/console',
        label: '控制台'
      })

      // 子页面
      if (pathSegments.length > 1) {
        const subPage = pathSegments[1]
        const pageLabels: Record<string, string> = {
          'keywords': '关键词管理',
          'upload': 'CSV导入',
          'analyze': 'AI分析'
        }

        breadcrumbs.push({
          href: `/console/${subPage}`,
          label: pageLabels[subPage] || subPage,
          isCurrent: true
        })
      }
    }

    return breadcrumbs
  }

  const breadcrumbs = generateBreadcrumbs()

  return (
    <header className={`border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 ${className}`}>
      <div className="flex h-16 items-center px-4">
        {/* Logo和标识 */}
        <div className="flex items-center space-x-4 mr-6">
          <div className="flex items-center space-x-2">
            <Settings className="h-6 w-6 text-primary sparkles-smooth sparkles-delay-2" />
            <span className="font-bold text-lg">CatchIdeas</span>
          </div>
          <Badge className="bg-secondary/10 text-secondary border-secondary/20">
            Console
          </Badge>
        </div>

        {/* 面包屑导航 */}
        <Breadcrumb className="flex-1">
          <BreadcrumbList>
            {breadcrumbs.map((crumb, index) => (
              <div key={crumb.href} className="flex items-center">
                {index > 0 && <BreadcrumbSeparator />}
                <BreadcrumbItem>
                  {crumb.isCurrent ? (
                    <BreadcrumbPage className="font-medium">
                      {crumb.label}
                    </BreadcrumbPage>
                  ) : (
                    <BreadcrumbLink asChild>
                      <Link
                        href={crumb.href}
                        className="flex items-center"
                        target={crumb.target}
                        rel={crumb.target === '_blank' ? 'noopener noreferrer' : undefined}
                      >
                        {crumb.isHome && <Home className="h-3 w-3 mr-1" />}
                        {crumb.label}
                      </Link>
                    </BreadcrumbLink>
                  )}
                </BreadcrumbItem>
              </div>
            ))}
          </BreadcrumbList>
        </Breadcrumb>

        {/* 操作按钮区域 */}
        <div className="ml-auto flex items-center space-x-4">
          {actions && (
            <div className="flex items-center space-x-2">
              {actions}
            </div>
          )}

          {/* 退出按钮 */}
          {session && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                // 清除所有相关缓存和存储
                if (typeof window !== 'undefined') {
                  // 清除localStorage
                  localStorage.clear()
                  // 清除sessionStorage
                  sessionStorage.clear()
                  // 清除所有cookies
                  document.cookie.split(";").forEach(function(c) {
                    document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/");
                  })
                }
                // NextAuth退出
                signOut({ callbackUrl: '/' })
              }}
              className="btn-outline-enhanced text-destructive border-destructive/30 hover:bg-destructive/5 hover:border-destructive/50 hover:text-destructive"
            >
              <LogOut className="h-4 w-4 mr-2" />
              <span className="hidden sm:inline font-medium">退出登录</span>
              <span className="sm:hidden font-medium">退出</span>
            </Button>
          )}


        </div>
      </div>

      {/* 页面标题和描述 */}
      {(title || description) && (
        <div className="border-t bg-muted/30 px-4 py-3">
          <div className="flex items-center justify-between">
            <div>
              {title && (
                <h1 className="text-xl font-semibold text-foreground">{title}</h1>
              )}
              {description && (
                <p className="text-sm text-muted-foreground mt-1">{description}</p>
              )}
            </div>
          </div>
        </div>
      )}
    </header>
  )
}
