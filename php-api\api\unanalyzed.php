<?php
require_once '../utils/Response.php';
require_once '../models/Keyword.php';

/**
 * 未分析关键词API
 * 获取需要AI分析的关键词列表
 */

// 处理CORS预检请求
Response::handleOptions();

// 验证请求方法
$method = Response::validateMethod(['GET']);

try {
    $keywordModel = new Keyword();
    
    switch ($method) {
        case 'GET':
            handleGetUnanalyzed($keywordModel);
            break;
    }
    
} catch (Exception $e) {
    Response::serverError('操作失败: ' . $e->getMessage());
}

/**
 * 处理获取未分析关键词请求
 */
function handleGetUnanalyzed($keywordModel) {
    // 获取查询参数
    $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 50;
    $limit = max(1, min($limit, 200)); // 限制在1-200之间
    
    // 获取未分析的关键词
    $unanalyzedKeywords = $keywordModel->getUnanalyzedKeywords($limit);

    // 获取统计信息
    $stats = $keywordModel->getStats();


    // 格式化响应数据
    $response = [
        'keywords' => $unanalyzedKeywords,
        'statistics' => [
            'total_keywords' => (int)$stats['total'],
            'analyzed_keywords' => (int)$stats['analyzed'],
            'unanalyzed_keywords' => (int)$stats['unanalyzed'],
            'categorized_keywords' => (int)$stats['categorized'],
            'analysis_rate' => $stats['total'] > 0 
                ? round(($stats['analyzed'] / $stats['total']) * 100, 2) 
                : 0,
            'current_batch_size' => count($unanalyzedKeywords),
            'requested_limit' => $limit
        ],
        'meta' => [
            'has_more' => count($unanalyzedKeywords) >= $limit,
            'next_batch_available' => (int)$stats['unanalyzed'] > $limit,
            'estimated_remaining' => max(0, (int)$stats['unanalyzed'] - $limit)
        ]
    ];
    
    $message = count($unanalyzedKeywords) > 0 
        ? "获取到 " . count($unanalyzedKeywords) . " 个待分析关键词"
        : "没有待分析的关键词";
    
    Response::success($response, $message);
}
?>
