<?php
require_once '../utils/Response.php';
require_once '../config/database.php';

/**
 * 相邻关键词API
 * 获取指定关键词的前一个和后一个关键词
 */

// 处理CORS预检请求
Response::handleOptions();

// 验证请求方法
$method = Response::validateMethod(['GET']);

try {
    $database = new Database();
    $conn = $database->getConnection();

    switch ($method) {
        case 'GET':
            handleGet($conn);
            break;
    }

} catch (Exception $e) {
    Response::serverError('操作失败: ' . $e->getMessage());
}

/**
 * 处理GET请求 - 获取相邻关键词
 */
function handleGet($conn) {
    // 获取参数
    $keyword_id = $_GET['keyword_id'] ?? '';

    if (empty($keyword_id)) {
        Response::error('缺少关键词ID参数', 400);
    }

    // 首先验证当前关键词是否存在
    $stmt = $conn->prepare("SELECT id, created_at FROM keywords WHERE id = ?");
    $stmt->execute([$keyword_id]);
    $current_keyword = $stmt->fetch();

    if (!$current_keyword) {
        Response::error('关键词不存在', 404);
    }

    $current_created_at = $current_keyword['created_at'];

    // 获取前一个关键词（按创建时间排序，比当前关键词早创建的最新一个）
    $prev_stmt = $conn->prepare("
        SELECT id, keyword
        FROM keywords
        WHERE created_at < ?
        ORDER BY created_at DESC
        LIMIT 1
    ");
    $prev_stmt->execute([$current_created_at]);
    $prev_keyword = $prev_stmt->fetch();

    // 获取后一个关键词（按创建时间排序，比当前关键词晚创建的最早一个）
    $next_stmt = $conn->prepare("
        SELECT id, keyword
        FROM keywords
        WHERE created_at > ?
        ORDER BY created_at ASC
        LIMIT 1
    ");
    $next_stmt->execute([$current_created_at]);
    $next_keyword = $next_stmt->fetch();

    Response::success([
        'prev' => $prev_keyword ?: null,
        'next' => $next_keyword ?: null
    ], '获取相邻关键词成功');
}
?>
