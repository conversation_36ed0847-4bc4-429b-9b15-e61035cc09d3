{"name": "catchideas", "version": "3.23.0", "description": "基于Google Trends数据采集和SiliconFlow AI的智能关键词分析平台3.23", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "process-data": "node scripts/processData.js", "test-processor": "node scripts/testDataProcessor.js", "test-single": "node scripts/testDataProcessor.js single", "test-all": "node scripts/testDataProcessor.js all"}, "dependencies": {"@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@tailwindcss/postcss": "^4.1.10", "@tailwindcss/typography": "^0.5.16", "autoprefixer": "^10.4.21", "axios": "^1.6.2", "cheerio": "^1.0.0-rc.12", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "formidable": "^3.5.4", "jsdom": "^23.0.1", "lucide-react": "^0.523.0", "next": "^14.0.4", "next-auth": "^4.24.11", "next-themes": "^0.4.6", "postcss": "^8.5.6", "react": "^18.2.0", "react-dom": "^18.2.0", "rss-parser": "^3.13.0", "sonner": "^2.0.5", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.10", "uuid": "^11.1.0"}, "devDependencies": {"@testing-library/jest-dom": "^6.1.6", "@testing-library/react": "^14.1.2", "@types/node": "^20.10.5", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "eslint": "^8.56.0", "eslint-config-next": "^14.0.4", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "typescript": "^5.3.3"}, "keywords": ["nextjs", "keyword-analysis", "google-trends", "ai-analysis", "siliconflow", "data-collection", "mysql", "vercel"], "author": "CatchIdeas Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/yourusername/CatchIdeas.git"}, "engines": {"node": ">=18.17.0", "npm": ">=9.0.0"}}