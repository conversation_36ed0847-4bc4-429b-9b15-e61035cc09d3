'use client'

import { useState, useRef } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card'
import { Button } from './ui/button'
import { Textarea } from './ui/textarea'
import { Progress } from './ui/progress'
import { Alert, AlertDescription } from './ui/alert'
import { Badge } from './ui/badge'
import {
  FileText,
  CheckCircle,
  XCircle,
  Loader2,
  Database,
  Filter,
  Type,
  Edit3,
  Trash2
} from 'lucide-react'

interface ImportResult {
  success: boolean
  data?: {
    parse: {
      totalLines: number
      totalKeywords: number
    }
    import: {
      source: string
      totalLines: number
      rawKeywords: number
      importedAt: string
    }
    aiFilter: {
      total: number
      accepted: number
      rejected: number
      passRate: string
      duplicateCount: number
      acceptedKeywords: string[]
    }
    database: {
      attempted: number
      imported: number
      duplicates: number
      errors: number
    }
  }
  error?: string
}

// SEO友好的slug ID生成函数
function generateSlugId(keyword: string): string {
  // 基础slug生成
  let slug = keyword
    .toLowerCase()
    .trim()
    // 移除特殊字符，保留字母、数字、空格、连字符
    .replace(/[^\w\s-]/g, '')
    // 将多个空格替换为单个连字符
    .replace(/\s+/g, '-')
    // 移除多个连续的连字符
    .replace(/-+/g, '-')
    // 移除开头和结尾的连字符
    .replace(/^-+|-+$/g, '');

  // 如果slug为空或太短，使用fallback
  if (!slug || slug.length < 2) {
    slug = `keyword-${Date.now()}`;
  }

  // 限制长度，避免过长的URL
  if (slug.length > 50) {
    slug = slug.substring(0, 50).replace(/-+$/, '');
  }

  return slug;
}

export default function ManualImportTab() {
  const [isProcessing, setIsProcessing] = useState(false)
  const [importResult, setImportResult] = useState<ImportResult | null>(null)
  const [processingStep, setProcessingStep] = useState('')
  const [processingProgress, setProcessingProgress] = useState(0)
  const [textInput, setTextInput] = useState('')
  const [importMethod, setImportMethod] = useState<'paste' | 'file'>('paste')
  const fileInputRef = useRef<HTMLInputElement>(null)

  // 处理进度模拟函数
  const startProcessingProgress = () => {
    const steps = [
      { progress: 45, message: '正在解析关键词格式...', delay: 500 },
      { progress: 65, message: '正在过滤和去重...', delay: 800 },
      { progress: 85, message: '正在保存到数据库...', delay: 600 }
    ]

    let stepIndex = 0

    const interval = setInterval(() => {
      if (stepIndex < steps.length) {
        const step = steps[stepIndex]
        setProcessingProgress(step.progress)
        setProcessingStep(step.message)
        stepIndex++
      }
    }, 800)

    return interval
  }

  // 处理文件上传
  const handleFileUpload = async (file: File) => {
    if (!file) return

    // 验证文件类型
    if (!file.name.toLowerCase().endsWith('.txt')) {
      setImportResult({
        success: false,
        error: '请选择TXT格式文件'
      })
      return
    }

    try {
      const text = await file.text()
      setTextInput(text)
      setImportMethod('file')
    } catch {
      setImportResult({
        success: false,
        error: '文件读取失败'
      })
    }
  }

  // 处理手动导入
  const handleManualImport = async () => {
    if (!textInput.trim()) {
      setImportResult({
        success: false,
        error: '请输入关键词内容'
      })
      return
    }

    setIsProcessing(true)
    setImportResult(null)
    setProcessingProgress(0)
    setProcessingStep('开始处理文本...')

    try {
      // 步骤1: 解析文本
      setProcessingStep('正在解析文本内容...')
      setProcessingProgress(10)

      // 解析关键词
      const lines = textInput.split(/[\n\r]+/).map(line => line.trim()).filter(line => line)
      
      if (lines.length === 0) {
        throw new Error('未找到有效的关键词')
      }

      if (lines.length > 1000) {
        throw new Error('关键词数量超过限制（最多1000个）')
      }

      // 步骤2: 准备数据
      setProcessingStep('正在准备关键词数据...')
      setProcessingProgress(20)

      const keywords = lines.map((line) => ({
        id: generateSlugId(line),
        keyword: line,
        source: 'manual_import',
        importDate: new Date().toISOString().split('T')[0],
        importedAt: new Date().toISOString()
      }))

      // 步骤3: 开始过滤处理
      setProcessingStep('正在过滤和去重关键词...')
      setProcessingProgress(30)

      // 启动处理进度模拟
      const progressInterval = startProcessingProgress()

      const response = await fetch('/api/trends/manual-import', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          keywords: keywords,
          source: importMethod === 'file' ? 'txt_file' : 'manual_paste'
        })
      })

      // 清除进度模拟
      clearInterval(progressInterval)

      // 检查响应状态
      if (!response.ok) {
        let errorMessage = '服务器错误'
        try {
          const contentType = response.headers.get('content-type')
          if (contentType && contentType.includes('application/json')) {
            const errorData = await response.json()
            errorMessage = errorData.error || errorData.message || '服务器错误'
          } else {
            errorMessage = '服务暂时不可用，请稍后重试'
          }
        } catch {
          // 解析错误响应失败，使用默认错误信息
        }
        throw new Error(errorMessage)
      }

      // 检查响应类型
      const contentType = response.headers.get('content-type')
      if (!contentType || !contentType.includes('application/json')) {
        throw new Error('服务器返回格式错误')
      }

      const result = await response.json()

      // 步骤4: 完成处理
      setProcessingStep('处理完成！')
      setProcessingProgress(100)

      if (result.success) {
        setImportResult(result)
      } else {
        throw new Error(result.error || '导入失败')
      }

    } catch (error) {
      setImportResult({
        success: false,
        error: error instanceof Error ? error.message : '导入过程中发生未知错误'
      })
    } finally {
      setIsProcessing(false)
      setProcessingProgress(0)
      setProcessingStep('')
    }
  }

  // 清空输入
  const clearInput = () => {
    setTextInput('')
    setImportResult(null)
  }



  return (
    <div className="space-y-6">
      {/* 导入方式选择 */}
      <Card className="data-card-enhanced">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Edit3 className="h-5 w-5 text-primary" />
            <span>选择导入方式</span>
          </CardTitle>
          <CardDescription>
            支持TXT文件上传或直接在线粘贴关键词
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* 方式选择按钮 */}
          <div className="flex space-x-4">
            <Button
              variant={importMethod === 'paste' ? 'default' : 'outline'}
              onClick={() => setImportMethod('paste')}
              className="flex items-center space-x-2"
            >
              <Type className="h-4 w-4" />
              <span>在线粘贴</span>
            </Button>
            <Button
              variant={importMethod === 'file' ? 'default' : 'outline'}
              onClick={() => setImportMethod('file')}
              className="flex items-center space-x-2"
            >
              <FileText className="h-4 w-4" />
              <span>TXT文件</span>
            </Button>
          </div>

          {/* 文件上传区域 */}
          {importMethod === 'file' && (
            <div className="space-y-4">
              <div
                className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-8 text-center hover:border-primary/50 transition-colors cursor-pointer"
                onClick={() => fileInputRef.current?.click()}
              >
                <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-lg font-medium mb-2">点击选择TXT文件</p>
                <p className="text-sm text-muted-foreground">
                  支持.txt格式，每行一个关键词
                </p>
              </div>
              <input
                ref={fileInputRef}
                type="file"
                accept=".txt"
                onChange={(e) => {
                  const file = e.target.files?.[0]
                  if (file) handleFileUpload(file)
                }}
                className="hidden"
              />
            </div>
          )}

          {/* 文本输入区域 */}
          {importMethod === 'paste' && (
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <label className="text-sm font-medium">关键词列表</label>
                <div className="flex space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={clearInput}
                    className="text-xs"
                  >
                    <Trash2 className="h-3 w-3 mr-1" />
                    清空
                  </Button>
                </div>
              </div>
              <Textarea
                value={textInput}
                onChange={(e) => setTextInput(e.target.value)}
                placeholder="请输入关键词，每行一个关键词"
                className="!min-h-[200px] !max-h-80 !h-auto font-mono text-sm resize-none overflow-y-auto border-2 focus:border-primary/50 [field-sizing:initial]"
                disabled={isProcessing}
                style={{
                  scrollbarWidth: 'thin',
                  scrollbarColor: 'rgb(203 213 225) transparent',
                  height: 'auto',
                  minHeight: '200px',
                  maxHeight: '320px'
                }}
              />
              <div className="flex justify-between text-xs text-muted-foreground">
                <span>
                  已输入 {textInput.split(/[\n\r]+/).filter(line => line.trim()).length} 个关键词
                </span>
                <span>最多支持1000个关键词</span>
              </div>
            </div>
          )}

          {/* 导入按钮 */}
          <div className="flex justify-center pt-4">
            <Button
              onClick={handleManualImport}
              disabled={isProcessing || !textInput.trim()}
              size="lg"
              className="px-8"
            >
              {isProcessing ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  处理中...
                </>
              ) : (
                <>
                  <Database className="h-4 w-4 mr-2" />
                  开始导入
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* 处理进度 */}
      {isProcessing && (
        <Card className="data-card-enhanced border-primary/30">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Loader2 className="h-5 w-5 animate-spin text-primary" />
              <span>正在处理关键词</span>
            </CardTitle>
            <CardDescription>
              {processingStep}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Progress value={processingProgress} className="w-full" />
              <div className="flex justify-between text-sm text-muted-foreground">
                <span>处理进度</span>
                <span>{processingProgress}%</span>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 导入结果 */}
      {importResult && (
        <Card className={`data-card-enhanced ${importResult.success ? 'border-success/30' : 'border-destructive/30'}`}>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              {importResult.success ? (
                <CheckCircle className="h-5 w-5 text-success" />
              ) : (
                <XCircle className="h-5 w-5 text-destructive" />
              )}
              <span>{importResult.success ? '导入完成' : '导入失败'}</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {importResult.success && importResult.data ? (
              <div className="space-y-6">
                {/* 导入统计 */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="text-center p-4 bg-muted/50 rounded-lg">
                    <div className="text-2xl font-bold text-primary">
                      {importResult.data.parse.totalKeywords}
                    </div>
                    <div className="text-sm text-muted-foreground">解析关键词</div>
                  </div>
                  <div className="text-center p-4 bg-muted/50 rounded-lg">
                    <div className="text-2xl font-bold text-success">
                      {importResult.data.aiFilter.accepted}
                    </div>
                    <div className="text-sm text-muted-foreground">有效关键词</div>
                  </div>
                  <div className="text-center p-4 bg-muted/50 rounded-lg">
                    <div className="text-2xl font-bold text-warning">
                      {importResult.data.aiFilter.duplicateCount || 0}
                    </div>
                    <div className="text-sm text-muted-foreground">重复关键词</div>
                  </div>
                  <div className="text-center p-4 bg-muted/50 rounded-lg">
                    <div className="text-2xl font-bold text-secondary">
                      {importResult.data.database.imported}
                    </div>
                    <div className="text-sm text-muted-foreground">成功入库</div>
                  </div>
                </div>

                {/* 过滤详情 */}
                <div>
                  <h4 className="font-semibold mb-3 flex items-center space-x-2">
                    <Filter className="h-4 w-4" />
                    <span>处理详情</span>
                    <Badge variant="outline" className="bg-success/10 text-success">
                      有效率 {((importResult.data.database.imported / importResult.data.parse.totalKeywords) * 100).toFixed(1)}%
                    </Badge>
                  </h4>

                  <div className="space-y-3">
                    <div className="text-sm text-muted-foreground">
                      共处理 {importResult.data.parse.totalKeywords} 个关键词，
                      最终成功入库 {importResult.data.database.imported} 个，
                      有效率为 {((importResult.data.database.imported / importResult.data.parse.totalKeywords) * 100).toFixed(1)}%。
                      其中 {importResult.data.aiFilter.duplicateCount || 0} 个重复，
                      {importResult.data.database.duplicates} 个数据库重复跳过，
                      {importResult.data.database.errors} 个导入错误。
                    </div>
                  </div>
                </div>

                {/* 数据库操作结果 */}
                <div>
                  <h4 className="font-semibold mb-3 flex items-center space-x-2">
                    <Database className="h-4 w-4" />
                    <span>数据库操作</span>
                  </h4>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                    <div className="flex justify-between p-3 bg-muted/50 rounded">
                      <span>尝试导入：</span>
                      <span className="font-medium">{importResult.data.database.attempted}</span>
                    </div>
                    <div className="flex justify-between p-3 bg-muted/50 rounded">
                      <span>重复跳过：</span>
                      <span className="font-medium text-warning">{importResult.data.database.duplicates}</span>
                    </div>
                    <div className="flex justify-between p-3 bg-muted/50 rounded">
                      <span>导入错误：</span>
                      <span className="font-medium text-destructive">{importResult.data.database.errors}</span>
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <Alert className="border-destructive/30">
                <XCircle className="h-4 w-4" />
                <AlertDescription>
                  {importResult.error || '导入过程中发生未知错误'}
                </AlertDescription>
              </Alert>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  )
}
