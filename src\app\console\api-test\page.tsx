'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../../components/ui/card'
import { Button } from '../../../components/ui/button'
import { Skeleton } from '../../../components/ui/skeleton'
import { Alert, AlertDescription } from '../../../components/ui/alert'
import { Toaster, toast } from 'sonner'
import {
  Key,
  Search,
  DollarSign,
  CheckCircle,
  XCircle,
  Loader2,
  Info,
  Zap,
  Globe,
  ExternalLink,
  Copy
} from 'lucide-react'
import ConsoleLayout from '../../../components/console-layout'

interface SiliconFlowBalance {
  keyIndex: number
  keyName: string
  balance: string
  totalBalance: string
  chargeBalance: string
  status: 'success' | 'error' | 'loading'
  error?: string
}

interface GoogleKeyStatus {
  keyIndex: number
  keyName: string
  apiKey: string
  searchCx: string
  status: 'success' | 'error' | 'loading'
  error?: string
  testResult?: unknown
  testUrl?: string
}

export default function ApiTestPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  
  const [siliconFlowBalances, setSiliconFlowBalances] = useState<SiliconFlowBalance[]>([])
  const [googleKeyStatuses, setGoogleKeyStatuses] = useState<GoogleKeyStatus[]>([])
  const [loading, setLoading] = useState(false)

  // 设置页面标题
  useEffect(() => {
    document.title = 'API测试中心 - CatchIdeas'
  }, [])

  useEffect(() => {
    if (status === 'loading') return

    if (!session) {
      router.push('/auth/signin')
      return
    }

    // 检查是否为管理员
    const isAdmin = session.user?.email === '<EMAIL>'
    if (!isAdmin) {
      router.push('/console/analyze')
      return
    }
  }, [session, status, router])

  // 生成Google API测试链接
  const generateGoogleTestLinks = async () => {
    try {
      const response = await fetch('/api/test/google-links', {
        method: 'GET',
        headers: {
          'Cache-Control': 'no-cache'
        }
      })

      const data = await response.json()

      if (data.success) {
        setGoogleKeyStatuses(data.links)
      }
    } catch (error) {
      // 静默处理错误
    }
  }

  // 初始化密钥列表
  useEffect(() => {
    // 初始化硅基流动密钥状态
    const siliconKeys = Array.from({ length: 10 }, (_, i) => ({
      keyIndex: i + 1,
      keyName: `SILICONFLOW_API_KEY_${i + 1}`,
      balance: '',
      totalBalance: '',
      chargeBalance: '',
      status: 'loading' as const
    }))
    setSiliconFlowBalances(siliconKeys)

    // 生成Google测试链接
    generateGoogleTestLinks()
  }, [])

  // 查询硅基流动余额
  const checkSiliconFlowBalances = async () => {
    setLoading(true)
    toast.info('开始查询硅基流动API余额...')

    try {
      const response = await fetch('/api/test/siliconflow-balance', {
        method: 'GET',
        headers: {
          'Cache-Control': 'no-cache'
        }
      })

      const data = await response.json()

      if (data.success) {
        setSiliconFlowBalances(data.balances)
        toast.success(`成功查询 ${data.balances.filter((b: { status: string }) => b.status === 'success').length} 个有效密钥`)
      } else {
        toast.error(data.error || '查询失败')
      }
    } catch (error) {
      toast.error('网络错误，请稍后重试')
    } finally {
      setLoading(false)
    }
  }



  if (status === 'loading') {
    return <div className="min-h-screen flex items-center justify-center">加载中...</div>
  }

  if (!session) {
    return null
  }

  return (
    <ConsoleLayout title="API测试中心">
      <div className="space-y-8">
        {/* 功能概览 */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card className="data-card-enhanced stats-card-primary">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-primary/80">硅基流动</CardTitle>
              <Zap className="h-5 w-5 text-primary" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">AI模型</div>
              <p className="text-xs text-muted-foreground">
                10个API密钥余额查询
              </p>
            </CardContent>
          </Card>

          <Card className="data-card-enhanced stats-card-success">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-success/80">Google搜索</CardTitle>
              <Globe className="h-5 w-5 text-success" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">搜索API</div>
              <p className="text-xs text-muted-foreground">
                10个搜索密钥状态测试
              </p>
            </CardContent>
          </Card>

          <Card className="data-card-enhanced">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">API监控</CardTitle>
              <Key className="h-5 w-5 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">实时检测</div>
              <p className="text-xs text-muted-foreground">
                密钥状态实时监控
              </p>
            </CardContent>
          </Card>
        </div>

        {/* 操作按钮区域 */}
        <div className="console-gradient-section p-6">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h3 className="text-lg font-semibold text-foreground">API密钥测试</h3>
              <p className="text-sm text-muted-foreground">检查所有API密钥的状态和余额</p>
            </div>
            <div className="flex space-x-3">
              <Button
                onClick={checkSiliconFlowBalances}
                disabled={loading}
                className="btn-primary-enhanced"
              >
                {loading ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <DollarSign className="h-4 w-4 mr-2" />
                )}
                查询硅基余额
              </Button>
            </div>
          </div>
        </div>

        {/* 硅基流动余额查询结果 */}
        <Card className="data-card-enhanced">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Zap className="h-5 w-5 text-primary" />
              <span>硅基流动API余额</span>
            </CardTitle>
            <CardDescription>
              查询所有硅基流动API密钥的余额信息
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {siliconFlowBalances.map((balance) => (
                <div
                  key={balance.keyIndex}
                  className="p-4 border rounded-lg bg-card hover:bg-muted/50 transition-colors"
                >
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium">密钥 {balance.keyIndex}</span>
                    {balance.status === 'loading' ? (
                      <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
                    ) : balance.status === 'success' ? (
                      <CheckCircle className="h-4 w-4 text-success" />
                    ) : (
                      <XCircle className="h-4 w-4 text-destructive" />
                    )}
                  </div>
                  
                  {balance.status === 'success' ? (
                    <div className="space-y-1">
                      <div className="text-xs text-muted-foreground">可用余额</div>
                      <div className="text-lg font-bold text-primary">¥{balance.balance}</div>
                      <div className="text-xs text-muted-foreground">
                        总余额: ¥{balance.totalBalance}
                      </div>
                    </div>
                  ) : balance.status === 'error' ? (
                    <div className="text-xs text-destructive">
                      {balance.error || '查询失败'}
                    </div>
                  ) : (
                    <Skeleton className="h-12 w-full" />
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Google搜索API测试结果 */}
        <Card className="data-card-enhanced">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Globe className="h-5 w-5 text-success" />
              <span>Google搜索API状态</span>
            </CardTitle>
            <CardDescription>
              点击测试链接在浏览器中直接验证Google搜索API密钥的可用性
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {googleKeyStatuses.map((keyStatus) => (
                <div
                  key={keyStatus.keyIndex}
                  className="p-4 border rounded-lg bg-card hover:bg-muted/50 transition-colors"
                >
                  <div className="flex items-center justify-between mb-3">
                    <span className="text-sm font-medium">密钥 {keyStatus.keyIndex}</span>
                    {keyStatus.testUrl ? (
                      <CheckCircle className="h-4 w-4 text-success" />
                    ) : (
                      <XCircle className="h-4 w-4 text-destructive" />
                    )}
                  </div>

                  {keyStatus.testUrl ? (
                    <div className="space-y-3">
                      <div className="space-y-1">
                        <div className="text-xs text-muted-foreground">API密钥</div>
                        <div className="text-xs font-mono bg-muted p-1 rounded">
                          {keyStatus.apiKey}
                        </div>
                      </div>
                      <div className="space-y-1">
                        <div className="text-xs text-muted-foreground">搜索引擎ID</div>
                        <div className="text-xs font-mono bg-muted p-1 rounded">
                          {keyStatus.searchCx}
                        </div>
                      </div>
                      <div className="flex space-x-2">
                        <Button
                          onClick={() => window.open(keyStatus.testUrl, '_blank')}
                          size="sm"
                          className="flex-1"
                        >
                          <ExternalLink className="h-3 w-3 mr-1" />
                          测试链接
                        </Button>
                        <Button
                          onClick={() => {
                            navigator.clipboard.writeText(keyStatus.testUrl || '')
                            toast.success('链接已复制到剪贴板')
                          }}
                          size="sm"
                          variant="outline"
                        >
                          <Copy className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <div className="space-y-2">
                      <div className="text-xs text-destructive">
                        {keyStatus.error || '密钥未配置'}
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* 使用说明 */}
        <Alert className="border-primary/30 bg-card border-2">
          <Info className="h-4 w-4 text-primary" />
          <AlertDescription className="text-foreground">
            <strong>使用说明：</strong>
            <br />
            • 硅基流动余额查询：检查所有API密钥的可用余额，确保AI功能正常运行
            <br />
            • Google搜索API测试：点击"测试链接"在浏览器中直接验证API密钥，或复制链接手动测试
            <br />
            • <strong>测试查询：</strong>固定使用"catchideas.com"作为测试关键词
            <br />
            • <strong>Google API限制：</strong>免费版每天100次查询，每秒最多1次请求
            <br />
            • <strong>测试结果：</strong>成功返回JSON数据表示API可用，错误信息表示配置问题或配额不足
            <br />
            <br />
            <strong>Google API配置指南：</strong>
            <br />
            • <strong>购买账户：</strong>
            <a href="https://www.haixiaohao.com/product" target="_blank" rel="noopener noreferrer" className="text-primary hover:text-primary/80 underline ml-1">
              https://www.haixiaohao.com/product <ExternalLink className="h-3 w-3 inline ml-1" />
            </a>
            <br />
            • <strong>创建APIKey：</strong>
            <a href="https://console.cloud.google.com/apis/credentials" target="_blank" rel="noopener noreferrer" className="text-primary hover:text-primary/80 underline ml-1">
              https://console.cloud.google.com/apis/credentials <ExternalLink className="h-3 w-3 inline ml-1" />
            </a>
            <br />
            • <strong>创建CSEID：</strong>
            <a href="https://cse.google.com/cse/all" target="_blank" rel="noopener noreferrer" className="text-primary hover:text-primary/80 underline ml-1">
              https://cse.google.com/cse/all <ExternalLink className="h-3 w-3 inline ml-1" />
            </a>
            <br />
            • <strong>启用API服务：</strong>
            <a href="https://console.developers.google.com/apis/api/customsearch.googleapis.com/overview?project=" target="_blank" rel="noopener noreferrer" className="text-primary hover:text-primary/80 underline ml-1">
              https://console.developers.google.com/apis/api/customsearch.googleapis.com/overview?project= <ExternalLink className="h-3 w-3 inline ml-1" />
            </a>
          </AlertDescription>
        </Alert>
      </div>

      <Toaster position="top-right" richColors closeButton duration={5000} />
    </ConsoleLayout>
  )
}
