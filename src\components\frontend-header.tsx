'use client'

import Link from 'next/link'
import { useSession } from 'next-auth/react'
import { Badge } from './ui/badge'
import { Button } from './ui/button'
import { Sparkles, LogIn, Settings, Bookmark, TrendingUp, Target } from 'lucide-react'

interface FrontendHeaderProps {
  className?: string
}

export default function FrontendHeader({ className = '' }: FrontendHeaderProps) {
  const { data: session } = useSession()

  return (
    <header className={`sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 ${className}`}>
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          {/* Logo和品牌 */}
          <div className="flex items-center space-x-2">
            <Link href="/" className="flex items-center space-x-2 hover:opacity-80 transition-opacity">
              <Sparkles className="h-8 w-8 text-primary sparkles-smooth sparkles-delay-1" />
              <h1 className="text-2xl font-bold text-foreground">CatchIdeas</h1>
            </Link>
            <Badge className="bg-primary/10 text-primary border-primary/20">
              GA
            </Badge>
          </div>

          {/* 右侧按钮区域 - 仅PC端显示 */}
          <div className="hidden md:flex items-center space-x-3">
            {/* 书签导航按钮 */}
            <Button asChild variant="outline" className="btn-outline-enhanced">
              <Link href="/bookmarks">
                <Bookmark className="h-4 w-4 mr-2" />
                书签导航
              </Link>
            </Button>

            {session ? (
              // 已登录：显示后台管理按钮
              <Button asChild variant="outline" className="btn-outline-enhanced">
                <Link href="/console" target="_blank" rel="noopener noreferrer">
                  <Settings className="h-4 w-4 mr-2" />
                  后台管理
                </Link>
              </Button>
            ) : (
              // 未登录：显示登录按钮
              <Button asChild className="btn-primary-enhanced">
                <Link href="/auth/signin">
                  <LogIn className="h-4 w-4 mr-2" />
                  登录
                </Link>
              </Button>
            )}
          </div>
        </div>
      </div>
    </header>
  )
}
