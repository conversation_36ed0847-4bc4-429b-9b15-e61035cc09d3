export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ 
      success: false, 
      message: 'Method not allowed' 
    })
  }

  try {
    const { id } = req.body

    if (!id) {
      return res.status(400).json({
        success: false,
        message: '缺少关键词ID'
      })
    }

    const apiBaseUrl = process.env.NEXT_PUBLIC_PHP_API_URL || 'https://api.xstty.com/api'
    
    // 调用PHP API删除关键词
    const response = await fetch(`${apiBaseUrl}/keywords.php?id=${id}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      }
    })

    const data = await response.json()

    if (data.success) {
      res.status(200).json({
        success: true,
        message: '关键词删除成功'
      })
    } else {
      res.status(500).json({
        success: false,
        message: data.message || '删除失败'
      })
    }

  } catch (error) {
    console.error('Delete keyword API error:', error)
    res.status(500).json({
      success: false,
      message: '服务器内部错误: ' + error.message
    })
  }
}
