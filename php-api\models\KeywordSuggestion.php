<?php
require_once __DIR__ . '/../config/database.php';

class KeywordSuggestion {
    private $conn;
    private $table_name = "keyword_suggestions";

    public function __construct() {
        $database = new Database();
        $this->conn = $database->getConnection();
    }

    /**
     * 批量创建关键词建议
     */
    public function createBatch($keywordId, $suggestions, $source) {
        // 先删除该关键词的旧建议
        $this->deleteByKeywordId($keywordId);
        
        if (empty($suggestions)) {
            return true;
        }

        $query = "INSERT INTO " . $this->table_name . " 
                  (keyword_id, suggestion, source) 
                  VALUES ";
        
        $values = [];
        $params = [];
        
        foreach ($suggestions as $index => $suggestion) {
            $values[] = "(:keyword_id_$index, :suggestion_$index, :source_$index)";
            $params[":keyword_id_$index"] = $keywordId;
            $params[":suggestion_$index"] = trim($suggestion);
            $params[":source_$index"] = $source;
        }
        
        $query .= implode(', ', $values);
        
        $stmt = $this->conn->prepare($query);
        
        foreach ($params as $key => $value) {
            $stmt->bindValue($key, $value);
        }
        
        return $stmt->execute();
    }

    /**
     * 根据关键词ID获取建议
     */
    public function getByKeywordId($keywordId) {
        $query = "SELECT * FROM " . $this->table_name . " 
                  WHERE keyword_id = :keyword_id 
                  ORDER BY created_at DESC";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":keyword_id", $keywordId);
        $stmt->execute();
        
        return $stmt->fetchAll();
    }

    /**
     * 删除关键词的所有建议
     */
    public function deleteByKeywordId($keywordId) {
        $query = "DELETE FROM " . $this->table_name . " WHERE keyword_id = :keyword_id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":keyword_id", $keywordId);
        return $stmt->execute();
    }

    /**
     * 检查关键词是否已有建议
     */
    public function hasKeywordSuggestions($keywordId) {
        $query = "SELECT COUNT(*) as count FROM " . $this->table_name . " 
                  WHERE keyword_id = :keyword_id";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":keyword_id", $keywordId);
        $stmt->execute();
        
        $result = $stmt->fetch();
        return $result['count'] > 0;
    }

    /**
     * 获取建议统计
     */
    public function getStats($keywordId) {
        $query = "SELECT 
                    source,
                    COUNT(*) as count
                  FROM " . $this->table_name . " 
                  WHERE keyword_id = :keyword_id
                  GROUP BY source";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":keyword_id", $keywordId);
        $stmt->execute();
        
        return $stmt->fetchAll();
    }
}
