<?php
require_once '../utils/Response.php';
require_once '../models/Keyword.php';

/**
 * 关键词CRUD API
 * 处理关键词的增删改查操作
 */

// 处理CORS预检请求
Response::handleOptions();

// 验证请求方法
$method = Response::validateMethod(['GET', 'POST', 'PUT', 'DELETE']);

try {
    $keywordModel = new Keyword();
    
    switch ($method) {
        case 'GET':
            handleGet($keywordModel);
            break;
            
        case 'POST':
            handlePost($keywordModel);
            break;
            
        case 'PUT':
            handlePut($keywordModel);
            break;
            
        case 'DELETE':
            handleDelete($keywordModel);
            break;
    }
    
} catch (Exception $e) {
    Response::serverError('操作失败: ' . $e->getMessage());
}

/**
 * 处理GET请求 - 获取关键词列表或单个关键词
 */
function handleGet($keywordModel) {
    // 检查action参数
    $action = $_GET['action'] ?? 'list';

    switch ($action) {
        case 'daily_stats':
            handleDailyStats($keywordModel);
            break;

        case 'recent_activity':
            handleRecentActivity($keywordModel);
            break;

        case 'list':
        default:
            handleKeywordList($keywordModel);
            break;
    }
}

function handleKeywordList($keywordModel) {
    // 如果有ID参数，获取单个关键词
    if (isset($_GET['id'])) {
        $keyword = $keywordModel->getById($_GET['id']);
        if (!$keyword) {
            Response::notFound('关键词不存在');
        }
        Response::success($keyword);
    }

    // 获取关键词列表
    $pagination = Response::getPaginationParams();
    
    // 构建过滤条件
    $filters = [];
    if (isset($_GET['category']) && !empty($_GET['category'])) {
        $filters['category'] = $_GET['category'];
    }
    if (isset($_GET['source']) && !empty($_GET['source'])) {
        $filters['source'] = $_GET['source'];
    }
    if (isset($_GET['import_date']) && !empty($_GET['import_date'])) {
        $filters['import_date'] = $_GET['import_date'];
    }
    if (isset($_GET['search']) && !empty($_GET['search'])) {
        $filters['search'] = $_GET['search'];
    }
    if (isset($_GET['analyzed_only']) && $_GET['analyzed_only'] !== '') {
        $filters['analyzed_only'] = $_GET['analyzed_only'];
    }
    if (isset($_GET['competition_level']) && !empty($_GET['competition_level'])) {
        $filters['competition_level'] = $_GET['competition_level'];
    }
    if (isset($_GET['date_range']) && !empty($_GET['date_range'])) {
        $filters['date_range'] = $_GET['date_range'];
    }

    // 排序参数
    $sort_by = isset($_GET['sort_by']) ? $_GET['sort_by'] : 'created_at';
    $sort_order = isset($_GET['sort_order']) ? $_GET['sort_order'] : 'desc';
    
    // 获取数据
    $keywords = $keywordModel->getList($filters, $pagination['limit'], $pagination['offset'], $sort_by, $sort_order);
    $total = $keywordModel->getCount($filters);
    $statistics = $keywordModel->getStats();

    // 返回分页数据
    $response = Response::paginated($keywords, $total, $pagination['page'], $pagination['limit']);
    $response['statistics'] = $statistics;
    Response::success($response);
}

/**
 * 处理POST请求 - 创建新关键词
 */
function handlePost($keywordModel) {
    $data = Response::getRequestData();
    
    // 验证必需字段
    if (!isset($data['keyword']) || empty(trim($data['keyword']))) {
        Response::validationError(['keyword' => '关键词不能为空']);
    }
    
    $keyword = trim($data['keyword']);
    
    // 检查是否已存在
    if ($keywordModel->exists($keyword)) {
        Response::error('关键词已存在', 409);
    }
    
    // 准备数据
    $keywordData = [
        'id' => generateSlugId($keyword),
        'keyword' => $keyword,
        'user_intent' => $data['user_intent'] ?? null,
        'user_pain_point' => $data['user_pain_point'] ?? null,
        'competition_level' => $data['competition_level'] ?? null,
        'competition_score' => $data['competition_score'] ?? null,
        'competition_color' => $data['competition_color'] ?? null,
        'competition_description' => $data['competition_description'] ?? null,
        'category' => $data['category'] ?? null,
        'source' => $data['source'] ?? 'manual',
        'import_date' => date('Y-m-d')
    ];
    
    // 创建关键词
    $success = $keywordModel->create($keywordData);
    
    if (!$success) {
        Response::serverError('关键词创建失败');
    }
    
    // 获取创建的关键词
    $createdKeyword = $keywordModel->getById($keywordData['id']);
    Response::success($createdKeyword, '关键词创建成功', 201);
}

/**
 * 处理PUT请求 - 更新关键词
 */
function handlePut($keywordModel) {
    if (!isset($_GET['id'])) {
        Response::validationError(['id' => '关键词ID不能为空']);
    }
    
    $id = $_GET['id'];
    $data = Response::getRequestData();
    
    // 检查关键词是否存在
    $existingKeyword = $keywordModel->getById($id);
    if (!$existingKeyword) {
        Response::notFound('关键词不存在');
    }
    
    // 检查分类锁定状态
    if ($existingKeyword['category_locked'] && isset($data['category'])) {
        Response::error('该关键词的分类已锁定，无法修改', 403);
    }
    
    // 更新关键词
    $success = $keywordModel->update($id, $data);
    
    if (!$success) {
        Response::serverError('关键词更新失败');
    }
    
    // 获取更新后的关键词
    $updatedKeyword = $keywordModel->getById($id);
    Response::success($updatedKeyword, '关键词更新成功');
}

/**
 * 处理DELETE请求 - 删除关键词
 */
function handleDelete($keywordModel) {
    if (!isset($_GET['id'])) {
        Response::validationError(['id' => '关键词ID不能为空']);
    }
    
    $id = $_GET['id'];
    
    // 检查关键词是否存在
    $existingKeyword = $keywordModel->getById($id);
    if (!$existingKeyword) {
        Response::notFound('关键词不存在');
    }
    
    // 删除关键词
    $success = $keywordModel->delete($id);
    
    if (!$success) {
        Response::serverError('关键词删除失败');
    }
    
    Response::success(null, '关键词删除成功');
}

/**
 * 生成SEO友好的slug ID
 */
function generateSlugId($keyword) {
    // 基础slug生成
    $slug = strtolower(trim($keyword));

    // 移除特殊字符，只保留字母、数字、空格、连字符
    $slug = preg_replace('/[^\w\s\-]/u', '', $slug);

    // 将多个空格替换为单个连字符
    $slug = preg_replace('/\s+/', '-', $slug);

    // 移除多个连续的连字符
    $slug = preg_replace('/\-+/', '-', $slug);

    // 移除开头和结尾的连字符
    $slug = trim($slug, '-');

    // 如果slug为空或太短，使用fallback
    if (empty($slug) || strlen($slug) < 2) {
        $slug = 'keyword-' . time() . '-' . mt_rand(100, 999);
    }

    // 限制长度
    if (strlen($slug) > 50) {
        $slug = substr($slug, 0, 50);
        $slug = rtrim($slug, '-');
    }

    return $slug;
}

/**
 * 处理每日统计请求
 */
function handleDailyStats($keywordModel) {
    $date = $_GET['date'] ?? date('Y-m-d');

    try {
        $stats = $keywordModel->getDailyStats($date);
        Response::success($stats);
    } catch (Exception $e) {
        Response::error('获取每日统计失败: ' . $e->getMessage());
    }
}

/**
 * 处理最近活动请求
 */
function handleRecentActivity($keywordModel) {
    $limit = $_GET['limit'] ?? 5;

    try {
        $activities = $keywordModel->getRecentActivity($limit);
        Response::success($activities);
    } catch (Exception $e) {
        Response::error('获取最近活动失败: ' . $e->getMessage());
    }
}
?>
