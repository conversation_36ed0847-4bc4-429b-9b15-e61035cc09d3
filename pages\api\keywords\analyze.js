const aiProcessor = require('../../../lib/ai-processor')

/**
 * 关键词AI分析API
 * 提供前端调用的关键词分析接口
 */

export default async function handler(req, res) {
  // 设置CORS头
  res.setHeader('Access-Control-Allow-Origin', '*')
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type')

  // 处理预检请求
  if (req.method === 'OPTIONS') {
    res.status(200).end()
    return
  }

  try {
    switch (req.method) {
      case 'GET':
        await handleGetUnanalyzed(req, res)
        break
        
      case 'POST':
        await handleAnalyze(req, res)
        break
        
      default:
        res.status(405).json({
          success: false,
          error: '不支持的请求方法'
        })
    }
  } catch {
    res.status(500).json({
      success: false,
      error: '服务器内部错误: ' + error.message
    })
  }
}

/**
 * 获取未分析的关键词
 */
async function handleGetUnanalyzed(req, res) {
  try {
    const { limit = 50 } = req.query
    
    // 调用PHP API获取未分析关键词
    const apiUrl = `${process.env.NEXT_PUBLIC_PHP_API_URL}/unanalyzed.php?limit=${limit}`
    const response = await fetch(apiUrl)
    
    if (!response.ok) {
      throw new Error('PHP API错误')
    }
    
    const data = await response.json()
    
    if (!data.success) {
      throw new Error(data.message || 'PHP API返回错误')
    }
    
    res.status(200).json({
      success: true,
      data: data.data,
      message: data.message
    })
    
  } catch {
    res.status(500).json({
      success: false,
      error: '获取未分析关键词失败: ' + error.message
    })
  }
}

/**
 * 执行关键词分析
 */
async function handleAnalyze(req, res) {
  try {
    const { keywords, mode = 'batch' } = req.body
    
    if (!keywords || !Array.isArray(keywords) || keywords.length === 0) {
      return res.status(400).json({
        success: false,
        error: '关键词数组不能为空'
      })
    }
    
    if (mode === 'single') {
      await handleSingleAnalysis(keywords[0], res)
    } else {
      await handleBatchAnalysis(keywords, res)
    }
    
  } catch {
    res.status(500).json({
      success: false,
      error: '关键词分析失败: ' + error.message
    })
  }
}

/**
 * 单个关键词分析
 */
async function handleSingleAnalysis(keyword, res) {
  try {
    const result = await aiProcessor.analyzeKeyword(keyword.keyword || keyword)
    
    if (result.success) {
      // 保存分析结果到PHP API
      await saveAnalysisResult([{
        keyword: keyword.keyword || keyword,
        analysis: result.analysis
      }])
      
      res.status(200).json({
        success: true,
        data: {
          keyword: keyword.keyword || keyword,
          analysis: result.analysis
        },
        message: '关键词分析完成'
      })
    } else {
      res.status(400).json({
        success: false,
        error: result.error,
        data: {
          keyword: keyword.keyword || keyword
        }
      })
    }
    
  } catch (error) {
    throw error
  }
}

/**
 * 批量关键词分析 (高并发)
 */
async function handleBatchAnalysis(keywords, res) {
  try {
    // 使用DeepSeek-V3高质量批量分析
    const batchResults = await aiProcessor.analyzeKeywords(
      keywords.map(k => k.keyword || k),
      null, // 不需要进度回调，因为是批量返回
      2     // 并发数2，确保复杂分析的稳定性
    )

    const results = []
    const errors = []

    // 处理批量结果
    batchResults.forEach((result) => {
      if (result.success) {
        results.push({
          keyword: result.keyword,
          analysis: result.analysis,
          duckduckgoDebug: result.duckduckgoDebug // 传递调试信息
        })
      } else {
        errors.push({
          keyword: result.keyword,
          error: result.error
        })
      }
    })
    
    // 保存所有成功的分析结果
    if (results.length > 0) {
      try {
        await saveAnalysisResult(results)
      } catch {
        // 静默处理保存错误
      }
    }

    // 自动删除失败的关键词
    let deletionResult = { attempted: 0, deleted: 0, errors: [] }
    if (errors.length > 0) {
      try {
        deletionResult = await deleteFailedKeywords(errors.map(e => e.keyword))
      } catch (deleteError) {
        console.error('删除失败关键词时出错:', deleteError)
        // 静默处理删除错误，不影响主流程
      }
    }

    // 返回完成结果
    res.status(200).json({
      success: true,
      data: {
        summary: {
          total: keywords.length,
          successful: results.length,
          failed: errors.length,
          processed: results.length + errors.length
        },
        results: results,
        errors: errors,
        cleanup: deletionResult // 添加清理结果
      },
      message: `批量分析完成：成功 ${results.length} 个，失败 ${errors.length} 个${deletionResult.deleted > 0 ? `，已清理 ${deletionResult.deleted} 个失败关键词` : ''}`
    })
    
  } catch (error) {
    throw error
  }
}

/**
 * 保存分析结果到PHP API并生成推荐域名
 */
async function saveAnalysisResult(results) {
  try {
    // 1. 保存分析结果
    const apiUrl = `${process.env.NEXT_PUBLIC_PHP_API_URL}/analyze.php`
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        results: results,
        batch_id: `batch_${Date.now()}`
      })
    })

    if (!response.ok) {
      throw new Error('保存失败')
    }

    const data = await response.json()

    if (!data.success) {
      throw new Error(data.message || '保存分析结果失败')
    }

    // 分析结果已保存，无需额外处理

    return data

  } catch (error) {
    throw error
  }
}

/**
 * 删除分析失败的关键词
 */
async function deleteFailedKeywords(failedKeywords) {
  const result = { attempted: 0, deleted: 0, errors: [] }

  if (!failedKeywords || failedKeywords.length === 0) {
    return result
  }

  try {
    // 1. 通过关键词名称获取对应的ID
    const keywordIds = await getKeywordIdsByNames(failedKeywords)
    result.attempted = keywordIds.length

    if (keywordIds.length === 0) {
      result.errors.push('未找到对应的关键词ID')
      return result
    }

    // 2. 调用PHP批量删除API
    const deleteResponse = await fetch(`${process.env.NEXT_PUBLIC_PHP_API_URL}/batch-delete.php`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ ids: keywordIds })
    })

    if (!deleteResponse.ok) {
      result.errors.push(`删除API调用失败: ${deleteResponse.status}`)
      return result
    }

    const deleteData = await deleteResponse.json()

    if (deleteData.success) {
      result.deleted = keywordIds.length
    } else {
      result.errors.push(deleteData.message || '批量删除失败')
    }

  } catch (error) {
    result.errors.push(`删除失败关键词时出错: ${error.message}`)
  }

  return result
}

/**
 * 通过关键词名称获取对应的ID（支持UUID和manual-格式）
 */
async function getKeywordIdsByNames(keywordNames) {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_PHP_API_URL}/keywords-by-names.php`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ keywords: keywordNames })
    })

    if (!response.ok) {
      throw new Error('获取关键词ID失败')
    }

    const data = await response.json()

    if (data.success && data.data && Array.isArray(data.data)) {
      return data.data.map(item => item.id).filter(id => id)
    }

    return []

  } catch {
    return []
  }
}


