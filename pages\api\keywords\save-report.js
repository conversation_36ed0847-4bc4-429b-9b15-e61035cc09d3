export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      message: '请求不被允许'
    })
  }

  try {
    const { keyword_id, report_data } = req.body

    if (!keyword_id || !report_data) {
      return res.status(400).json({
        success: false,
        message: '关键词ID和报告数据不能为空'
      })
    }

    const apiBaseUrl = process.env.NEXT_PUBLIC_PHP_API_URL || 'https://api.xstty.com/api'

    // 提取关键信息用于数据库存储
    const reportSummary = {
      keyword_id: keyword_id,
      user_intent: extractUserIntent(report_data),
      user_pain_point: extractUserPainPoint(report_data),
      competition_level: extractCompetitionLevel(report_data),
      competition_score: extractCompetitionScore(report_data),
      competition_color: getCompetitionColor(extractCompetitionScore(report_data)),
      competition_description: extractCompetitionDescription(report_data),
      category: extractCategory(report_data),
      ai_analysis: JSON.stringify(report_data),
      analyzed_at: new Date().toISOString()
    }

    // 保存报告数据

    // 更新关键词分析结果
    const updateResponse = await fetch(`${apiBaseUrl}/keywords.php`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        action: 'update',
        id: keyword_id,
        ...reportSummary
      })
    })

    const updateData = await updateResponse.json()

    if (!updateData.success) {
      throw new Error(updateData.message || '保存报告失败')
    }

    res.json({
      success: true,
      message: '报告保存成功',
      data: {
        keyword_id,
        updated_fields: Object.keys(reportSummary),
        report_size: JSON.stringify(report_data).length
      }
    })

  } catch (error) {
    res.status(500).json({
      success: false,
      message: error.message || '保存报告失败'
    })
  }
}

// 提取用户意图
function extractUserIntent(reportData) {
  try {
    const intentSection = reportData.sections?.intent
    if (intentSection?.data?.intent_type) {
      return intentSection.data.intent_type
    }

    // 从算法分析中提取
    const algorithmSection = reportData.sections?.algorithms
    if (algorithmSection?.data?.title_patterns?.searchIntent?.type) {
      return algorithmSection.data.title_patterns.searchIntent.type
    }

    // 从算法结果中提取
    if (algorithmSection?.algorithm_results?.searchIntent?.type) {
      return algorithmSection.algorithm_results.searchIntent.type
    }

    return 'informational' // 默认值
  } catch (error) {
    return 'informational' // 默认值
  }
}

// 提取用户痛点
function extractUserPainPoint(reportData) {
  try {
    const contentSection = reportData.sections?.content
    if (contentSection?.data?.analysis) {
      return contentSection.data.analysis.substring(0, 500)
    }

    const intentSection = reportData.sections?.intent
    if (intentSection?.data?.analysis) {
      return intentSection.data.analysis.substring(0, 500)
    }

    const businessSection = reportData.sections?.business
    if (businessSection?.data?.analysis) {
      return businessSection.data.analysis.substring(0, 500)
    }

    // 生成默认痛点分析
    return '用户在搜索过程中面临信息获取困难、解决方案不明确等问题，需要提供专业、准确的内容来满足用户需求。'
  } catch (error) {
    return '用户在搜索过程中面临信息获取和问题解决的挑战，需要提供有价值的内容和解决方案。'
  }
}

// 提取竞争等级
function extractCompetitionLevel(reportData) {
  try {
    const competitionSection = reportData.sections?.competition
    if (competitionSection?.data?.intensity) {
      return competitionSection.data.intensity
    }

    // 从算法分析中提取
    const algorithmSection = reportData.sections?.algorithms
    if (algorithmSection?.data?.enhanced_seo_analysis?.level) {
      return algorithmSection.data.enhanced_seo_analysis.level
    }

    // 从算法结果中提取
    if (algorithmSection?.algorithm_results?.seoDifficulty?.level) {
      return algorithmSection.algorithm_results.seoDifficulty.level
    }

    return 'medium' // 默认值
  } catch (error) {
    return 'medium' // 默认值
  }
}

// 提取竞争评分
function extractCompetitionScore(reportData) {
  try {
    const competitionSection = reportData.sections?.competition
    if (competitionSection?.data?.intensity_score) {
      return competitionSection.data.intensity_score
    }

    // 从算法分析中提取
    const algorithmSection = reportData.sections?.algorithms
    if (algorithmSection?.data?.enhanced_seo_analysis?.score) {
      return algorithmSection.data.enhanced_seo_analysis.score
    }

    // 从算法结果中提取
    if (algorithmSection?.algorithm_results?.seoDifficulty?.score) {
      return algorithmSection.algorithm_results.seoDifficulty.score
    }

    return 50 // 默认值
  } catch (error) {
    return 50 // 默认值
  }
}

// 提取竞争描述
function extractCompetitionDescription(reportData) {
  try {
    const competitionSection = reportData.sections?.competition
    if (competitionSection?.data?.opportunity) {
      return competitionSection.data.opportunity.substring(0, 500)
    }

    if (competitionSection?.data?.analysis) {
      return competitionSection.data.analysis.substring(0, 500)
    }

    const algorithmSection = reportData.sections?.algorithms
    if (algorithmSection?.summary) {
      return `SEO难度: ${algorithmSection.summary.seo_difficulty || 50}, 商业价值: ${algorithmSection.summary.commercial_value || 6}`
    }

    return '基于算法分析，该关键词具有中等竞争强度，需要优质内容和适当的SEO策略来获得良好排名。'
  } catch (error) {
    return '竞争分析显示该关键词具有一定的市场机会，建议制定合适的SEO策略。'
  }
}

// 提取分类
function extractCategory(reportData) {
  try {
    const intentSection = reportData.sections?.intent
    if (intentSection?.data?.intent_type) {
      // 根据意图类型映射到分类
      const intentToCategory = {
        'transactional': 'Shopping',
        'informational': 'Reference',
        'navigational': 'Online Communities',
        'commercial': 'Business & Industrial'
      }
      return intentToCategory[intentSection.data.intent_type] || 'General'
    }

    return 'General'
  } catch (error) {
    return 'General'
  }
}

// 获取竞争颜色
function getCompetitionColor(score) {
  if (!score) return 'gray'
  
  if (score <= 30) return 'green'
  if (score <= 60) return 'orange'
  return 'red'
}
