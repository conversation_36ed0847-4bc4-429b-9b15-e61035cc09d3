/**
 * Google搜索API测试链接生成接口
 * 生成所有配置的Google搜索API测试链接
 */

export default async function handler(req, res) {
  // 设置CORS头
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  if (req.method === 'GET') {
    return handleGenerateLinks(req, res);
  }

  return res.status(405).json({
    success: false,
    error: '不支持的请求方法，请使用GET'
  });
}

/**
 * 处理生成Google API测试链接
 */
async function handleGenerateLinks(req, res) {
  try {
    const links = [];
    const testQuery = 'catchideas.com';

    // 生成20个Google搜索API测试链接
    for (let i = 1; i <= 20; i++) {
      const apiKey = process.env[`GOOGLE_SEARCH_API_KEY_${i}`];
      const searchCx = process.env[`GOOGLE_SEARCH_CX_${i}`];
      
      if (!apiKey || !searchCx) {
        links.push({
          keyIndex: i,
          keyName: `GOOGLE_SEARCH_API_KEY_${i}`,
          apiKey: apiKey || '',
          searchCx: searchCx || '',
          status: 'error',
          error: !apiKey ? '密钥未配置' : '搜索引擎ID未配置',
          testUrl: null
        });
        continue;
      }

      // 构建Google Custom Search API测试URL
      const testUrl = new URL('https://www.googleapis.com/customsearch/v1');
      testUrl.searchParams.append('key', apiKey);
      testUrl.searchParams.append('cx', searchCx);
      testUrl.searchParams.append('q', testQuery);
      testUrl.searchParams.append('num', '1'); // 只请求1个结果进行测试

      links.push({
        keyIndex: i,
        keyName: `GOOGLE_SEARCH_API_KEY_${i}`,
        apiKey: apiKey,
        searchCx: searchCx,
        status: 'success',
        testUrl: testUrl.toString()
      });
    }

    return res.status(200).json({
      success: true,
      links: links,
      testQuery: testQuery,
      summary: {
        total: links.length,
        configured: links.filter(l => l.testUrl).length,
        missing: links.filter(l => !l.testUrl).length
      }
    });

  } catch {
    return res.status(500).json({
      success: false,
      error: '服务器内部错误'
    });
  }
}
