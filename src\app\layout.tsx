import type { Metadata } from "next";
// import { Inter, JetBrains_Mono } from "next/font/google";
import "./globals.css";
import AuthSessionProvider from "../components/session-provider";

// 临时禁用Google Fonts以解决构建问题
// const inter = Inter({
//   variable: "--font-inter",
//   subsets: ["latin"],
// });

// const jetbrainsMono = JetBrains_Mono({
//   variable: "--font-jetbrains-mono",
//   subsets: ["latin"],
// });

export const metadata: Metadata = {
  title: "关键词需求分析 - 发现有价值的互联网项目 | CatchIdeas",
  description: "专注谷歌趋势关键词分析，识别用户真实需求，推荐可执行的网站、应用项目。为产品人员、技术创业者提供基于数据的项目选择指导。",
  authors: [{ name: "CatchIdeas团队" }],
  creator: "CatchIdeas",
  publisher: "CatchIdeas",
  robots: "index, follow",
  openGraph: {
    type: "website",
    locale: "zh_CN",
    url: "https://catchideas.com",
    title: "关键词需求分析 - 发现有价值的互联网项目 | CatchIdeas",
    description: "专注谷歌趋势关键词分析，识别用户真实需求，推荐可执行的网站、应用项目。为产品人员、技术创业者提供基于数据的项目选择指导。",
    siteName: "CatchIdeas",
  },
  twitter: {
    card: "summary_large_image",
    title: "关键词需求分析 - 发现有价值的互联网项目 | CatchIdeas",
    description: "专注谷歌趋势关键词分析，识别用户真实需求，推荐可执行的网站、应用项目。为产品人员、技术创业者提供基于数据的项目选择指导。",
  },
  alternates: {
    canonical: "https://catchideas.com",
    languages: {
      'zh-CN': 'https://catchideas.com',
    },
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="zh-CN" suppressHydrationWarning>
      <head>
        <link rel="icon" href="/favicon.ico" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <meta name="theme-color" content="#3b82f6" />
        <script async data-cfasync="false" src="//pl27211408.profitableratecpm.com/dab3873d7045ab55c32b055616093330/invoke.js"></script>
      </head>
      <body
        className="antialiased"
      >
        <AuthSessionProvider>
          {children}
        </AuthSessionProvider>
        <div id="container-dab3873d7045ab55c32b055616093330"></div>
      </body>
    </html>
  );
}
