import NextAuth from "next-auth";
import GoogleProvider from "next-auth/providers/google";

const adminEmails = ["honghai<PERSON><EMAIL>"]; // 管理员邮箱列表

export const authOptions = {
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
      authorization: {
        params: {
          prompt: "select_account", // 强制显示账号选择页面
          access_type: "offline",
          response_type: "code"
        }
      }
    }),
  ],
  pages: {
    signIn: '/auth/signin', // 自定义登录页面
  },
  session: {
    strategy: "jwt" as const,
    maxAge: 7 * 24 * 60 * 60, // 7天
    updateAge: 24 * 60 * 60, // 24小时更新一次
  },
  jwt: {
    maxAge: 7 * 24 * 60 * 60, // 7天
  },
  callbacks: {
    async signIn({ user }: any) {
      // 允许所有Google用户登录
      return true;
    },
    async jwt({ token, user }: any) {
      // 首次登录时将用户信息添加到token
      if (user) {
        token.role = adminEmails.includes(user.email) ? "admin" : "user";
        token.email = user.email;
        token.name = user.name;
        token.picture = user.image;
      }
      return token;
    },
    async session({ session, token }: any) {
      // 将token中的信息传递给session
      session.user.role = token.role;
      session.user.email = token.email;
      session.user.name = token.name;
      session.user.image = token.picture;
      return session;
    },
  },
  // 安全配置
  secret: process.env.NEXTAUTH_SECRET,
  cookies: {
    sessionToken: {
      name: `next-auth.session-token`,
      options: {
        httpOnly: true,
        sameSite: 'lax',
        path: '/',
        secure: process.env.NODE_ENV === 'production',
        maxAge: 7 * 24 * 60 * 60, // 7天
      },
    },
    csrfToken: {
      name: `next-auth.csrf-token`,
      options: {
        httpOnly: true,
        sameSite: 'lax',
        path: '/',
        secure: process.env.NODE_ENV === 'production',
      },
    },
  },
  // 启用调试模式（仅开发环境）
  debug: process.env.NODE_ENV === 'development',
  // 事件处理
  events: {
    async signIn({ user, account, profile }: any) {
      console.log(`用户登录: ${user.email} via ${account?.provider}`)
    },
    async signOut({ session, token }: any) {
      console.log(`用户登出: ${session?.user?.email || token?.email}`)
    },
    async session({ session, token }: any) {
      // 可以在这里记录session活动
      return session
    },
  },
};

export default NextAuth(authOptions);
