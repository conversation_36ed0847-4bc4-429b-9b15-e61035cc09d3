import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const { keyword, count = 10 } = await request.json()

    if (!keyword) {
      return NextResponse.json({
        success: false,
        error: '缺少关键词参数'
      }, { status: 400 })
    }

    // 硅基流动API配置 (动态生成10个)
    const apiKeys = [];
    for (let i = 1; i <= 10; i++) {
      const apiKey = process.env[`SILICONFLOW_API_KEY_${i}`];
      if (apiKey) {
        apiKeys.push(apiKey);
      }
    }

    if (apiKeys.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'API密钥未配置'
      }, { status: 500 })
    }

    // 随机选择一个API密钥
    const apiKey = apiKeys[Math.floor(Math.random() * apiKeys.length)]

    // 构建提示词
    const prompt = `基于关键词"${keyword}"生成${count}个SEO友好的.com域名。

要求：
1. 包含关键词或其变形
2. 8-20字符，无连字符
3. 适合Google排名
4. 说明必须用中文

返回JSON格式：
{
  "domains": [
    {
      "domain": "example.com",
      "reason": "中文说明域名含义"
    }
  ]
}`

    const response = await fetch(process.env.SILICONFLOW_API_URL || 'https://api.siliconflow.cn/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model: process.env.SILICONFLOW_DOMAIN_MODEL || 'Qwen/Qwen2.5-7B-Instruct',
        messages: [
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.7,
        max_tokens: 1000,
        stream: false, // 明确关闭流式输出，提高响应速度
        enable_thinking: false  // 禁用思考模式，确保快速响应
      })
    })

    if (!response.ok) {
      return NextResponse.json({
        success: false,
        error: `API请求失败: ${response.status}`
      }, { status: response.status })
    }

    const data = await response.json()

    if (!data.choices || !data.choices[0] || !data.choices[0].message) {
      return NextResponse.json({
        success: false,
        error: 'API响应格式错误'
      }, { status: 500 })
    }

    const content = data.choices[0].message.content.trim()

    // 解析JSON响应
    let domains: Array<{domain: string, reason: string}> = []
    try {
      const jsonMatch = content.match(/\{[\s\S]*\}/)
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0])
        domains = parsed.domains || []
      } else {
        throw new Error('未找到有效的JSON格式')
      }
    } catch (parseError) {
      // 如果JSON解析失败，尝试从文本中提取.com域名
      const domainRegex = /([a-zA-Z0-9-]+\.com)/g
      const matches = content.match(domainRegex) || []
      domains = matches.slice(0, count).map((domain: string) => ({
        domain: domain,
        reason: '基于关键词的创意推荐'
      }))
    }

    // 为每个域名添加查询链接
    const domainsWithLinks = domains.map((item: {domain: string, reason: string}) => ({
      ...item,
      check_url: `https://wanwang.aliyun.com/domain/searchresult/?keyword=${encodeURIComponent(item.domain.split('.')[0])}`
    }))

    return NextResponse.json({
      success: true,
      data: {
        keyword,
        count: domainsWithLinks.length,
        domains: domainsWithLinks
      }
    })

  } catch (error) {
    return NextResponse.json({
      success: false,
      error: '服务器内部错误'
    }, { status: 500 })
  }
}
