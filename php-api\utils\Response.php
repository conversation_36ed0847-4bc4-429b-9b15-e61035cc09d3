<?php
/**
 * API响应工具类
 * 统一处理API响应格式
 */

class Response {
    
    /**
     * 发送成功响应
     */
    public static function success($data = null, $message = '操作成功', $code = 200) {
        http_response_code($code);
        header('Content-Type: application/json; charset=utf-8');
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
        header('Access-Control-Allow-Headers: Content-Type, Authorization');
        
        $response = [
            'success' => true,
            'code' => $code,
            'message' => $message,
            'data' => $data,
            'timestamp' => date('Y-m-d H:i:s')
        ];
        
        echo json_encode($response, JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    /**
     * 发送错误响应
     */
    public static function error($message = '操作失败', $code = 400, $data = null) {
        http_response_code($code);
        header('Content-Type: application/json; charset=utf-8');
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
        header('Access-Control-Allow-Headers: Content-Type, Authorization');
        
        $response = [
            'success' => false,
            'code' => $code,
            'message' => $message,
            'data' => $data,
            'timestamp' => date('Y-m-d H:i:s')
        ];
        
        echo json_encode($response, JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    /**
     * 发送验证错误响应
     */
    public static function validationError($errors, $message = '数据验证失败') {
        self::error($message, 422, ['validation_errors' => $errors]);
    }
    
    /**
     * 发送未找到响应
     */
    public static function notFound($message = '资源未找到') {
        self::error($message, 404);
    }
    
    /**
     * 发送服务器错误响应
     */
    public static function serverError($message = '服务器内部错误') {
        self::error($message, 500);
    }
    
    /**
     * 发送未授权响应
     */
    public static function unauthorized($message = '未授权访问') {
        self::error($message, 401);
    }
    
    /**
     * 处理OPTIONS请求 (CORS预检)
     */
    public static function handleOptions() {
        if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
            header('Access-Control-Allow-Origin: *');
            header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
            header('Access-Control-Allow-Headers: Content-Type, Authorization');
            header('Access-Control-Max-Age: 86400');
            http_response_code(200);
            exit;
        }
    }
    
    /**
     * 获取请求体数据
     */
    public static function getRequestData() {
        $input = file_get_contents('php://input');
        $data = json_decode($input, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            self::error('无效的JSON数据', 400);
        }
        
        return $data ?: [];
    }
    
    /**
     * 验证请求方法
     */
    public static function validateMethod($allowedMethods) {
        $method = $_SERVER['REQUEST_METHOD'];
        
        if (!in_array($method, $allowedMethods)) {
            self::error('不支持的请求方法', 405);
        }
        
        return $method;
    }
    
    /**
     * 获取分页参数
     */
    public static function getPaginationParams() {
        $page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
        $limit = isset($_GET['limit']) ? max(1, min(100, intval($_GET['limit']))) : 20;
        $offset = ($page - 1) * $limit;
        
        return [
            'page' => $page,
            'limit' => $limit,
            'offset' => $offset
        ];
    }
    
    /**
     * 格式化分页响应
     */
    public static function paginated($data, $total, $page, $limit) {
        $totalPages = ceil($total / $limit);
        
        return [
            'items' => $data,
            'pagination' => [
                'current_page' => $page,
                'per_page' => $limit,
                'total' => $total,
                'total_pages' => $totalPages,
                'has_next' => $page < $totalPages,
                'has_prev' => $page > 1
            ]
        ];
    }
}
?>
