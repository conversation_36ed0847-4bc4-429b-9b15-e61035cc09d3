export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({
      success: false,
      message: 'Method not allowed'
    })
  }

  try {
    const { keyword_id } = req.query

    if (!keyword_id) {
      return res.status(400).json({
        success: false,
        message: '关键词ID不能为空'
      })
    }

    const apiBaseUrl = process.env.NEXT_PUBLIC_PHP_API_URL || 'https://api.xstty.com/api'

    // 调用PHP API获取报告
    const response = await fetch(`${apiBaseUrl}/keyword_reports.php?keyword_id=${encodeURIComponent(keyword_id)}`)
    const data = await response.json()

    if (data.success) {
      res.status(200).json({
        success: true,
        data: data.data
      })
    } else {
      // 没有报告数据时返回200状态码，但标明没有数据
      res.status(200).json({
        success: false,
        message: '报告不存在',
        data: null
      })
    }

  } catch (error) {
    console.error('获取报告失败:', error)
    res.status(500).json({
      success: false,
      message: '服务器错误，请稍后重试'
    })
  }
}
