'use client'

import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { AlertCircle, RotateCcw, Mail, Sparkles } from 'lucide-react'

export default function GlobalError({
  error: _error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  return (
    <html lang="zh-CN">
      <head>
        <title>系统错误 - CatchIdeas</title>
        <meta name="viewport" content="width=device-width, initial-scale=1" />
      </head>
      <body className="antialiased">
        <div className="min-h-screen bg-background">
          {/* 简化的顶部导航 */}
          <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur">
            <div className="container mx-auto px-4 py-4">
              <div className="flex items-center justify-center">
                <div className="flex items-center space-x-2">
                  <Sparkles className="h-8 w-8 text-primary sparkles-smooth sparkles-delay-1" />
                  <h1 className="text-2xl font-bold text-foreground">CatchIdeas</h1>
                </div>
              </div>
            </div>
          </header>

          {/* 全局错误区域 - 紫粉渐变主题 */}
          <section className="relative py-16 px-4 bg-gradient-to-br from-purple-50 via-pink-50 to-rose-50 overflow-hidden">
            {/* 背景装饰 - 全局错误主题色 */}
            <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
            <div className="absolute top-10 right-10 w-72 h-72 bg-purple-200 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob"></div>
            <div className="absolute top-20 left-10 w-72 h-72 bg-pink-200 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob animation-delay-2000"></div>
            <div className="absolute bottom-10 left-20 w-72 h-72 bg-rose-200 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob animation-delay-4000"></div>

            <div className="container mx-auto relative z-10">
              <div className="text-center mb-12">
                {/* 全局错误标题 */}
                <div className="mb-8">
                  <div className="text-8xl mb-6">🚨</div>
                  <h1 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-purple-600 via-pink-600 to-rose-600 bg-clip-text text-transparent mb-6">
                    网站大崩盘！
                  </h1>
                  <p className="text-xl text-muted-foreground mb-4 max-w-2xl mx-auto leading-relaxed">
                    糟糕！整个系统都<span className="font-semibold text-purple-600">宕机</span>了
                  </p>
                  <p className="text-lg text-muted-foreground mb-8 max-w-2xl mx-auto">
                    就像网站被搜索引擎完全除名一样严重... 但别担心！
                  </p>
                </div>

                {/* 专业提醒卡片 */}
                <div className="max-w-2xl mx-auto mb-8">
                  <Card className="idea-card-enhanced border-purple-200">
                    <CardContent className="p-8">
                      <div className="flex items-center justify-center space-x-3 mb-6">
                        <AlertCircle className="h-6 w-6 text-purple-600" />
                        <h3 className="text-xl font-bold text-foreground">专业提醒</h3>
                      </div>
                      <div className="text-left space-y-3 text-muted-foreground">
                        <div className="flex items-start space-x-2">
                          <span className="text-purple-600">•</span>
                          <span>这种全局错误比关键词竞争度突然飙升还罕见</span>
                        </div>
                        <div className="flex items-start space-x-2">
                          <span className="text-purple-600">•</span>
                          <span>我们的服务器可能正在经历"算法惩罚"</span>
                        </div>
                        <div className="flex items-start space-x-2">
                          <span className="text-purple-600">•</span>
                          <span>开发团队已经启动紧急救援模式</span>
                        </div>
                        <div className="flex items-start space-x-2">
                          <span className="text-purple-600">•</span>
                          <span>恢复速度目标：比网站重新被索引还要快</span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {/* 操作按钮 */}
                <div className="space-y-6">
                  <Button 
                    onClick={() => reset()}
                    size="lg" 
                    className="bg-purple-600 hover:bg-purple-700 text-white px-8 py-3 shadow-lg hover:shadow-xl transition-all duration-200 text-lg"
                  >
                    <RotateCcw className="h-5 w-5 mr-2" />
                    紧急重启
                  </Button>
                  
                  {/* 临时建议提示 */}
                  <div className="max-w-md mx-auto">
                    <Alert className="border-yellow-200 bg-yellow-50">
                      <AlertCircle className="h-4 w-4 text-yellow-600" />
                      <AlertDescription className="text-yellow-800">
                        <strong>临时建议：</strong> 可以先去喝杯咖啡，
                        等我们修复这个比负面SEO还要棘手的问题
                      </AlertDescription>
                    </Alert>
                  </div>
                  
                  <div className="text-sm text-muted-foreground">
                    紧急联系：
                    <a 
                      href="mailto:<EMAIL>" 
                      className="text-purple-600 hover:underline font-medium mx-1 inline-flex items-center"
                    >
                      <Mail className="h-4 w-4 mr-1" />
                      <EMAIL>
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* 简化的底部 */}
          <footer className="border-t bg-muted/30 py-8">
            <div className="container mx-auto px-4 text-center">
              <div className="flex items-center justify-center space-x-2 mb-4">
                <Sparkles className="h-6 w-6 text-primary sparkles-smooth sparkles-delay-3" />
                <span className="font-bold text-lg">CatchIdeas</span>
              </div>
              <p className="text-sm text-muted-foreground">
                系统正在紧急修复中，感谢您的耐心等待
              </p>
            </div>
          </footer>
        </div>
      </body>
    </html>
  )
}
