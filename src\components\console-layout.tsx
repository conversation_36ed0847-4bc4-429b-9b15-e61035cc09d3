'use client'

import { useState } from 'react'
import { useSession } from 'next-auth/react'
import { Button } from './ui/button'
import {
  Settings,
  Upload,
  FileText,
  Home,
  Menu,
  X,
  BarChart3,
  Edit3
} from 'lucide-react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import ConsoleHeader from './console-header'

interface ConsoleLayoutProps {
  children: React.ReactNode
  title?: string
  description?: string
  actions?: React.ReactNode
}

export default function ConsoleLayout({ 
  children, 
  title = "后台管理", 
  description,
  actions 
}: ConsoleLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const pathname = usePathname()
  const { data: session } = useSession()

  // 根据用户角色过滤导航菜单
  const allNavigation = [
    {
      name: '仪表板',
      href: '/console',
      icon: Home,
      current: pathname === '/console',
      description: '数据概览统计',
      adminOnly: true // 仅管理员可见
    },
    {
      name: '关键词管理',
      href: '/console/keywords',
      icon: FileText,
      current: pathname === '/console/keywords',
      description: '关键词管理',
      adminOnly: true // 仅管理员可见
    },
    {
      name: 'CSV导入',
      href: '/console/upload',
      icon: Upload,
      current: pathname === '/console/upload',
      description: '批量数据导入',
      adminOnly: false // 所有已登录用户可见
    },
    {
      name: '手动导入',
      href: '/console/manual-import',
      icon: Edit3,
      current: pathname === '/console/manual-import',
      description: '文本数据导入',
      adminOnly: true // 仅管理员可见
    },
    {
      name: 'AI分析',
      href: '/console/analyze',
      icon: BarChart3,
      current: pathname === '/console/analyze',
      description: '智能分析处理',
      adminOnly: false // 所有用户可见
    },
    {
      name: 'API测试',
      href: '/console/api-test',
      icon: Settings,
      current: pathname === '/console/api-test',
      description: 'API密钥测试',
      adminOnly: true // 仅管理员可见
    }
  ]

  // 根据用户角色过滤导航
  const navigation = allNavigation.filter(item => {
    if (!item.adminOnly) return true // 所有用户都可以看到的菜单
    return (session?.user as any)?.role === 'admin' // 仅管理员可见的菜单
  })

  return (
    <div className="min-h-screen bg-background">
      {/* 使用新的统一顶部组件 */}
      <ConsoleHeader
        title={title}
        description={description}
        actions={actions}
      />

      {/* 移动端菜单按钮 */}
      <Button
        variant="ghost"
        size="sm"
        className="fixed top-4 left-4 z-50 md:hidden"
        onClick={() => setSidebarOpen(!sidebarOpen)}
      >
        {sidebarOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
      </Button>

      <div className="flex">
        {/* 现代化侧边栏 */}
        <aside className={`
          fixed inset-y-0 left-0 z-40 w-72 bg-gradient-to-b from-background to-muted/30 border-r border-border
          transform transition-all duration-300 ease-in-out shadow-lg
          md:relative md:translate-x-0 md:inset-0 md:shadow-none
          ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}
        `}>
          <div className="flex flex-col h-full">
            {/* 侧边栏头部 - 卡片风格 */}
            <div className="p-4 m-4 mb-2 rounded-xl bg-card border border-border/50 shadow-sm data-card-enhanced">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-primary rounded-xl flex items-center justify-center shadow-lg">
                  <Settings className="h-6 w-6 text-primary-foreground sparkles-smooth sparkles-delay-1" />
                </div>
                <div>
                  <h2 className="font-bold text-foreground text-lg">控制台</h2>
                  <p className="text-sm text-primary/80 font-medium">数据管理中心</p>
                </div>
              </div>
            </div>

            {/* 导航菜单 - 卡片风格 */}
            <nav className="flex-1 px-4 py-6 space-y-3">
              {navigation.map((item) => {
                const Icon = item.icon
                return (
                  <Link
                    key={item.name}
                    href={item.href}
                    className={`
                      group block p-4 rounded-xl border transition-all duration-300 ease-out data-card-enhanced
                      ${item.current
                        ? 'bg-card border-primary/30 shadow-md shadow-primary/10 transform scale-[1.02]'
                        : 'bg-card border-border/50 hover:border-primary/20 hover:shadow-lg hover:shadow-primary/5 hover:transform hover:scale-[1.02] active:scale-[0.98]'
                      }
                    `}
                    onClick={() => setSidebarOpen(false)}
                  >
                    <div className="flex items-center space-x-3">
                      <div className={`
                        p-2 rounded-lg transition-all duration-300
                        ${item.current
                          ? 'bg-primary/20 text-primary'
                          : 'bg-muted text-muted-foreground group-hover:bg-primary/15 group-hover:text-primary'
                        }
                      `}>
                        <Icon className="h-5 w-5" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className={`font-semibold text-sm transition-colors duration-300 ${
                          item.current ? 'text-primary' : 'text-foreground group-hover:text-primary'
                        }`}>
                          {item.name}
                        </div>
                        <div className={`text-xs mt-0.5 transition-colors duration-300 ${
                          item.current ? 'text-primary/70' : 'text-muted-foreground group-hover:text-primary/60'
                        }`}>
                          {item.description}
                        </div>
                      </div>
                      {item.current && (
                        <div className="w-2 h-2 bg-primary rounded-full animate-pulse"></div>
                      )}
                    </div>
                  </Link>
                )
              })}
            </nav>

            {/* 底部信息 - 卡片风格 */}
            <div className="p-4 m-4 mt-2 rounded-xl bg-card border border-border/50 shadow-sm data-card-enhanced">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-secondary rounded-lg flex items-center justify-center shadow-md">
                  <BarChart3 className="h-5 w-5 text-secondary-foreground" />
                </div>
                <div className="text-xs">
                  <div className="font-bold text-foreground">CatchIdeas v3.57</div>
                  <div className="text-secondary/80 font-medium"><EMAIL></div>
                </div>
              </div>
            </div>
          </div>
        </aside>

        {/* 主内容区域 */}
        <main className="flex-1 md:ml-0 console-background">
          {/* 页面内容 */}
          <div className="p-6">
            {children}
          </div>
        </main>
      </div>

      {/* 移动端遮罩 */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 z-30 bg-black/50 backdrop-blur-sm md:hidden transition-opacity duration-300"
          onClick={() => setSidebarOpen(false)}
        />
      )}
    </div>
  )
}
