import { NextRequest, NextResponse } from 'next/server'

/**
 * 批量删除关键词API
 * 调用PHP后端API进行批量删除操作
 */
export async function POST(request: NextRequest) {
  try {
    // 解析请求数据
    const { ids } = await request.json()
    
    // 验证参数
    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return NextResponse.json(
        { success: false, message: '请选择要删除的关键词' },
        { status: 400 }
      )
    }

    // 验证ID格式（只支持slug格式）
    const slugRegex = /^[a-z0-9]+(?:-[a-z0-9]+)*$/
    const invalidIds = ids.filter(id => !slugRegex.test(id))
    if (invalidIds.length > 0) {
      return NextResponse.json(
        { success: false, message: '包含无效的关键词ID格式' },
        { status: 400 }
      )
    }

    // 调用PHP批量删除API
    const response = await fetch('https://api.xstty.com/api/batch-delete.php', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ ids })
    })

    if (!response.ok) {
      const errorText = await response.text()
      return NextResponse.json(
        {
          success: false,
          message: `PHP API调用失败: ${response.status}`,
          error: errorText
        },
        { status: 500 }
      )
    }

    const result = await response.json()

    // 直接返回PHP API的结果
    if (result.success) {
      return NextResponse.json(result)
    } else {
      return NextResponse.json(result, { status: response.status || 500 })
    }

  } catch (error) {
    return NextResponse.json(
      { 
        success: false, 
        message: '服务器内部错误',
        error: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    )
  }
}

// 不支持其他HTTP方法
export async function GET() {
  return NextResponse.json(
    { success: false, message: '不支持的请求方法' },
    { status: 405 }
  )
}

export async function PUT() {
  return NextResponse.json(
    { success: false, message: '不支持的请求方法' },
    { status: 405 }
  )
}

export async function DELETE() {
  return NextResponse.json(
    { success: false, message: '不支持的请求方法' },
    { status: 405 }
  )
}
