'use client'

import { ChevronRight, Home } from 'lucide-react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'

interface BreadcrumbItem {
  name: string
  href: string
  current?: boolean
}

export default function ConsoleBreadcrumb() {
  const pathname = usePathname()

  const getBreadcrumbs = (): BreadcrumbItem[] => {
    const breadcrumbs: BreadcrumbItem[] = [
      { name: 'CatchIdeas', href: '/' },
      { name: '后台管理', href: '/console' }
    ]

    // 根据当前路径添加面包屑
    if (pathname === '/console/keywords') {
      breadcrumbs.push({ name: '关键词管理', href: '/console/keywords', current: true })
    } else if (pathname === '/console/upload') {
      breadcrumbs.push({ name: 'CSV导入', href: '/console/upload', current: true })
    } else if (pathname === '/console/analyze') {
      breadcrumbs.push({ name: 'AI分析', href: '/console/analyze', current: true })
    } else if (pathname === '/console') {
      breadcrumbs[breadcrumbs.length - 1].current = true
    }

    return breadcrumbs
  }

  const breadcrumbs = getBreadcrumbs()

  return (
    <nav className="flex items-center space-x-2 text-sm">
      {breadcrumbs.map((crumb, index) => (
        <div key={crumb.href} className="flex items-center">
          {index > 0 && <ChevronRight className="h-4 w-4 text-muted-foreground mx-2" />}
          {index === 0 && <Home className="h-4 w-4 mr-2 text-muted-foreground" />}
          <Link
            href={crumb.href}
            className={`
              ${crumb.current 
                ? 'text-foreground font-medium' 
                : 'text-muted-foreground hover:text-foreground'
              }
            `}
          >
            {crumb.name}
          </Link>
        </div>
      ))}
    </nav>
  )
}
