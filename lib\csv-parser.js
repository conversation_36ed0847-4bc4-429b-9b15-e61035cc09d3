/**
 * CSV解析器 - 专门处理Google Trends CSV文件
 * 读取第一列数据，从第3行开始（跳过标题行）
 */

import { keywordAPI } from './api-client.js'

/**
 * 生成SEO友好的slug ID
 * @param {string} keyword - 关键词
 * @returns {string} - slug格式的ID
 */
function generateSlugId(keyword) {
  // 基础slug生成
  let slug = keyword
    .toLowerCase()
    .trim()
    // 移除特殊字符，保留字母、数字、空格、连字符
    .replace(/[^\w\s-]/g, '')
    // 将多个空格替换为单个连字符
    .replace(/\s+/g, '-')
    // 移除多个连续的连字符
    .replace(/-+/g, '-')
    // 移除开头和结尾的连字符
    .replace(/^-+|-+$/g, '');

  // 如果slug为空或太短，使用fallback
  if (!slug || slug.length < 2) {
    slug = `keyword-${Date.now()}`;
  }

  // 限制长度，避免过长的URL
  if (slug.length > 50) {
    slug = slug.substring(0, 50).replace(/-+$/, '');
  }

  return slug;
}

/**
 * 生成简单的UUID（备用方案）
 */
function generateSimpleUUID() {
  return 'xxxx-xxxx-4xxx-yxxx-xxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0
    const v = c === 'x' ? r : (r & 0x3 | 0x8)
    return v.toString(16)
  })
}

/**
 * 解析CSV文件内容
 * @param {string} csvContent - CSV文件内容
 * @param {string} filename - 文件名
 * @returns {Object} 解析结果
 */
export function parseCSV(csvContent, filename = 'unknown.csv') {
  try {
    const lines = csvContent.split('\n').map(line => line.trim()).filter(line => line)
    
    if (lines.length < 3) {
      throw new Error('CSV文件格式不正确，至少需要3行数据')
    }

    // 跳过前两行，从第3行开始读取关键词
    const keywordLines = lines.slice(2)
    const rawKeywords = []
    
    keywordLines.forEach((line, index) => {
      // 解析CSV行，处理可能的引号和逗号
      const columns = parseCSVLine(line)
      if (columns.length > 0 && columns[0]) {
        const keyword = columns[0].trim()
        if (keyword) {
          rawKeywords.push({
            id: generateSlugId(keyword),
            keyword: keyword,
            originalLine: index + 3, // 原始行号（从1开始计数）
            source: 'google_trends',
            importDate: new Date().toISOString().split('T')[0],
            importedAt: new Date().toISOString(),
            filename: filename
          })
        }
      }
    })

    return {
      success: true,
      totalLines: lines.length,
      headerLines: 2,
      keywordLines: keywordLines.length,
      rawKeywords: rawKeywords,
      validKeywords: rawKeywords.length,
      filename: filename,
      importedAt: new Date().toISOString()
    }

  } catch (error) {
    return {
      success: false,
      error: error.message,
      filename: filename,
      importedAt: new Date().toISOString()
    }
  }
}

/**
 * 解析单行CSV数据，处理引号和逗号
 * @param {string} line - CSV行数据
 * @returns {Array} 列数据数组
 */
function parseCSVLine(line) {
  const result = []
  let current = ''
  let inQuotes = false
  
  for (let i = 0; i < line.length; i++) {
    const char = line[i]
    
    if (char === '"') {
      if (inQuotes && line[i + 1] === '"') {
        // 双引号转义
        current += '"'
        i++ // 跳过下一个引号
      } else {
        // 切换引号状态
        inQuotes = !inQuotes
      }
    } else if (char === ',' && !inQuotes) {
      // 列分隔符
      result.push(current)
      current = ''
    } else {
      current += char
    }
  }
  
  // 添加最后一列
  result.push(current)
  
  return result
}

// 注意：deepFilterKeywords 函数已被移除，现在全程使用AI过滤

/**
 * AI智能过滤关键词
 * @param {Array} keywords - 关键词数组
 * @param {Function} onProgress - 进度回调函数
 * @returns {Promise<Object>} 过滤结果
 */
export async function aiFilterKeywords(keywords, onProgress = null) {
  try {
    // 获取可用的API密钥 (动态生成10个)
    const availableKeys = [];
    for (let i = 1; i <= 10; i++) {
      const apiKey = process.env[`SILICONFLOW_API_KEY_${i}`];
      if (apiKey && apiKey.trim() !== '') {
        availableKeys.push(apiKey);
      }
    }

    if (availableKeys.length === 0) {
      return {
        success: false,
        error: 'AI服务暂时不可用，请稍后重试',
        accepted: [],
        rejected: keywords.map(keyword => ({
          ...keyword,
          ai_filter_reason: 'AI服务配置错误',
          rejectionReason: 'service_unavailable'
        })),
        stats: {
          total: keywords.length,
          accepted: 0,
          rejected: keywords.length
        }
      }
    }

    // 验证API URL
    const apiUrl = process.env.SILICONFLOW_API_URL
    if (!apiUrl) {
      return {
        success: false,
        error: 'AI服务配置错误',
        accepted: [],
        rejected: keywords.map(keyword => ({
          ...keyword,
          ai_filter_reason: 'AI服务配置错误',
          rejectionReason: 'service_unavailable'
        })),
        stats: {
          total: keywords.length,
          accepted: 0,
          rejected: keywords.length
        }
      }
    }

    // 使用最高质量的模型进行精准过滤
    const modelName = process.env.SILICONFLOW_COMPETITION_MODEL || 'moonshotai/Kimi-K2-Instruct'

    const batchSize = 50 // 每批处理50个关键词
    const batches = []

    // 分批处理
    for (let i = 0; i < keywords.length; i += batchSize) {
      batches.push(keywords.slice(i, i + batchSize))
    }

    const results = {
      accepted: [],
      rejected: [],
      total: keywords.length,
      processed: 0
    }

    // 处理每一批
    for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
      const batch = batches[batchIndex]
      const progress = Math.round(((batchIndex + 1) / batches.length) * 100)

      if (onProgress) {
        onProgress({
          step: 'ai_filtering',
          message: `智能质量检测中... (${batchIndex + 1}/${batches.length})`,
          progress: progress,
          processed: results.processed,
          total: results.total
        })
      }

      // 预过滤：移除单个单词和明显不合格的关键词
      const preFilteredKeywords = []
      const preRejectedKeywords = []

      batch.forEach(keyword => {
        const keywordText = keyword.keyword.trim()
        const wordCount = keywordText.split(/\s+/).filter(word => word.length > 0).length

        // 拒绝单个单词
        if (wordCount < 2) {
          preRejectedKeywords.push({
            ...keyword,
            ai_filter_reason: '单个单词，无SEO价值',
            rejectionReason: 'single_word'
          })
          return
        }

        // 拒绝过短或过长的关键词
        if (keywordText.length < 3 || keywordText.length > 100) {
          preRejectedKeywords.push({
            ...keyword,
            ai_filter_reason: '关键词长度不合适',
            rejectionReason: 'invalid_length'
          })
          return
        }

        // 拒绝纯数字或主要由数字组成的关键词
        if (/^\d+$/.test(keywordText) || /^\d+[\s\d]*\d+$/.test(keywordText)) {
          preRejectedKeywords.push({
            ...keyword,
            ai_filter_reason: '纯数字关键词',
            rejectionReason: 'numeric_only'
          })
          return
        }

        // 通过预过滤的关键词
        preFilteredKeywords.push(keyword)
      })

      // 将预过滤拒绝的关键词添加到结果中
      preRejectedKeywords.forEach(rejected => {
        results.rejected.push(rejected)
        results.processed++
      })

      const keywordsToProcess = preFilteredKeywords

      // 如果批次为空，跳过AI处理
      if (keywordsToProcess.length === 0) {
        continue
      }

      // 获取当前时间信息
      const currentDate = new Date()
      const currentYear = currentDate.getFullYear()
      const currentDateStr = `${currentYear}年${currentDate.getMonth() + 1}月${currentDate.getDate()}日`

      const prompt = `当前时间：${currentDateStr}（${currentYear}年）

你是世界顶级的SEO关键词筛选专家。请对以下关键词进行严格筛选，确保只保留真正有SEO价值且个人网站可以竞争的关键词。

关键词列表：
${keywordsToProcess.map(k => k.keyword).join('\n')}

## 筛选标准

### 直接拒绝的关键词类型：

#### 1. 知名品牌/公司名 (brands)
- 科技巨头：google, apple, microsoft, amazon, meta, facebook, tesla, netflix, spotify, adobe, oracle, salesforce, nvidia, intel, amd, ibm, cisco, dell, hp, lenovo, samsung, lg, sony, panasonic, toshiba, canon, nikon
- 社交平台：youtube, tiktok, instagram, whatsapp, twitter, linkedin, snapchat, pinterest, reddit, discord, telegram, wechat, line, viber, skype, zoom, teams
- 电商平台：amazon, ebay, walmart, alibaba, shopify, etsy, wish, mercadolibre, taobao, tmall, jd, rakuten
- 金融服务：paypal, visa, mastercard, stripe, square, robinhood, coinbase, binance, chase, wells fargo, bank of america, citibank, hsbc, jpmorgan
- 物流快递：fedex, ups, dhl, usps, tnt, dpd
- 传统品牌：nike, adidas, coca cola, pepsi, mcdonalds, kfc, starbucks, target, costco, walmart, home depot, lowes, best buy
- 汽车品牌：toyota, honda, ford, bmw, mercedes, audi, volkswagen, hyundai, kia, nissan, mazda, subaru, volvo, jaguar, porsche, ferrari, lamborghini, bentley, rolls royce
- 奢侈品牌：louis vuitton, gucci, chanel, hermes, prada, rolex, cartier, tiffany, bulgari, versace, armani, dior, burberry
- 服装品牌：zara, h&m, uniqlo, gap, levi's, calvin klein, tommy hilfiger, polo, lacoste, under armour, puma, reebok, converse, vans
- 航空公司：american airlines, delta, united, southwest, lufthansa, emirates, british airways, air france, klm, singapore airlines, cathay pacific, qantas
- 酒店连锁：marriott, hilton, hyatt, intercontinental, accor, sheraton, westin, ritz carlton, four seasons, mandarin oriental
- 媒体平台：cnn, bbc, fox news, nytimes, wsj, reuters, bloomberg, espn, discovery, national geographic, time, forbes, wired
- 游戏公司：nintendo, sony, microsoft, valve, epic games, activision, blizzard, ea, ubisoft, rockstar, bethesda, square enix, capcom, konami
- 流媒体：netflix, disney, hulu, hbo, paramount, peacock, prime video, youtube tv, twitch

#### 2. 通用词汇 (generic_words)
- 颜色词汇：black, white, red, blue, green, yellow, pink, purple, orange, brown, gray, grey
- 趋势词汇：rising, trending, popular, hot, viral, news, latest, current, today, new, best, top
- 地理词汇：usa, america, china, europe, asia, california, florida, texas, new york, london, paris
- 通用名词：house, car, food, music, movie, book, game, app, website, business, camera, phone, computer, laptop, software, hardware, service, product, company, store, shop, market
- 时间词汇：2024, 2023, 2022, yesterday, today, tomorrow, weekend, holiday, monday, tuesday, wednesday, thursday, friday, saturday, sunday, morning, afternoon, evening, night
- 抽象概念：love, life, happiness, success, money, time, health, beauty, fashion, style, design, art, culture, education, learning, work, job, career, business, finance
- 单个字母/数字：a, b, c, d, e, f, g, h, i, j, k, l, m, n, o, p, q, r, s, t, u, v, w, x, y, z, 1, 2, 3, 4, 5, 6, 7, 8, 9, 0
- 过于简单的词：go, do, be, is, are, was, were, have, has, had, get, got, make, take, come, see, know, think, say, tell, use, find, give, work, call, try, ask, need, feel, become, leave, put, mean, keep, let, begin, seem, help, talk, turn, start, show, hear, play, run, move, live, believe, hold, bring, happen, write, provide, sit, stand, lose, pay, meet, include, continue, set, learn, change, lead, understand, watch, follow, stop, create, speak, read, allow, add, spend, grow, open, walk, win, offer, remember, consider, appear, buy, wait, serve, die, send, expect, build, stay, fall, cut, reach, kill, remain

#### 3. 知名人物姓名 (person_names)
- 企业家：bezos, jeff bezos, elon musk, bill gates, mark zuckerberg, steve jobs, warren buffett, larry page, sergey brin, tim cook, satya nadella, sundar pichai, jack ma, ma yun
- 政治人物：biden, trump, obama, putin, xi jinping, modi, macron, merkel, trudeau, johnson, kim jong un, erdogan, bolsonaro
- 明星艺人：taylor swift, beyonce, justin bieber, ariana grande, drake, kanye west, rihanna, lady gaga, ed sheeran, adele, bruno mars, eminem, jay z, britney spears, madonna, michael jackson, elvis presley
- 演员：leonardo dicaprio, brad pitt, angelina jolie, tom cruise, will smith, jennifer lawrence, scarlett johansson, robert downey jr, chris evans, ryan reynolds, dwayne johnson, the rock
- 体育明星：lebron james, michael jordan, kobe bryant, cristiano ronaldo, lionel messi, neymar, serena williams, roger federer, novak djokovic, rafael nadal, tiger woods, usain bolt
- 历史人物：einstein, newton, shakespeare, lincoln, washington, churchill, gandhi, napoleon, caesar, cleopatra

#### 4. 非英文内容 (non_english)
- 包含中文、日文、韩文、阿拉伯文、俄文等非英文字符的关键词
- 混合语言的关键词

#### 5. 无意义内容 (meaningless)
- 明显的乱码或随机字符组合
- 无法理解含义的字符串
- 纯数字或纯符号

#### 6. 竞争过激烈 (too_competitive)
- 金融指数：dow jones, nasdaq, s&p 500, bitcoin, ethereum, dogecoin, crypto, cryptocurrency, forex, trading, investment, stocks, bonds
- 超高竞争行业通用词：insurance, loan, mortgage, credit, lawyer, attorney, casino, gambling, poker, slots, betting, weight loss, diet, fitness, health, medical, doctor, hospital, pharmacy, medicine, drug, treatment, therapy, rehab, addiction, recovery
- 高价值商业词：money, cash, income, salary, profit, revenue, business, marketing, advertising, seo, sem, ppc, affiliate, mlm, make money, earn money, get rich, millionaire, billionaire
- 法律相关：lawsuit, legal, court, judge, jury, trial, settlement, compensation, injury, accident, malpractice, divorce, custody, immigration, visa, green card

#### 7. 无SEO价值 (no_seo_value)
- 过于宽泛无具体搜索意图的词汇
- 无法创建有价值内容的关键词

### 应该保留的关键词类型：
1. **多词组合短语**：2个以上单词组成的具体短语（优先保留）
2. **教程指南类**：how to, best, guide, tips等有明确搜索意图的词组
3. **产品评测类**：review, comparison, vs等比较类关键词
4. **本地服务类**：near me, local等本地化关键词
5. **问题解决型**：具体的问题或需求相关关键词
6. **长尾关键词**：具有明确搜索意图的3个以上单词组合
7. **行业术语组合**：专业术语与修饰词的组合
8. **具体事件/话题**：有明确指向性的事件或话题名称

### 严格执行的规则：
1. **绝对禁止单个单词**：任何单个单词的关键词必须拒绝，包括但不限于：juegos, lotería, solitaire, games, music, food, car, house, love, money, health, beauty, fashion, travel, sports, news, business, education, technology, science, art, culture, history, politics, religion, philosophy, psychology, medicine, law, finance, marketing, advertising, design, photography, cooking, fitness, yoga, meditation, dance, theater, cinema, literature, poetry, writing, reading, learning, teaching, working, shopping, entertainment, fun, hobby, leisure, vacation, holiday, weekend, family, friends, children, kids, baby, pet, dog, cat, bird, fish, plant, flower, tree, garden, nature, environment, weather, climate, season, spring, summer, autumn, winter, morning, afternoon, evening, night, today, tomorrow, yesterday, week, month, year, time, space, world, earth, universe, galaxy, star, planet, moon, sun, light, dark, color, sound, music, voice, language, word, sentence, story, book, magazine, newspaper, website, internet, computer, phone, tablet, camera, video, photo, image, picture, drawing, painting, sculpture, architecture, building, house, home, room, kitchen, bedroom, bathroom, living, dining, office, school, university, college, hospital, hotel, restaurant, cafe, bar, club, gym, park, beach, mountain, river, lake, ocean, sea, island, city, town, village, country, state, province, region, continent, america, europe, asia, africa, australia, antarctica, north, south, east, west, left, right, up, down, inside, outside, front, back, top, bottom, center, middle, beginning, end, start, finish, open, close, big, small, large, tiny, huge, giant, long, short, tall, wide, narrow, thick, thin, heavy, light, fast, slow, quick, easy, hard, difficult, simple, complex, good, bad, best, worst, better, worse, new, old, young, fresh, clean, dirty, hot, cold, warm, cool, dry, wet, soft, hard, smooth, rough, sharp, dull, bright, dark, loud, quiet, high, low, deep, shallow, full, empty, rich, poor, expensive, cheap, free, busy, lazy, active, passive, strong, weak, healthy, sick, happy, sad, angry, calm, excited, bored, tired, energetic, beautiful, ugly, pretty, handsome, smart, stupid, clever, wise, funny, serious, kind, mean, nice, rude, polite, honest, dishonest, true, false, real, fake, natural, artificial, organic, synthetic, local, global, national, international, public, private, personal, professional, formal, informal, casual, official, legal, illegal, safe, dangerous, secure, risky, certain, uncertain, possible, impossible, probable, unlikely, necessary, optional, important, unimportant, useful, useless, helpful, harmful, positive, negative, optimistic, pessimistic, confident, nervous, brave, scared, proud, ashamed, grateful, ungrateful, patient, impatient, generous, selfish, loyal, disloyal, faithful, unfaithful, reliable, unreliable, responsible, irresponsible, mature, immature, independent, dependent, creative, uncreative, original, copied, unique, common, rare, frequent, occasional, regular, irregular, normal, abnormal, typical, unusual, standard, special, ordinary, extraordinary, average, excellent, terrible, perfect, imperfect, complete, incomplete, finished, unfinished, ready, unready, available, unavailable, present, absent, here, there, everywhere, nowhere, somewhere, anywhere, everyone, nobody, somebody, anybody, everything, nothing, something, anything
2. **优先多词组合**：2-5个单词的组合优先保留
3. **明确搜索意图**：关键词必须有明确的搜索意图和内容创作空间
4. **避免过度宽泛**：过于宽泛无具体指向的词组要拒绝
5. **长度合理**：关键词总长度应在5-50个字符之间
6. **避免纯数字**：纯数字或主要由数字组成的关键词要拒绝
7. **英文为主**：关键词应主要由英文单词组成

## 输出格式要求

严格按照以下JSON格式输出，不要任何额外解释：

{
  "summary": {
    "total_processed": 0,
    "accepted": 0,
    "rejected": 0,
    "acceptance_rate": "0.00%"
  },
  "accepted_keywords": [
    "keyword1",
    "keyword2"
  ],
  "rejected_keywords": {
    "brands": [
      "brand_keyword1",
      "brand_keyword2"
    ],
    "generic_words": [
      "generic_keyword1",
      "generic_keyword2"
    ],
    "person_names": [
      "person_name1",
      "person_name2"
    ],
    "non_english": [
      "non_english_keyword1",
      "non_english_keyword2"
    ],
    "meaningless": [
      "meaningless_keyword1",
      "meaningless_keyword2"
    ],
    "too_competitive": [
      "competitive_keyword1",
      "competitive_keyword2"
    ],
    "no_seo_value": [
      "no_value_keyword1",
      "no_value_keyword2"
    ]
  }
}

注意：
1. 严格按照标准筛选，保留有价值的关键词
2. acceptance_rate 计算公式：(accepted / total_processed) * 100，保留2位小数
3. total_processed 必须等于 accepted + rejected 总数
4. 每个拒绝分类数组可以为空，但必须存在
5. 只输出JSON，不要任何其他文字或解释`

      // 尝试多个API密钥
      let batchResult = null
      let apiError = null
      
      for (const apiKey of availableKeys) {
        try {
          const requestBody = {
            model: modelName,
            messages: [
              {
                role: 'system',
                content: '你是世界顶级的SEO专家和品牌识别专家。你的任务是严格筛选关键词，确保只保留真正有SEO价值且个人网站可以竞争的关键词。'
              },
              {
                role: 'user',
                content: prompt
              }
            ],
            temperature: 0.6, // 使用Kimi推荐值
            max_tokens: 4000, // 提升token数量支持更详细分析
            top_p: 0.7, // 保持采样参数
            frequency_penalty: 0.0, // 添加Kimi支持的参数
            stream: false
          }

          const response = await fetch(apiUrl, {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${apiKey}`,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestBody)
          })

          if (!response.ok) {
            const errorText = await response.text()
            throw new Error(`API响应错误 (${response.status}): ${errorText}`)
          }

          // 处理标准JSON响应
          const responseData = await response.json()

          // 提取AI回复内容
          const aiContent = responseData.choices?.[0]?.message?.content || ''

          // 尝试解析JSON
          try {
            // 清理可能的markdown格式
            const cleanContent = aiContent.replace(/```json\n?/g, '').replace(/```\n?/g, '').trim()
            const parsedResult = JSON.parse(cleanContent)

            // 验证新的JSON格式
            if (parsedResult?.summary && Array.isArray(parsedResult?.accepted_keywords) && parsedResult?.rejected_keywords) {
              batchResult = parsedResult
              break // 成功处理，跳出API密钥循环
            } else {
              throw new Error('AI响应格式不正确，缺少必要字段')
            }
          } catch (parseError) {
            throw new Error(`AI响应解析失败: ${parseError.message}`)
          }
        } catch (error) {
          apiError = error
          continue // 尝试下一个API密钥
        }
      }

      // 如果所有API密钥都失败了
      if (!batchResult && apiError) {
        // 将这批关键词标记为处理失败，但不暴露具体错误
        batch.forEach(keyword => {
          results.rejected.push({
            ...keyword,
            ai_filter_reason: '智能检测暂时不可用，请稍后重试',
            rejectionReason: 'service_error'
          })
          results.processed++
        })
        continue
      }

      // 处理批次结果
      if (batchResult && batchResult.summary && Array.isArray(batchResult.accepted_keywords)) {
        const acceptedKeywords = new Set(batchResult.accepted_keywords)

        // 处理所有关键词（不再有预过滤）
        batch.forEach(originalKeyword => {
          if (acceptedKeywords.has(originalKeyword.keyword)) {
            results.accepted.push({
              ...originalKeyword,
              ai_filter_reason: '通过AI智能筛选'
            })
          } else {
            // 根据AI返回的分类确定拒绝原因
            let rejectionReason = 'ai_filtered'
            let detailedReason = '未通过AI智能筛选'

            const rejected = batchResult.rejected_keywords
            if (rejected.brands && rejected.brands.includes(originalKeyword.keyword)) {
              rejectionReason = 'brand'
              detailedReason = '知名品牌/公司名'
            } else if (rejected.generic_words && rejected.generic_words.includes(originalKeyword.keyword)) {
              rejectionReason = 'generic'
              detailedReason = '通用词汇'
            } else if (rejected.person_names && rejected.person_names.includes(originalKeyword.keyword)) {
              rejectionReason = 'person_name'
              detailedReason = '知名人物姓名'
            } else if (rejected.non_english && rejected.non_english.includes(originalKeyword.keyword)) {
              rejectionReason = 'non_english'
              detailedReason = '非英文内容'
            } else if (rejected.meaningless && rejected.meaningless.includes(originalKeyword.keyword)) {
              rejectionReason = 'meaningless'
              detailedReason = '无意义内容'
            } else if (rejected.too_competitive && rejected.too_competitive.includes(originalKeyword.keyword)) {
              rejectionReason = 'too_competitive'
              detailedReason = '竞争过于激烈'
            } else if (rejected.no_seo_value && rejected.no_seo_value.includes(originalKeyword.keyword)) {
              rejectionReason = 'no_seo_value'
              detailedReason = '无SEO价值'
            }

            results.rejected.push({
              ...originalKeyword,
              ai_filter_reason: detailedReason,
              rejectionReason: rejectionReason
            })
          }
          results.processed++
        })
      } else {
        // AI过滤失败，拒绝所有关键词
        batch.forEach(keyword => {
          results.rejected.push({
            ...keyword,
            ai_filter_reason: 'AI服务异常，无法处理',
            rejectionReason: 'ai_service_error'
          })
          results.processed++
        })
      }

      // 添加延迟避免API限制
      if (batchIndex < batches.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 1000))
      }
    }

    // 计算详细的拒绝统计
    const rejectionStats = {
      brand: 0,
      generic: 0,
      person_name: 0,
      non_english: 0,
      meaningless: 0,
      too_competitive: 0,
      no_seo_value: 0,
      ai_service_error: 0,
      single_word: 0,
      invalid_length: 0,
      numeric_only: 0,
      other: 0
    }

    results.rejected.forEach(item => {
      const reason = item.rejectionReason || 'other'
      if (rejectionStats.hasOwnProperty(reason)) {
        rejectionStats[reason]++
      } else {
        rejectionStats.other++
      }
    })

    return {
      success: true,
      accepted: results.accepted,
      rejected: results.rejected,
      stats: {
        total: results.total,
        accepted: results.accepted.length,
        rejected: results.rejected.length,
        rejectionReasons: rejectionStats
      }
    }

  } catch (error) {
    return {
      success: false,
      error: error.message || '智能过滤过程发生错误',
      accepted: [],
      rejected: keywords.map(keyword => ({
        ...keyword,
        ai_filter_reason: '处理过程出错',
        rejectionReason: 'process_error'
      })),
      stats: {
        total: keywords.length,
        accepted: 0,
        rejected: keywords.length
      }
    }
  }
}

/**
 * 保存关键词数据到PHP API
 * @param {Object} parseResult - 解析结果
 * @param {Array} processedKeywords - 处理后的关键词
 * @param {Object} aiFilterStats - AI过滤统计信息
 * @returns {Promise<Object>} 保存结果
 */
export async function saveToAPI(parseResult, processedKeywords, aiFilterStats = null) {
  try {
    // 如果没有关键词要保存，直接返回成功
    if (!processedKeywords || processedKeywords.length === 0) {
      return {
        success: true,
        totalImported: 0,
        duplicates: [],
        errors: []
      }
    }

    // 格式化关键词数据用于PHP API
    // 只发送基本字段，其他字段由其他功能页面处理
    const keywordsForAPI = processedKeywords.map(item => ({
      keyword: item.originalKeyword || item.keyword, // 使用原始关键词
      data: {
        source: item.source || 'google_trends',
        import_date: item.importDate || new Date().toISOString().split('T')[0]
      }
    }))

    // 分批处理，每批50个关键词
    const batchSize = 50
    const batches = []
    for (let i = 0; i < keywordsForAPI.length; i += batchSize) {
      batches.push(keywordsForAPI.slice(i, i + batchSize))
    }

    // 记录所有批次的结果
    const results = {
      success: true,
      totalImported: 0,
      duplicates: [],
      errors: [],
      failedBatches: [],
      batchResults: [] // 记录每个批次的详细结果
    }

    // 处理每个批次
    for (let i = 0; i < batches.length; i++) {
      const batch = batches[i]
      const batchStartIndex = i * batchSize
      const batchEndIndex = batchStartIndex + batch.length
      
      try {
        // 添加批次信息到文件名
        const batchFilename = `${parseResult.filename}_batch${i + 1}of${batches.length}`

    // 调用PHP API进行批量导入
        const apiResponse = await keywordAPI.batchImport(batch, batchFilename, aiFilterStats)

        // 记录批次结果
        results.batchResults.push({
          batchIndex: i,
          startIndex: batchStartIndex,
          endIndex: batchEndIndex,
          success: true,
          imported: apiResponse.data?.import?.successfully_imported || 0,
          skipped: apiResponse.data?.import?.database_skipped || 0,
          duplicates: apiResponse.data?.duplicates || [],
          errors: apiResponse.data?.errors || []
        })

        // 累加成功结果
        if (apiResponse.data) {
          results.totalImported += apiResponse.data.import.successfully_imported || 0
          if (apiResponse.data.duplicates) {
            results.duplicates.push(...apiResponse.data.duplicates.map(d => ({
              ...d,
              batchIndex: i,
              batchRange: `${batchStartIndex + 1}-${batchEndIndex}`
            })))
          }
          if (apiResponse.data.errors) {
            results.errors.push(...apiResponse.data.errors.map(e => ({
              ...e,
              batchIndex: i,
              batchRange: `${batchStartIndex + 1}-${batchEndIndex}`
            })))
          }
        }

      } catch (error) {
        // 记录失败的批次
        const failedBatch = {
          batchIndex: i,
          startIndex: batchStartIndex,
          endIndex: batchEndIndex,
          keywords: batch.map(k => k.keyword),
          error: error.message,
          timestamp: new Date().toISOString()
        }
        
        results.failedBatches.push(failedBatch)
        results.batchResults.push({
          ...failedBatch,
          success: false,
          imported: 0,
          skipped: 0,
          duplicates: [],
          errors: [{ error: error.message }]
        })
        
        // 继续处理下一批，不中断整个过程
      }

      // 添加延迟避免API限制
      if (i < batches.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 1000))
      }
    }

    // 如果有失败的批次，在返回结果中标记部分成功
    if (results.failedBatches.length > 0) {
      results.success = false
      results.message = `部分批次导入失败 (${results.failedBatches.length}/${batches.length} 批次失败)`

      // 如果全部批次都失败了，则返回完全失败
      if (results.failedBatches.length === batches.length) {
        return {
          success: false,
          error: '所有批次导入失败',
          totalImported: 0,
          duplicates: [],
          errors: results.failedBatches.map(b => ({
            keywords: b.keywords,
            error: b.error,
            batchIndex: b.batchIndex,
            batchRange: `${b.startIndex + 1}-${b.endIndex}`
          })),
          batchResults: results.batchResults
        }
      }
    }

    return {
      success: results.success,
      totalImported: results.totalImported,
      duplicates: results.duplicates,
      errors: results.errors,
      failedBatches: results.failedBatches,
      batchResults: results.batchResults,
      message: results.success ? 
        `成功导入 ${results.totalImported} 个关键词` : 
        `部分导入成功：${results.totalImported} 个关键词，${results.failedBatches.length} 个批次失败`
    }

  } catch (error) {
    return {
      success: false,
      error: error.message || '保存失败',
      totalImported: 0,
      duplicates: [],
      errors: [{
        error: error.message || '未知错误',
        stack: error.stack
      }],
      batchResults: []
    }
  }
}

/**
 * 获取指定日期的原始数据
 * @returns {Object|null} 原始数据或null
 * 注意：现在数据直接存储在远程数据库中，不再使用本地文件
 */
export function getRawData() {
  // 数据现在存储在远程数据库中，不再使用本地文件
  // 如果需要获取历史数据，应该通过API调用
  return null
}

/**
 * 获取所有可用的数据日期
 * @returns {Array} 日期数组
 */
export function getAvailableDates() {
  try {
    const keywordsDir = path.join(process.cwd(), 'data', 'keywords')
    
    if (!fs.existsSync(keywordsDir)) {
      return []
    }
    
    const dates = fs.readdirSync(keywordsDir)
      .filter(item => {
        const itemPath = path.join(keywordsDir, item)
        return fs.statSync(itemPath).isDirectory() && /^\d{4}-\d{2}-\d{2}$/.test(item)
      })
      .sort((a, b) => b.localeCompare(a)) // 最新日期在前
    
    return dates
    
  } catch (error) {
    return []
  }
}

/**
 * 保存手动导入的关键词到数据库（通过PHP API）
 * @param {Array} keywords - 关键词数组
 * @param {Object} importInfo - 导入信息
 * @returns {Promise<Object>} 保存结果
 */
export async function saveManualToAPI(keywords, importInfo = {}) {
  try {
    // 如果没有关键词要保存，直接返回成功
    if (!keywords || keywords.length === 0) {
      return {
        attempted: 0,
        imported: 0,
        duplicates: 0,
        errors: 0
      }
    }

    // 格式化关键词数据用于PHP API
    const keywordsForAPI = keywords.map(item => ({
      id: item.id,
      keyword: item.keyword,
      source: item.source || 'manual_import',
      importDate: item.importDate || new Date().toISOString().split('T')[0],
      importedAt: item.importedAt || new Date().toISOString()
    }))

    // 调用PHP API进行导入
    const response = await fetch('https://api.xstty.com/api/manual-import.php', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        keywords: keywordsForAPI,
        source: importInfo.source || 'manual_import'
      })
    })

    if (!response.ok) {
      throw new Error(`API请求失败: ${response.status}`)
    }

    const result = await response.json()

    if (!result.success) {
      throw new Error(result.message || '数据库保存失败')
    }

    return {
      attempted: result.data.attempted || 0,
      imported: result.data.imported || 0,
      duplicates: result.data.duplicates || 0,
      errors: result.data.errors || 0
    }

  } catch (error) {
    throw new Error(`数据库保存失败: ${error.message}`)
  }
}
