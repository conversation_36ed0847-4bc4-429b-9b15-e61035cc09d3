'use client'

import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card'
import { Badge } from '../../components/ui/badge'
import { Button } from '../../components/ui/button'
import { Input } from '../../components/ui/input'
import { Label } from '../../components/ui/label'
import { Textarea } from '../../components/ui/textarea'
import { Smartphone, ExternalLink, Copy, Trash2, Plus } from 'lucide-react'
import FrontendHeader from '../../components/frontend-header'
import FrontendFooter from '../../components/frontend-footer'

interface AppInfo {
  id: string
  originalUrl: string
  sensorTowerUrl: string
  appName?: string
}

export default function AppAnalyzerPage() {
  const [singleUrl, setSingleUrl] = useState<string>('')
  const [batchUrls, setBatchUrls] = useState<string>('')
  const [apps, setApps] = useState<AppInfo[]>([])
  const [mode, setMode] = useState<'single' | 'batch'>('single')

  // 从App Store URL提取App ID
  const extractAppId = (url: string): string | null => {
    const patterns = [
      /\/id(\d+)/,  // 标准格式: /id1672003086
      /\/app\/[^/]+\/id(\d+)/, // 完整格式: /app/app-name/id1672003086
    ]
    
    for (const pattern of patterns) {
      const match = url.match(pattern)
      if (match) {
        return match[1]
      }
    }
    return null
  }

  // 处理单个URL
  const processSingleUrl = () => {
    if (!singleUrl.trim()) return

    const appId = extractAppId(singleUrl.trim())
    if (!appId) {
      alert('无法从URL中提取App ID，请检查URL格式')
      return
    }

    const newApp: AppInfo = {
      id: appId,
      originalUrl: singleUrl.trim(),
      sensorTowerUrl: `https://app.sensortower.com/overview/${appId}?country=US`
    }

    setApps(prev => [...prev, newApp])
    setSingleUrl('')
  }

  // 处理批量URL
  const processBatchUrls = () => {
    if (!batchUrls.trim()) return

    const urls = batchUrls.trim().split('\n').filter(url => url.trim())
    const newApps: AppInfo[] = []

    urls.forEach(url => {
      const appId = extractAppId(url.trim())
      if (appId) {
        newApps.push({
          id: appId,
          originalUrl: url.trim(),
          sensorTowerUrl: `https://app.sensortower.com/overview/${appId}?country=US`
        })
      }
    })

    if (newApps.length === 0) {
      alert('未能从输入的URL中提取到有效的App ID')
      return
    }

    setApps(prev => [...prev, ...newApps])
    setBatchUrls('')
  }

  // 复制到剪贴板
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text).then(() => {
      // 可以添加toast提示
    })
  }

  // 清空所有结果
  const clearAll = () => {
    setApps([])
  }

  // 删除单个app
  const removeApp = (index: number) => {
    setApps(prev => prev.filter((_, i) => i !== index))
  }

  // 分组打开链接（每组5个）
  const openGroup = (startIndex: number) => {
    const group = apps.slice(startIndex, startIndex + 5)
    group.forEach(app => {
      window.open(app.sensorTowerUrl, '_blank')
    })
  }

  // 将apps按5个一组分组
  const groupedApps = []
  for (let i = 0; i < apps.length; i += 5) {
    groupedApps.push(apps.slice(i, i + 5))
  }

  return (
    <div className="min-h-screen bg-background">
      <FrontendHeader />
      
      {/* 页面标题 */}
      <section className="py-8 px-4 bg-gradient-to-r from-blue-50 to-purple-50 border-b">
        <div className="container mx-auto text-center">
          <div className="flex items-center justify-center mb-4">
            <div className="p-3 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl mr-4">
              <Smartphone className="h-8 w-8 text-white" />
            </div>
            <h1 className="text-4xl font-bold text-gray-900">App Store分析工具</h1>
          </div>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            将App Store链接转换为SensorTower分析链接，支持单个和批量处理
          </p>
        </div>
      </section>

      {/* 主要内容区域 */}
      <section className="py-8 px-4">
        <div className="container mx-auto max-w-7xl">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* 输入区域 */}
            <div className="space-y-6">
              {/* 模式切换 */}
              <Card>
                <CardHeader>
                  <CardTitle>处理模式</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex gap-2">
                    <Button
                      variant={mode === 'single' ? "default" : "outline"}
                      onClick={() => setMode('single')}
                      className={mode === 'single'
                        ? "bg-gradient-to-r from-blue-500 to-purple-600 text-white hover:from-blue-600 hover:to-purple-700"
                        : "border-2 border-gray-300 hover:border-gray-500"
                      }
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      单个处理
                    </Button>
                    <Button
                      variant={mode === 'batch' ? "default" : "outline"}
                      onClick={() => setMode('batch')}
                      className={mode === 'batch'
                        ? "bg-gradient-to-r from-blue-500 to-purple-600 text-white hover:from-blue-600 hover:to-purple-700"
                        : "border-2 border-gray-300 hover:border-gray-500"
                      }
                    >
                      <Smartphone className="h-4 w-4 mr-2" />
                      批量处理
                    </Button>
                  </div>
                </CardContent>
              </Card>

              {/* 单个处理 */}
              {mode === 'single' && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <Plus className="h-5 w-5 mr-2 text-blue-600" />
                      单个App Store链接
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <Label htmlFor="singleUrl">App Store链接</Label>
                      <Input
                        id="singleUrl"
                        type="text"
                        placeholder="https://apps.apple.com/us/app/chatbot-ai-ask-chatin/id1672003086"
                        value={singleUrl}
                        onChange={(e) => setSingleUrl(e.target.value)}
                        className="h-12 text-lg border-2 border-blue-200 focus:border-blue-500"
                      />
                    </div>
                    <Button
                      onClick={processSingleUrl}
                      className="w-full h-12 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white font-semibold"
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      添加分析
                    </Button>
                  </CardContent>
                </Card>
              )}

              {/* 批量处理 */}
              {mode === 'batch' && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <Smartphone className="h-5 w-5 mr-2 text-green-600" />
                      批量App Store链接
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <Label htmlFor="batchUrls">App Store链接（每行一个）</Label>
                      <Textarea
                        id="batchUrls"
                        placeholder={`https://apps.apple.com/us/app/chatbot-ai-ask-chatin/id1672003086\nhttps://apps.apple.com/us/app/another-app/id1234567890\nhttps://apps.apple.com/us/app/third-app/id9876543210`}
                        value={batchUrls}
                        onChange={(e) => setBatchUrls(e.target.value)}
                        className="min-h-[200px] text-sm border-2 border-green-200 focus:border-green-500"
                      />
                    </div>
                    <Button
                      onClick={processBatchUrls}
                      className="w-full h-12 bg-gradient-to-r from-green-500 to-teal-600 hover:from-green-600 hover:to-teal-700 text-white font-semibold"
                    >
                      <Smartphone className="h-4 w-4 mr-2" />
                      批量分析
                    </Button>
                  </CardContent>
                </Card>
              )}

              {/* 操作按钮 */}
              {apps.length > 0 && (
                <Card>
                  <CardContent className="pt-6">
                    <Button
                      onClick={clearAll}
                      variant="outline"
                      className="w-full h-12 border-2 border-red-300 hover:border-red-500 text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="h-4 w-4 mr-2" />
                      清空所有结果
                    </Button>
                  </CardContent>
                </Card>
              )}
            </div>

            {/* 结果显示区域 */}
            <div className="space-y-6">
              {apps.length === 0 ? (
                <Card>
                  <CardContent className="text-center py-12">
                    <Smartphone className="h-16 w-16 mx-auto text-gray-400 mb-4" />
                    <h3 className="text-xl font-semibold text-gray-600 mb-2">等待处理</h3>
                    <p className="text-gray-500">
                      请输入App Store链接并点击处理按钮
                    </p>
                  </CardContent>
                </Card>
              ) : (
                <>
                  {/* 统计信息 */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center justify-between">
                        <span>处理结果</span>
                        <Badge className="bg-blue-100 text-blue-700">
                          {apps.length} 个应用
                        </Badge>
                      </CardTitle>
                    </CardHeader>
                  </Card>

                  {/* 分组显示 */}
                  {groupedApps.map((group, groupIndex) => (
                    <Card key={groupIndex}>
                      <CardHeader>
                        <CardTitle className="flex items-center justify-between">
                          <span>第 {groupIndex + 1} 组</span>
                          <div className="flex gap-2">
                            <Badge variant="outline">{group.length} 个应用</Badge>
                            <Button
                              size="sm"
                              onClick={() => openGroup(groupIndex * 5)}
                              className="h-8 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white"
                            >
                              <ExternalLink className="h-3 w-3 mr-1" />
                              打开全部
                            </Button>
                          </div>
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-3">
                          {group.map((app, appIndex) => {
                            const globalIndex = groupIndex * 5 + appIndex
                            return (
                              <div key={globalIndex} className="p-3 bg-gray-50 rounded-lg">
                                <div className="flex items-center justify-between mb-2">
                                  <Badge className="bg-blue-100 text-blue-700">
                                    ID: {app.id}
                                  </Badge>
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    onClick={() => removeApp(globalIndex)}
                                    className="h-6 w-6 p-0 border-red-300 hover:border-red-500 text-red-600"
                                  >
                                    <Trash2 className="h-3 w-3" />
                                  </Button>
                                </div>
                                <div className="flex gap-2">
                                  <Button
                                    size="sm"
                                    asChild
                                    className="flex-1 h-8 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white"
                                  >
                                    <a
                                      href={app.sensorTowerUrl}
                                      target="_blank"
                                      rel="noopener noreferrer"
                                    >
                                      <ExternalLink className="h-3 w-3 mr-1" />
                                      SensorTower
                                    </a>
                                  </Button>
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    onClick={() => copyToClipboard(app.sensorTowerUrl)}
                                    className="h-8 border-gray-300 hover:border-gray-500"
                                  >
                                    <Copy className="h-3 w-3" />
                                  </Button>
                                </div>
                              </div>
                            )
                          })}
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </>
              )}
            </div>
          </div>
        </div>
      </section>

      <FrontendFooter />
    </div>
  )
}
