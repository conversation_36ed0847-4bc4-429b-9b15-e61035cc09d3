export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({
      success: false,
      message: 'Method not allowed'
    })
  }

  try {
    const { keyword_id } = req.query

    // 验证参数
    if (!keyword_id) {
      return res.status(400).json({
        success: false,
        message: '关键词ID不能为空'
      })
    }

    const apiBaseUrl = process.env.NEXT_PUBLIC_PHP_API_URL || 'https://api.xstty.com/api'

    // 调用PHP API获取建议
    const response = await fetch(`${apiBaseUrl}/keyword_suggestions.php?keyword_id=${encodeURIComponent(keyword_id)}`)
    const data = await response.json()

    if (data.success) {
      res.status(200).json({
        success: true,
        data: data.data
      })
    } else {
      res.status(400).json({
        success: false,
        message: data.error || '获取建议失败'
      })
    }

  } catch (error) {
    console.error('获取建议失败:', error)
    res.status(500).json({
      success: false,
      message: '服务器错误，请稍后重试'
    })
  }
}
