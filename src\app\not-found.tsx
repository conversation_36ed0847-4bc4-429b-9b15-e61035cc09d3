import Link from 'next/link'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Search, Home, TrendingUp, ArrowRight } from 'lucide-react'
import FrontendHeader from '@/components/frontend-header'
import FrontendFooter from '@/components/frontend-footer'

export default function NotFound() {
  return (
    <div className="min-h-screen bg-background">
      {/* 使用统一前台顶部组件 */}
      <FrontendHeader />

      {/* 404错误区域 - 与首页样式保持一致 */}
      <section className="relative py-16 px-4 bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 overflow-hidden">
        {/* 背景装饰 - 复用首页样式 */}
        <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
        <div className="absolute top-10 right-10 w-72 h-72 bg-blue-200 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob"></div>
        <div className="absolute top-20 left-10 w-72 h-72 bg-purple-200 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob animation-delay-2000"></div>
        <div className="absolute bottom-10 left-20 w-72 h-72 bg-pink-200 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob animation-delay-4000"></div>

        <div className="container mx-auto relative z-10">
          <div className="text-center mb-12">
            {/* 404标题 */}
            <div className="mb-8">
              <h1 className="text-8xl md:text-9xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 bg-clip-text text-transparent mb-6">
                404
              </h1>
              <div className="text-3xl md:text-4xl font-bold text-foreground mb-4 flex items-center justify-center space-x-3">
                <Search className="h-8 w-8 text-primary" />
                <span>关键词搜索失败</span>
              </div>
              <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto leading-relaxed">
                抱歉，这个页面就像<span className="font-semibold text-primary">零搜索量关键词</span>一样 - 完全找不到！
              </p>
            </div>

            {/* SEO小贴士卡片 */}
            <div className="max-w-2xl mx-auto mb-8">
              <Card className="idea-card-enhanced">
                <CardContent className="p-8">
                  <div className="flex items-center justify-center space-x-3 mb-4">
                    <TrendingUp className="h-6 w-6 text-primary" />
                    <h3 className="text-xl font-bold text-foreground">SEO小贴士</h3>
                  </div>
                  <p className="text-muted-foreground leading-relaxed">
                    就像选择关键词一样，选择正确的URL路径也很重要。
                    检查一下链接是否输入正确吧！
                  </p>
                </CardContent>
              </Card>
            </div>

            {/* 操作按钮 */}
            <div className="space-y-6">
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button asChild size="lg" className="btn-primary-enhanced px-8 py-3">
                  <Link href="/">
                    <Home className="h-5 w-5 mr-2" />
                    返回首页
                  </Link>
                </Button>
                <Button asChild variant="outline" size="lg" className="btn-outline-enhanced px-8 py-3">
                  <Link href="/console/analyze">
                    <TrendingUp className="h-5 w-5 mr-2" />
                    关键词分析
                  </Link>
                </Button>
              </div>
              
              <div className="text-sm text-muted-foreground">
                或者试试我们的 
                <Link href="/console/upload" className="text-primary hover:underline font-medium mx-1">
                  数据上传工具
                </Link>
                <ArrowRight className="h-4 w-4 inline mx-1" />
                开始您的创意发现之旅
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 使用统一前台底部组件 */}
      <FrontendFooter />
    </div>
  )
}
