/**
 * AI处理器
 * 专门用于关键词分析和分类
 */

import { getCurrentDateString, getCurrentYear, getTodayString } from './date-utils.js'
import googleUsageTracker from './google-usage-tracker.js'

// Google Trends分类列表
const GOOGLE_TRENDS_CATEGORIES = [
  'Arts & Entertainment', 'Autos & Vehicles', 'Beauty & Fitness', 'Books & Literature',
  'Business & Industrial', 'Computers & Electronics', 'Finance', 'Food & Drink',
  'Games', 'Health', 'Hobbies & Leisure', 'Home & Garden', 'Internet & Telecom',
  'Jobs & Education', 'Law & Government', 'News', 'Online Communities',
  'People & Society', 'Pets & Animals', 'Real Estate', 'Reference',
  'Science', 'Shopping', 'Sports', 'Travel', 'World Localities'
]

/**
 * 信号量类 - 控制并发数量
 */
class Semaphore {
  constructor(max) {
    this.max = max;
    this.current = 0;
    this.queue = [];
  }

  async acquire() {
    return new Promise((resolve) => {
      if (this.current < this.max) {
        this.current++;
        resolve();
      } else {
        this.queue.push(resolve);
      }
    });
  }

  release() {
    this.current--;
    if (this.queue.length > 0) {
      this.current++;
      const resolve = this.queue.shift();
      resolve();
    }
  }
}



class AIProcessor {
  constructor() {
    // SiliconFlow DeepSeek-V3配置 - 从环境变量读取
    this.baseURL = 'https://api.siliconflow.cn/v1';
    this.model = process.env.SILICONFLOW_COMPETITION_MODEL || 'moonshotai/Kimi-K2-Instruct';
    this.rateLimit = 60; // RPM (SiliconFlow限制)
    this.requestInterval = 1000; // ms (1秒间隔)
    this.maxConcurrency = 2; // 降低并发数，确保稳定性

    this.lastRequestTime = 0;

    // 初始化SiliconFlow API Keys (动态生成10个)
    this.apiKeys = [];
    for (let i = 1; i <= 10; i++) {
      const apiKey = process.env[`SILICONFLOW_API_KEY_${i}`];
      if (apiKey) {
        this.apiKeys.push(apiKey);
      }
    }

    // 初始化使用统计
    this.keyUsage = new Map();
    this.apiKeys.forEach(key => this.keyUsage.set(key, 0));

    // 初始化Google Token管理
    this.googleTokens = [];
    this.currentTokenIndex = 0;
    this.initializeGoogleTokens();
  }



  /**
   * 获取随机API Key（与其他SiliconFlow调用保持一致）
   */
  getNextApiKey() {
    if (this.apiKeys.length === 0) {
      throw new Error('没有可用的SiliconFlow API Key');
    }

    // 随机选择API Key（与项目中其他SiliconFlow调用保持一致）
    const apiKey = this.apiKeys[Math.floor(Math.random() * this.apiKeys.length)];

    // 更新使用统计
    this.keyUsage.set(apiKey, (this.keyUsage.get(apiKey) || 0) + 1);
    return apiKey;
  }

  /**
   * 获取模型 (智谱AI GLM-4-Flash)
   */
  getNextModel() {
    return this.model;
  }

  /**
   * 获取API使用统计
   */
  getUsageStats() {
    return {
      keyCount: 1,
      usage: Object.fromEntries(this.keyUsage),
      categories: GOOGLE_TRENDS_CATEGORIES,
      model: this.model
    };
  }

  /**
   * 根据竞争分数获取颜色编码
   */
  getCompetitionColor(score) {
    if (score <= 3) return 'green';    // 简单
    if (score <= 7) return 'yellow';   // 中等
    return 'red';                      // 困难
  }

  /**
   * 根据竞争分数获取难度等级
   */
  getCompetitionLevel(score) {
    if (score <= 3) return 'easy';     // 简单 (1-3)
    if (score <= 7) return 'medium';   // 中等 (4-7)
    return 'hard';                     // 困难 (8-10)
  }

  /**
   * 根据关键词内容智能选择默认分类
   */
  getDefaultCategory(keyword) {
    const lowerKeyword = keyword.toLowerCase();

    // 简单的关键词分类映射
    const categoryMappings = {
      'Games': ['game', 'play', 'gaming', 'video', 'mobile', 'online', 'puzzle', 'strategy'],
      'Health': ['health', 'fitness', 'medical', 'doctor', 'medicine', 'wellness', 'diet', 'exercise'],
      'Shopping': ['buy', 'shop', 'store', 'price', 'sale', 'discount', 'product', 'purchase'],
      'Computers & Electronics': ['computer', 'software', 'tech', 'digital', 'app', 'device', 'electronic'],
      'Business & Industrial': ['business', 'company', 'service', 'professional', 'industry', 'corporate'],
      'Arts & Entertainment': ['music', 'movie', 'art', 'entertainment', 'show', 'celebrity', 'film'],
      'Food & Drink': ['food', 'restaurant', 'recipe', 'cooking', 'drink', 'coffee', 'meal'],
      'Travel': ['travel', 'hotel', 'flight', 'vacation', 'trip', 'tourism', 'destination'],
      'Education': ['education', 'school', 'learn', 'study', 'course', 'university', 'training']
    };

    // 查找匹配的分类
    for (const [category, keywords] of Object.entries(categoryMappings)) {
      if (keywords.some(kw => lowerKeyword.includes(kw))) {
        return category;
      }
    }

    // 默认分类
    return 'Reference';
  }



  /**
   * 获取Google相关词建议（单轮快速获取）
   */
  async getGoogleSuggestions(keyword) {
    try {
      // 只进行第1轮：获取主关键词的相关词
      const suggestions = await this.fetchGoogleSuggestions(keyword);

      // 过滤和整理
      const filteredSuggestions = suggestions
        .filter(s => s && s.length > 2 && s !== keyword)
        .slice(0, 10); // 最多保留10个相关词

      return {
        mainKeyword: keyword,
        relatedKeywords: filteredSuggestions,
        totalFound: filteredSuggestions.length,
        rounds: 1
      };

    } catch (error) {
      return {
        mainKeyword: keyword,
        relatedKeywords: [],
        totalFound: 0,
        rounds: 0,
        error: error.message
      };
    }
  }

  /**
   * 调用Google建议API获取相关词
   */
  async fetchGoogleSuggestions(keyword) {
    const suggestions = [];

    // 尝试两个不同的Google建议API
    const apis = [
      process.env.GOOGLE_SUGGESTIONS_API_1 || 'https://suggestqueries.google.com/complete/search',
      process.env.GOOGLE_SUGGESTIONS_API_2 || 'https://clients1.google.com/complete/search'
    ];

    for (const apiUrl of apis) {
      try {
        const params = new URLSearchParams({
          client: 'firefox',
          q: keyword,
          hl: 'en'
        });

        const response = await fetch(`${apiUrl}?${params.toString()}`, {
          method: 'GET',
          timeout: 5000,
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
          }
        });

        if (response.ok) {
          const text = await response.text();
          // Google建议API返回JSONP格式，需要解析
          const jsonMatch = text.match(/\[.*\]/);
          if (jsonMatch) {
            const data = JSON.parse(jsonMatch[0]);
            if (Array.isArray(data) && data.length > 1 && Array.isArray(data[1])) {
              suggestions.push(...data[1].slice(0, 10));
              break; // 成功获取就退出循环
            }
          }
        }
      } catch (error) {
        continue; // 尝试下一个API
      }
    }

    return suggestions;
  }

  /**
   * 调用Google Custom Search API获取关键词相关信息
   */
  async getGoogleSearchInfo(keyword) {
    let lastError = null;

    // 尝试所有可用的token，直到成功或全部失败
    for (let attempt = 0; attempt < 10; attempt++) {
      try {
        // 获取可用的Google API token
        const token = await this.getAvailableGoogleToken();

        const params = new URLSearchParams({
          key: token.key,
          cx: token.cx,
          q: keyword,
          num: 10,
          lr: 'lang_en',
          safe: 'medium',
          fields: 'searchInformation,items(title,link,snippet,displayLink,pagemap/metatags)'
        });

        const apiUrl = `https://www.googleapis.com/customsearch/v1?${params.toString()}`;

        const response = await fetch(apiUrl, {
          method: 'GET',
          timeout: 10000 // 10秒超时
        });

        if (!response.ok) {
          // 如果是429错误（配额超限），标记token失败并尝试下一个
          if (response.status === 429) {
            await this.recordGoogleTokenUsage(token.id, false);
            lastError = new Error(`Token ${token.id} 配额已用完`);
            continue;
          }
          throw new Error('Google搜索API请求失败');
        }

        const data = await response.json();

        // 记录API使用成功
        await this.recordGoogleTokenUsage(token.id, true);

        // 详细输出原始数据
        const searchInfo = data.searchInformation || {};
        const items = data.items || [];

        // 提取有用信息
        const info = {
          // 搜索量数据
          totalResults: parseInt(searchInfo.totalResults || '0'),
          searchTime: parseFloat(searchInfo.searchTime || '0'),
          estimatedVolume: this.estimateVolumeFromGoogle(searchInfo.totalResults),

          // 竞争对手数据
          topCompetitors: items.slice(0, 5).map(item => ({
            domain: item.displayLink,
            title: item.title,
            url: item.link,
            snippet: item.snippet
          })),

          // 内容分析数据
          contentThemes: this.extractContentThemes(items),
          titlePatterns: this.analyzeTitlePatterns(items),

          // 市场成熟度
          marketMaturity: this.assessMarketMaturity(items, searchInfo.totalResults),

          // 原始数据
          rawSearchInfo: searchInfo,
          rawItems: items.slice(0, 3)
        };

        return info;

      } catch (error) {
        // 如果是配额错误，记录失败并尝试下一个token
        if (error.message.includes('quota') || error.message.includes('limit') || error.message.includes('429')) {
          this.recordGoogleTokenUsage(token.id, false);
          lastError = error;
          continue;
        }
        // 其他错误直接抛出
        throw error;
      }
    }

    // 所有token都失败了，返回默认值
    return {
      totalResults: 0,
      searchTime: 0,
      estimatedVolume: 'unknown',
      topCompetitors: [],
      contentThemes: [],
      titlePatterns: { commonWords: [], averageLength: 0 },
      marketMaturity: 'unknown',
      rawSearchInfo: {},
      rawItems: [],
      error: lastError?.message || '所有Google API token都已用完'
    };
  }

  /**
   * SiliconFlow DeepSeek-V3请求频率控制 (基于60 RPM限制)
   */
  async rateLimitDelay() {
    const now = Date.now();
    const timeSinceLastRequest = now - this.lastRequestTime;

    // 60 RPM = 1000ms间隔 (每1秒一次请求)
    const minInterval = this.requestInterval;

    if (timeSinceLastRequest < minInterval) {
      const delayTime = minInterval - timeSinceLastRequest;
      await new Promise(resolve => setTimeout(resolve, delayTime));
    }

    this.lastRequestTime = Date.now();
  }

  /**
   * 调用AI API（带重试机制 - DeepSeek-V3优化版）
   */
  async callAPI(prompt, model = null, retries = 3) {
    for (let attempt = 0; attempt <= retries; attempt++) {
      try {
        await this.rateLimitDelay();

        const apiKey = this.getNextApiKey();
        const selectedModel = model || this.getNextModel();

        return await this.makeAPIRequest(prompt, apiKey, selectedModel);
      } catch (error) {
        const isTimeout = error.name === 'AbortError' || error.message.includes('aborted');
        const isRateLimit = error.message.includes('429') || error.message.includes('rate limit');

        if (attempt === retries) {
          throw error; // 最后一次尝试失败，抛出错误
        }

        // 根据错误类型调整等待时间
        let waitTime = 2000 * (attempt + 1); // 基础等待时间
        if (isTimeout) {
          waitTime = 8000 * (attempt + 1); // 超时错误等待更久
        } else if (isRateLimit) {
          waitTime = 10000 * (attempt + 1); // 频率限制等待最久
        }

        await new Promise(resolve => setTimeout(resolve, waitTime));
      }
    }
  }

  /**
   * 执行API请求 - SiliconFlow DeepSeek-V3
   */
  async makeAPIRequest(prompt, apiKey, selectedModel) {
    try {
      // DeepSeek-V3优化超时时间，30秒足够
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 30000); // 30秒超时

      const response = await fetch(`${this.baseURL}/chat/completions`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          model: selectedModel,
          messages: [
            {
              role: 'system',
              content: '你是专业的SEO关键词分析专家。请基于提供的Google搜索数据和相关词信息，分析关键词并返回准确的JSON格式结果。不要使用推理过程，直接给出分析结果。'
            },
            {
              role: 'user',
              content: prompt
            }
          ],
          temperature: 0.6,    // 使用Kimi推荐值
          max_tokens: 4000,    // 提升token数量支持更详细分析
          top_p: 0.7,          // 保持采样参数
          frequency_penalty: 0.0,  // 添加Kimi支持的参数
          stream: false        // 禁用流式输出
        }),
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error('AI分析服务暂时不可用');
      }

      const data = await response.json();

      // 智谱AI标准响应格式处理
      if (!data.choices || !data.choices[0] || !data.choices[0].message) {
        throw new Error('AI分析服务暂时不可用');
      }

      const message = data.choices[0].message;
      const content = message.content;

      return content;
    } catch (error) {
      throw error;
    }
  }

  /**
   * 快速分析单个关键词（批量处理优化版）
   */
  async analyzeKeywordFast(keyword) {
    // 1. 并行获取Google搜索信息和相关词建议（快速版）
    const [googleSearchInfo, googleSuggestions] = await Promise.all([
      this.getGoogleSearchInfo(keyword),
      this.getGoogleSuggestions(keyword)
    ]);

    // 2. 构建增强的分析Prompt
    let contextInfo = '';

    // Google搜索数据（增强版）
    if (googleSearchInfo.totalResults > 0) {
      contextInfo += `搜索结果数量: ${googleSearchInfo.totalResults.toLocaleString()}\n`;
    }
    if (googleSearchInfo.estimatedVolume) {
      contextInfo += `搜索热度: ${googleSearchInfo.estimatedVolume}\n`;
    }
    if (googleSearchInfo.topCompetitors.length > 0) {
      contextInfo += `主要竞争对手详情:\n`;
      googleSearchInfo.topCompetitors.slice(0, 3).forEach((competitor, index) => {
        contextInfo += `${index + 1}. ${competitor.domain}\n`;
        contextInfo += `   标题: ${competitor.title || '无标题'}\n`;
        contextInfo += `   描述: ${competitor.snippet || '无描述'}\n`;
      });
    }
    if (googleSearchInfo.contentThemes.length > 0) {
      contextInfo += `内容主题: ${googleSearchInfo.contentThemes.slice(0, 5).map(t => `${t.word}(${t.count}次)`).join(', ')}\n`;
    }
    if (googleSearchInfo.marketMaturity) {
      contextInfo += `市场成熟度: ${googleSearchInfo.marketMaturity}\n`;
    }
    if (googleSearchInfo.titlePatterns.commonWords.length > 0) {
      contextInfo += `标题关键词: ${googleSearchInfo.titlePatterns.commonWords.slice(0, 5).join(', ')}\n`;
    }

    // Google相关词数据（增强版）
    if (googleSuggestions.relatedKeywords.length > 0) {
      contextInfo += `相关搜索词: ${googleSuggestions.relatedKeywords.slice(0, 6).join(', ')}\n`;
      contextInfo += `用户搜索行为: 共发现${googleSuggestions.totalFound}个相关搜索词\n`;
    }

    // 获取当前时间信息
    const currentDateStr = getCurrentDateString()
    const currentYear = getCurrentYear()

    const prompt = `当前时间：${currentDateStr}（${currentYear}年）

作为顶级SEO分析专家，请基于以下Google真实搜索数据对关键词"${keyword}"进行深度分析：

=== 数据源分析 ===
${contextInfo}

请进行以下维度的深度分析：

## 1. 用户搜索意图分析
- 分析相关搜索词的模式和层次
- 识别用户的核心需求和次要需求
- 判断搜索意图的商业价值

## 2. 用户痛点挖掘
- 基于竞争对手标题分析现有内容覆盖情况
- 通过相关词识别用户未被满足的需求
- 分析内容主题的空缺领域

## 3. 竞争环境评估
- 分析竞争对手的域名权威性和内容质量
- 评估市场饱和度和进入难度
- 识别竞争优势和劣势

## 4. SERP结构分析
- 分析搜索结果页面的内容类型分布
- 识别主导内容格式和用户偏好
- 评估新内容的机会点

返回详细分析的JSON格式：

{
  "user_intent": "基于${googleSuggestions.totalFound}个相关搜索词和内容主题深度分析用户搜索意图，包括主要需求、次要需求和商业意图。请以自然段落形式描述，不使用数字编号（150字以内）",
  "user_pain_point": "通过分析竞争对手内容覆盖和相关词空缺，识别用户当前未被满足的具体痛点和需求。请以自然段落形式描述，不使用数字编号（150字以内）",
  "competition_level": "easy/medium/hard",
  "competition_score": 5,
  "competition_description": "基于${googleSearchInfo.totalResults?.toLocaleString() || '未知'}个搜索结果、竞争对手域名质量和市场成熟度的详细竞争分析。请以自然段落形式描述，不使用数字编号（150字以内）",
  "category": "从26个分类中选择最合适的一个",
  "serp_analysis": "基于内容主题分布、竞争对手类型和标题模式的SERP结构深度分析，包括内容机会识别。请以自然段落形式描述，不使用数字编号（150字以内）"
}

可选分类：
${GOOGLE_TRENDS_CATEGORIES.join(', ')}

分析要求：
1. 必须深度利用所有提供的数据进行分析，展现专业洞察
2. 每个字段都要包含具体的数据支撑和分析逻辑
3. 避免模板化回答，提供针对性的专业分析
4. 分析要体现对市场、用户、竞争的深度理解
5. 所有分析内容请使用自然段落形式，不要使用数字编号（如1. 2. 3.）或列表符号
6. 直接返回JSON，不要其他文字`;

    try {
      const response = await this.callAPI(prompt);
      let analysis;

      // 更强的JSON解析
      try {
        // 清理响应内容
        const cleanResponse = response.trim().replace(/```json|```/g, '');
        analysis = JSON.parse(cleanResponse);
      } catch {
        // 尝试提取JSON
        const jsonMatch = response.match(/\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}/);
        if (jsonMatch) {
          analysis = JSON.parse(jsonMatch[0]);
        } else {
          throw new Error('AI分析服务暂时不可用');
        }
      }

      // 验证和修复分类
      if (!analysis.category || !GOOGLE_TRENDS_CATEGORIES.includes(analysis.category)) {
        // 根据关键词内容智能选择默认分类
        analysis.category = this.getDefaultCategory(keyword);
      }

      // 验证和处理竞争分数
      const score = parseInt(analysis.competition_score) || 5;
      analysis.competition_color = this.getCompetitionColor(score);
      analysis.competition_score = score;
      // 确保竞争等级与分数一致
      analysis.competition_level = this.getCompetitionLevel(score);

      // 不在这里生成域名，将在保存分析结果时处理

      return {
        success: true,
        analysis: {
          ...analysis,
          processed_at: new Date().toISOString(),
          confidence: 0.85
        }
      };

    } catch (error) {
      return {
        success: false,
        error: '分析失败，请重试'
      };
    }
  }

  /**
   * 分析单个关键词（完整版 - 增强版）
   */
  async analyzeKeyword(keyword) {
    // 1. 并行获取Google搜索信息和相关词建议
    const [googleSearchInfo, googleSuggestions] = await Promise.all([
      this.getGoogleSearchInfo(keyword),
      this.getGoogleSuggestions(keyword)
    ]);

    // 2. 构建增强的分析Prompt，整合所有数据
    let contextInfo = '';

    // Google搜索数据
    if (googleSearchInfo.totalResults > 0) {
      contextInfo += `搜索结果数量: ${googleSearchInfo.totalResults.toLocaleString()}\n`;
    }
    if (googleSearchInfo.estimatedVolume) {
      contextInfo += `搜索热度: ${googleSearchInfo.estimatedVolume}\n`;
    }
    if (googleSearchInfo.topCompetitors.length > 0) {
      contextInfo += `主要竞争对手详情:\n`;
      googleSearchInfo.topCompetitors.slice(0, 5).forEach((competitor, index) => {
        contextInfo += `${index + 1}. ${competitor.domain}\n`;
        contextInfo += `   标题: ${competitor.title || '无标题'}\n`;
        contextInfo += `   描述: ${competitor.snippet || '无描述'}\n`;
      });
    }
    if (googleSearchInfo.contentThemes.length > 0) {
      contextInfo += `内容主题: ${googleSearchInfo.contentThemes.slice(0, 8).map(t => `${t.word}(${t.count})`).join(', ')}\n`;
    }
    if (googleSearchInfo.marketMaturity) {
      contextInfo += `市场成熟度: ${googleSearchInfo.marketMaturity}\n`;
    }
    if (googleSearchInfo.titlePatterns.commonWords.length > 0) {
      contextInfo += `标题模式: ${googleSearchInfo.titlePatterns.commonWords.join(', ')}\n`;
    }

    // Google相关词数据
    if (googleSuggestions.relatedKeywords.length > 0) {
      contextInfo += `相关搜索词: ${googleSuggestions.relatedKeywords.slice(0, 8).join(', ')}\n`;
      contextInfo += `相关词总数: ${googleSuggestions.totalFound}个\n`;
    }

    // 获取当前时间信息
    const currentDateStr = getCurrentDateString()
    const currentYear = getCurrentYear()

    const prompt = `当前时间：${currentDateStr}（${currentYear}年）

作为顶级SEO分析专家，请基于以下Google真实搜索数据对关键词"${keyword}"进行深度分析：

=== 数据源分析 ===
${contextInfo}

请进行以下维度的深度分析：

## 1. 用户搜索意图分析
- 分析相关搜索词的模式和层次
- 识别用户的核心需求和次要需求
- 判断搜索意图的商业价值

## 2. 用户痛点挖掘
- 基于竞争对手标题分析现有内容覆盖情况
- 通过相关词识别用户未被满足的需求
- 分析内容主题的空缺领域

## 3. 竞争环境评估
- 分析竞争对手的域名权威性和内容质量
- 评估市场饱和度和进入难度
- 识别竞争优势和劣势

## 4. SERP结构分析
- 分析搜索结果页面的内容类型分布
- 识别主导内容格式和用户偏好
- 评估新内容的机会点

返回详细分析的JSON格式：

{
  "user_intent": "基于${googleSuggestions.totalFound}个相关搜索词和内容主题深度分析用户搜索意图，包括主要需求、次要需求和商业意图。请以自然段落形式描述，不使用数字编号（150字以内）",
  "user_pain_point": "通过分析竞争对手内容覆盖和相关词空缺，识别用户当前未被满足的具体痛点和需求。请以自然段落形式描述，不使用数字编号（150字以内）",
  "competition_level": "easy/medium/hard",
  "competition_score": 5,
  "competition_description": "基于${googleSearchInfo.totalResults?.toLocaleString() || '未知'}个搜索结果、竞争对手域名质量和市场成熟度的详细竞争分析。请以自然段落形式描述，不使用数字编号（150字以内）",
  "category": "从26个分类中选择最合适的一个",
  "serp_analysis": "基于内容主题分布、竞争对手类型和标题模式的SERP结构深度分析，包括内容机会识别。请以自然段落形式描述，不使用数字编号（150字以内）"
}

可选分类：
${GOOGLE_TRENDS_CATEGORIES.join(', ')}

分析要求：
1. 必须深度利用所有提供的数据进行分析，展现专业洞察
2. 每个字段都要包含具体的数据支撑和分析逻辑
3. 避免模板化回答，提供针对性的专业分析
4. 分析要体现对市场、用户、竞争的深度理解
5. 所有分析内容请使用自然段落形式，不要使用数字编号（如1. 2. 3.）或列表符号
6. 直接返回JSON，不要其他文字`;

    try {
      const response = await this.callAPI(prompt);

      // 尝试解析JSON
      let analysis;
      try {
        analysis = JSON.parse(response);
      } catch {
        // 如果解析失败，尝试提取JSON部分
        const jsonMatch = response.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          analysis = JSON.parse(jsonMatch[0]);
        } else {
          throw new Error('AI分析服务暂时不可用');
        }
      }

      // 验证必要字段
      const requiredFields = ['user_intent', 'user_pain_point', 'competition_level', 'competition_score', 'category'];
      for (const field of requiredFields) {
        if (!(field in analysis)) {
          throw new Error('AI分析服务暂时不可用');
        }
      }

      // 验证分类是否在允许列表中
      if (!GOOGLE_TRENDS_CATEGORIES.includes(analysis.category)) {
        throw new Error('AI分析服务暂时不可用');
      }

      // 验证竞争分数
      const score = parseInt(analysis.competition_score);
      if (isNaN(score) || score < 1 || score > 10) {
        throw new Error('AI分析服务暂时不可用');
      }

      // 添加颜色编码和等级
      analysis.competition_color = this.getCompetitionColor(score);
      analysis.competition_score = score;
      // 确保竞争等级与分数一致
      analysis.competition_level = this.getCompetitionLevel(score);

      // 不在这里生成域名，将在保存分析结果时处理

      return {
        success: true,
        analysis: {
          ...analysis,
          processed_at: new Date().toISOString(),
          confidence: 0.85
        }
      };

    } catch (error) {
      // 返回默认分析结果
      return {
        success: false,
        error: '分析失败，请重试',
        analysis: {
          category: 'tech',
          intent: 'informational',
          difficulty: 5,
          commercial_value: 5,
          trend_score: 5,
          related_keywords: [],
          long_tail_keywords: [],
          business_opportunities: [],
          content_suggestions: [],
          target_audience: '未知',
          seasonality: '无明显季节性',
          summary: '分析失败，使用默认值',
          processed_at: new Date().toISOString(),
          confidence: 0.1
        }
      };
    }
  }

  /**
   * SiliconFlow DeepSeek-V3批量分析关键词 (适配60 RPM限制，优化稳定性)
   */
  async analyzeKeywords(keywords, onProgress = null, concurrency = null) {
    // 使用配置中的最大并发数，DeepSeek-V3为2（确保复杂分析的稳定性）
    const actualConcurrency = concurrency || this.maxConcurrency;

    const total = keywords.length;
    const semaphore = new Semaphore(actualConcurrency);
    let completed = 0;

    const startTime = Date.now();

    // 创建所有分析任务
    const promises = keywords.map(async (keyword, index) => {
      await semaphore.acquire();

      try {

        const result = await this.analyzeKeywordFast(keyword);

        completed++;

        // 调用进度回调
        if (onProgress) {
          onProgress({
            current: completed,
            total,
            keyword,
            progress: (completed / total * 100).toFixed(1)
          });
        }

        return {
          index,
          keyword,
          ...result
        };

      } catch (error) {
        completed++;

        if (onProgress) {
          onProgress({
            current: completed,
            total,
            keyword,
            progress: (completed / total * 100).toFixed(1)
          });
        }

        return {
          index,
          keyword,
          success: false,
          error: '分析失败，请重试',
          analysis: null
        };
      } finally {
        semaphore.release();
      }
    });

    // 等待所有任务完成
    const allResults = await Promise.all(promises);

    // 计算性能统计
    const endTime = Date.now();
    const totalTime = (endTime - startTime) / 1000;
    const successCount = allResults.filter(r => r.success).length;
    const failureCount = allResults.filter(r => !r.success).length;

    // 按原始顺序排序结果
    allResults.sort((a, b) => a.index - b.index);

    return allResults.map(result => ({
      keyword: result.keyword,
      success: result.success,
      error: result.error,
      analysis: result.analysis
    }));
  }

  /**
   * 生成关键词拓展建议
   */
  async expandKeywords(baseKeywords) {
    // 获取当前时间信息
    const currentDateStr = getCurrentDateString();
    const currentYear = getCurrentYear();

    const prompt = `基于以下关键词：${baseKeywords.join(', ')}

当前时间：${currentDateStr}（${currentYear}年），请确保建议的内容和趋势是最新的。

请生成相关的关键词拓展建议，返回JSON格式：

{
  "expanded_keywords": ["拓展关键词1", "拓展关键词2", "拓展关键词3"],
  "trending_topics": ["相关热门话题1", "相关热门话题2"],
  "search_suggestions": ["搜索建议1", "搜索建议2", "搜索建议3"],
  "niche_opportunities": ["细分机会1", "细分机会2"]
}
`;

    try {
      const response = await this.callAPI(prompt);
      const result = JSON.parse(response);
      return {
        success: true,
        data: result
      };
    } catch (error) {
      return {
        success: false,
        error: '分析失败，请重试',
        data: {
          expanded_keywords: [],
          trending_topics: [],
          search_suggestions: [],
          niche_opportunities: []
        }
      };
    }
  }

  // ===== Google Token 管理方法 =====

  // 初始化Google Tokens (支持20个API)
  initializeGoogleTokens() {
    this.googleTokens = [];

    // 动态生成20个Google API配置
    for (let i = 1; i <= 20; i++) {
      const apiKey = process.env[`GOOGLE_SEARCH_API_KEY_${i}`];
      const cx = process.env[`GOOGLE_SEARCH_CX_${i}`];

      if (apiKey && cx) {
        this.googleTokens.push({
          id: i,
          key: apiKey,
          cx: cx,
          dailyUsage: 0,
          lastResetDate: getTodayString(),
          isActive: true
        });
      }
    }

  }

  // 获取可用的Google Token
  async getAvailableGoogleToken() {
    try {
      // 从数据库获取使用次数最少的密钥
      const keyIndex = await googleUsageTracker.getAvailableKey(100);

      // 根据密钥索引返回对应的token配置
      const tokens = this.getGoogleTokens();
      const selectedToken = tokens.find(token => token.id === keyIndex);

      if (!selectedToken) {
        // 如果没找到，根据keyIndex构建token
        return {
          id: keyIndex,
          key: process.env[`GOOGLE_SEARCH_API_KEY_${keyIndex}`],
          cx: process.env[`GOOGLE_SEARCH_CX_${keyIndex}`]
        };
      }

      return selectedToken;
    } catch (error) {
      // 如果数据库查询失败，使用原有逻辑
      const today = getTodayString();
      const maxDailyUsage = 100;

      // 重置每日计数
      this.googleTokens.forEach(token => {
        if (token.lastResetDate !== today) {
          token.dailyUsage = 0;
          token.lastResetDate = today;
          token.isActive = true;
        }
      });

      // 查找可用token
      const availableTokens = this.googleTokens.filter(token =>
        token.isActive && token.dailyUsage < maxDailyUsage
      );

      if (availableTokens.length === 0) {
        throw new Error('今日额度已用完');
      }

      // 选择使用次数最少的token
      const selectedToken = availableTokens.reduce((min, token) =>
        token.dailyUsage < min.dailyUsage ? token : min
      );

      return selectedToken;
    }
  }

  // 记录Token使用情况
  async recordGoogleTokenUsage(tokenId, success = true) {
    try {
      // 记录到数据库
      await googleUsageTracker.recordUsage(tokenId);
    } catch (error) {
      // 静默处理数据库记录错误
    }

    // 保持原有的内存记录逻辑作为备份
    const token = this.googleTokens.find(t => t.id === tokenId);
    if (token) {
      if (success) {
        token.dailyUsage++;
      } else {
        // 如果失败可能是配额问题，标记为已用完
        token.dailyUsage = 100;
        token.isActive = false;
      }
    }
  }



  // ===== Google搜索数据处理辅助方法 =====

  // 基于Google搜索结果估算搜索量（保持兼容性）
  estimateVolumeFromGoogle(totalResults) {
    const count = parseInt(totalResults?.replace(/[^\d]/g, '') || '0');

    if (count > 50000000) return 'very_high';    // 5000万+
    if (count > 10000000) return 'high';         // 1000万+
    if (count > 1000000) return 'medium_high';   // 100万+
    if (count > 100000) return 'medium';         // 10万+
    if (count > 10000) return 'low';             // 1万+
    return 'very_low';                           // 1万以下
  }

  // 增强版流量预测算法
  predictMonthlySearchVolume(googleData) {
    const { totalResults, competitors, titlePatterns, keyword } = googleData;

    // 基础流量计算（对数模型）
    const resultsCount = parseInt(totalResults || '1');
    const baseVolume = Math.log10(resultsCount) * 1000;

    // 竞争对手质量影响因子
    const avgDomainAuthority = this.calculateAverageDomainAuthority(competitors || []);
    const qualityFactor = avgDomainAuthority > 50 ? 0.8 : 1.2;

    // 搜索意图影响因子
    const commercialRatio = this.calculateCommercialRatio(titlePatterns);
    const intentFactor = commercialRatio > 0.3 ? 1.5 : 1.0;

    // 长尾关键词调整
    const wordCount = (keyword || '').split(' ').length;
    const longTailFactor = wordCount > 3 ? 0.7 : 1.0;

    const predictedVolume = Math.round(baseVolume * qualityFactor * intentFactor * longTailFactor);

    return {
      volume: Math.max(predictedVolume, 10), // 最小值限制
      confidence: this.calculateVolumeConfidence(totalResults, (competitors || []).length),
      factors: {
        base: Math.round(baseVolume),
        quality: qualityFactor,
        intent: intentFactor,
        longTail: longTailFactor
      },
      explanation: `基于${totalResults}搜索结果，考虑竞争对手质量(${qualityFactor})、商业意图(${intentFactor})、长尾特性(${longTailFactor})`
    };
  }

  // 计算平均域名权重
  calculateAverageDomainAuthority(competitors) {
    if (!competitors || competitors.length === 0) return 30;

    return competitors.reduce((sum, comp) => {
      const domainType = this.getDomainType(comp.url || comp.link);
      const weight = this.getDomainWeight(domainType);
      return sum + weight * 100;
    }, 0) / competitors.length;
  }

  // 获取域名类型
  getDomainType(url) {
    try {
      const urlObj = new URL(url);
      const pathSegments = urlObj.pathname.split('/').filter(s => s);

      if (pathSegments.length === 0) return 'root';
      if (urlObj.hostname.includes('.')) {
        const subdomains = urlObj.hostname.split('.');
        if (subdomains.length > 2) return 'subdomain';
      }
      return 'subdirectory';
    } catch {
      return 'subdirectory'; // 默认值
    }
  }

  // 获取域名权重
  getDomainWeight(domainType) {
    const weights = {
      root: 1.0,
      subdomain: 0.7,
      subdirectory: 0.9
    };
    return weights[domainType] || 0.8;
  }

  // 计算商业关键词比例
  calculateCommercialRatio(titlePatterns) {
    if (!titlePatterns || !titlePatterns.commonWords) return 0.1;

    const commercialWords = ['buy', 'price', 'cost', 'cheap', 'best', 'review', 'vs', 'deal'];
    const totalWords = titlePatterns.commonWords.length;

    if (totalWords === 0) return 0.1;

    const commercialCount = titlePatterns.commonWords.filter(wordObj => {
      const word = typeof wordObj === 'string' ? wordObj : wordObj.word;
      return commercialWords.some(cw => word.toLowerCase().includes(cw));
    }).length;

    return commercialCount / totalWords;
  }

  // 计算流量预测置信度
  calculateVolumeConfidence(totalResults, competitorCount) {
    const resultsCount = parseInt(totalResults || '0');
    const resultsFactor = Math.min(resultsCount / 1000000, 1);
    const competitorFactor = Math.min(competitorCount / 10, 1);
    return Math.round((resultsFactor * 0.6 + competitorFactor * 0.4) * 100);
  }

  // 提取内容主题
  extractContentThemes(items) {
    const allText = items.map(item =>
      `${item.title} ${item.snippet}`
    ).join(' ').toLowerCase();

    // 简单的关键词提取
    const words = allText.match(/\b\w{4,}\b/g) || [];
    const wordCount = {};

    words.forEach(word => {
      wordCount[word] = (wordCount[word] || 0) + 1;
    });

    return Object.entries(wordCount)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10)
      .map(([word, count]) => ({ word, count }));
  }

  // 分析标题模式
  analyzeTitlePatterns(items) {
    const titles = items.map(item => item.title);

    if (titles.length === 0) {
      return { commonWords: [], averageLength: 0 };
    }

    const allWords = titles.join(' ').toLowerCase().match(/\b\w{3,}\b/g) || [];
    const wordCount = {};

    allWords.forEach(word => {
      wordCount[word] = (wordCount[word] || 0) + 1;
    });

    return {
      averageLength: Math.round(titles.reduce((sum, title) => sum + title.length, 0) / titles.length),
      commonWords: Object.entries(wordCount)
        .filter(([word, count]) => count > 1)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 5)
        .map(([word]) => word)
    };
  }

  // 评估市场成熟度
  assessMarketMaturity(items, totalResults) {
    const resultCount = parseInt(totalResults?.replace(/[^\d]/g, '') || '0');
    const domains = items.map(item => item.displayLink);
    const uniqueDomains = [...new Set(domains)];

    // 基于结果数量和域名多样性评估
    if (resultCount > 10000000 && uniqueDomains.length > 8) {
      return 'very_mature';
    } else if (resultCount > 1000000 && uniqueDomains.length > 6) {
      return 'mature';
    } else if (resultCount > 100000 && uniqueDomains.length > 4) {
      return 'developing';
    } else {
      return 'emerging';
    }
  }
}

// 创建单例实例
const aiProcessor = new AIProcessor();

module.exports = aiProcessor;
