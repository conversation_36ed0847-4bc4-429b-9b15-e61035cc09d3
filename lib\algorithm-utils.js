/**
 * 算法工具函数
 * 提供关键词分析的核心算法
 */

// 辅助函数：使用对数变换和S型曲线计算搜索结果影响
function calculateResultsImpact(resultsCount) {
  // 使用对数变换处理大数值，避免线性假设
  const logResults = Math.log10(Math.max(1, resultsCount))
  // 使用S型曲线而非线性关系
  const normalized = (logResults - 3) / 5 // 标准化到合理范围
  return 1 / (1 + Math.exp(-normalized * 2)) * 10
}

// 辅助函数：增强的竞争密度质量评估
function calculateCompetitionDensity(resultsCount, competitors) {
  if (!competitors || competitors.length === 0) return 1

  // 考虑竞争者质量而非仅数量
  const qualityScore = competitors.reduce((sum, comp) => {
    let score = 1
    // 基于现有数据字段评估质量
    if (comp.contentType === 'product') score *= 1.3
    if (comp.contentType === 'review') score *= 1.2
    if (comp.title && comp.title.length > 50) score *= 1.1
    return sum + score
  }, 0) / competitors.length

  const density = (resultsCount / Math.max(1, competitors.length)) / 1000000
  return Math.min(10, density * qualityScore)
}

// 辅助函数：计算预测不确定性
function calculateUncertainty(googleSearchData) {
  let uncertainty = 0.2 // 基础不确定性20%

  const competitors = googleSearchData.top_competitors || []
  const resultsCount = parseInt(googleSearchData.results_count || 0)

  // 数据质量影响不确定性
  if (competitors.length < 5) uncertainty += 0.1
  if (resultsCount === 0) uncertainty += 0.15
  if (!googleSearchData.title_patterns?.commonWords?.length) uncertainty += 0.1

  return Math.min(0.5, uncertainty) // 最大50%不确定性
}

// 辅助函数：分析竞争对手权威性（基于现有数据）
function analyzeCompetitorAuthority(competitors) {
  if (!competitors.length) return { score: 0, level: 'unknown' }

  const authorityScore = competitors.reduce((sum, comp) => {
    let score = 5 // 基础分数

    // 基于现有字段评估权威性
    if (comp.contentType === 'product') score += 2 // 产品页面通常权威性较高
    if (comp.contentType === 'review') score += 1.5 // 评测页面权威性较高
    if (comp.title && comp.title.length > 60) score += 1 // 详细标题
    if (comp.snippet && comp.snippet.length > 150) score += 1 // 丰富摘要

    // 域名特征分析（基于URL）
    if (comp.url) {
      try {
        const domain = new URL(comp.url).hostname.toLowerCase()
        if (domain.includes('wikipedia') || domain.includes('gov')) score += 3
        if (domain.includes('edu')) score += 2
        if (domain.split('.').length === 2) score += 1 // 顶级域名
        // 知名平台加分
        if (domain.includes('amazon') || domain.includes('youtube') || domain.includes('reddit')) score += 1.5
      } catch (e) {
        // URL解析失败，不影响评分
      }
    }

    return sum + Math.min(10, score)
  }, 0) / competitors.length

  return {
    score: Math.round(authorityScore * 100) / 100,
    level: authorityScore > 8 ? 'very_high' :
           authorityScore > 6 ? 'high' :
           authorityScore > 4 ? 'medium' : 'low',
    competitorCount: competitors.length,
    distribution: calculateAuthorityDistribution(competitors)
  }
}

// 辅助函数：计算权威性分布
function calculateAuthorityDistribution(competitors) {
  const distribution = { high: 0, medium: 0, low: 0 }

  competitors.forEach(comp => {
    let score = 5
    if (comp.contentType === 'product') score += 2
    if (comp.contentType === 'review') score += 1.5
    if (comp.title && comp.title.length > 60) score += 1

    if (score > 7) distribution.high++
    else if (score > 5.5) distribution.medium++
    else distribution.low++
  })

  return distribution
}

// 辅助函数：识别SEO风险因素
function identifyRiskFactors(authorityAnalysis, googleSearchData) {
  const risks = []
  const resultsCount = parseInt(googleSearchData.results_count || 0)

  // 高权威性竞争风险
  if (authorityAnalysis.level === 'very_high') {
    risks.push({
      type: 'high_authority_competition',
      severity: 'high',
      description: '竞争对手权威性极高，需要长期投入和高质量内容策略',
      impact: 'ranking_difficulty'
    })
  } else if (authorityAnalysis.level === 'high') {
    risks.push({
      type: 'moderate_authority_competition',
      severity: 'medium',
      description: '竞争对手权威性较高，需要优质内容和技术优化',
      impact: 'time_investment'
    })
  }

  // 市场饱和度风险
  if (resultsCount > 50000000) {
    risks.push({
      type: 'market_saturation',
      severity: 'high',
      description: '市场极度饱和，竞争异常激烈',
      impact: 'resource_requirement'
    })
  } else if (resultsCount > 10000000) {
    risks.push({
      type: 'high_competition',
      severity: 'medium',
      description: '市场竞争激烈，需要差异化策略',
      impact: 'differentiation_need'
    })
  }

  // 权威性分布风险
  if (authorityAnalysis.distribution.high > authorityAnalysis.competitorCount * 0.6) {
    risks.push({
      type: 'authority_dominance',
      severity: 'high',
      description: '大部分竞争对手都具有高权威性',
      impact: 'entry_barrier'
    })
  }

  return risks
}

// 辅助函数：基于内容类型分布验证意图
function validateIntentByContent(intentType, contentAnalysis) {
  const distribution = contentAnalysis?.distribution || {}
  let multiplier = 1.0
  let confidence = 'medium'

  switch(intentType) {
    case 'transactional':
      if ((distribution.product || 0) > 30) {
        multiplier = 1.2
        confidence = 'high'
      }
      break
    case 'informational':
      if ((distribution.tutorial || 0) > 20) {
        multiplier = 1.15
        confidence = 'high'
      }
      break
    case 'commercial':
    case 'comparative':
      if ((distribution.review || 0) > 25) {
        multiplier = 1.1
        confidence = 'high'
      }
      break
    case 'navigational':
    case 'branded':
      if ((distribution.general || 0) > 40) {
        multiplier = 1.05
        confidence = 'high'
      }
      break
    case 'problem_solving':
      if ((distribution.tutorial || 0) > 15) {
        multiplier = 1.1
        confidence = 'high'
      }
      break
  }

  return { multiplier, confidence }
}

// 辅助函数：计算意图强度
function calculateIntentStrength(scores) {
  const maxScore = Math.max(...Object.values(scores))
  const totalScore = Object.values(scores).reduce((sum, score) => sum + score, 0)

  return {
    dominance: totalScore > 0 ? Math.round((maxScore / totalScore) * 100) / 100 : 0,
    clarity: maxScore > totalScore * 0.6 ? 'clear' : maxScore > totalScore * 0.4 ? 'moderate' : 'unclear'
  }
}

// 辅助函数：检测混合意图
function detectMixedIntent(scores) {
  const sortedScores = Object.entries(scores)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 2)

  const [primary, secondary] = sortedScores

  if (secondary && secondary[1] > primary[1] * 0.7) {
    return {
      isMixed: true,
      primary: primary[0],
      secondary: secondary[0],
      ratio: Math.round((secondary[1] / primary[1]) * 100) / 100
    }
  }

  return { isMixed: false }
}

// 辅助函数：基于竞争强度的CPC调整
function calculateCPCAdjustment(googleSearchData) {
  const competitors = googleSearchData.top_competitors || []
  if (competitors.length === 0) return 1.0

  // 产品页面比例影响
  const productRatio = competitors.filter(c => c.contentType === 'product').length / competitors.length

  // 评测页面比例影响
  const reviewRatio = competitors.filter(c => c.contentType === 'review').length / competitors.length

  // 竞争强度基础调整
  let adjustment = 1.0

  // 产品页面比例高，CPC通常更高
  adjustment += productRatio * 0.5

  // 评测页面比例高，表示商业竞争激烈
  adjustment += reviewRatio * 0.3

  // 搜索结果数量影响
  const resultsCount = parseInt(googleSearchData.results_count || 0)
  if (resultsCount > 10000000) adjustment += 0.2
  else if (resultsCount > 1000000) adjustment += 0.1

  return Math.min(3.0, adjustment) // 最大3倍调整
}

// 辅助函数：分析转化潜力
function analyzeConversionPotential(keyword, googleSearchData) {
  const keywordLower = keyword.toLowerCase()
  let potential = 'medium'
  let score = 5

  // 高转化意图关键词 (扩展版)
  const highConversionWords = [
    'buy', 'purchase', 'price', 'cost', 'cheap', 'best', 'review', 'deal',
    '购买', '价格', '便宜', '最好', '评测', '优惠', '折扣'
  ]

  const mediumConversionWords = [
    'compare', 'vs', 'alternative', 'recommend', 'top',
    '比较', '对比', '推荐', '排行'
  ]

  // 关键词意图评分
  const hasHighConversion = highConversionWords.some(word => keywordLower.includes(word))
  const hasMediumConversion = mediumConversionWords.some(word => keywordLower.includes(word))

  if (hasHighConversion) {
    potential = 'high'
    score = 8
  } else if (hasMediumConversion) {
    potential = 'medium'
    score = 6
  }

  // 基于内容类型分布调整
  const distribution = googleSearchData.content_analysis?.distribution || {}
  const productRatio = (distribution.product || 0) / Math.max(1, Object.values(distribution).reduce((a, b) => a + b, 0))
  const reviewRatio = (distribution.review || 0) / Math.max(1, Object.values(distribution).reduce((a, b) => a + b, 0))

  if (productRatio > 0.4) {
    score += 1
    potential = score > 7 ? 'high' : 'medium'
  }

  if (reviewRatio > 0.3) {
    score += 0.5
  }

  // 竞争对手商业化程度
  const competitors = googleSearchData.top_competitors || []
  const commercialCompetitors = competitors.filter(c =>
    c.contentType === 'product' || c.contentType === 'review'
  ).length

  if (commercialCompetitors > competitors.length * 0.6) {
    score += 0.5
  }

  return {
    potential: score > 7 ? 'high' : score > 5 ? 'medium' : 'low',
    score: Math.min(10, Math.round(score * 10) / 10),
    factors: {
      keywordIntent: hasHighConversion ? 'high' : hasMediumConversion ? 'medium' : 'low',
      contentCommercial: productRatio > 0.4 ? 'high' : 'medium',
      competitorCommercial: commercialCompetitors / Math.max(1, competitors.length)
    }
  }
}

// 辅助函数：生成投资建议
function generateInvestmentAdvice(commercialScore, adjustedCPC) {
  let recommendation = 'medium'
  let reasoning = []
  let budget = 'moderate'

  if (commercialScore > 7 && adjustedCPC < 2) {
    recommendation = 'high'
    reasoning.push('高商业价值且竞争成本合理')
    budget = 'high'
  } else if (commercialScore > 7 && adjustedCPC >= 2) {
    recommendation = 'medium'
    reasoning.push('高商业价值但竞争激烈，需要精准策略')
    budget = 'high'
  } else if (commercialScore > 4 && adjustedCPC < 1.5) {
    recommendation = 'medium'
    reasoning.push('中等商业价值，成本可控')
    budget = 'moderate'
  } else if (commercialScore <= 4) {
    recommendation = 'low'
    reasoning.push('商业价值较低，建议谨慎投入')
    budget = 'low'
  } else {
    recommendation = 'low'
    reasoning.push('竞争成本过高，ROI风险较大')
    budget = 'low'
  }

  return {
    level: recommendation,
    reasoning: reasoning,
    suggestedBudget: budget,
    priority: commercialScore > 6 ? 'high' : commercialScore > 3 ? 'medium' : 'low'
  }
}

// 增强版流量预测算法 (优化版)
export function enhancedVolumePrediction(googleSearchData) {
  const resultsCount = parseInt(googleSearchData.results_count || 0)
  const searchTime = parseFloat(googleSearchData.search_time || 0)
  const competitors = googleSearchData.top_competitors || []
  const competitorCount = competitors.length
  const titlePatterns = googleSearchData.title_patterns || {}

  // 使用新的对数变换和S型曲线计算搜索结果影响
  const resultsImpact = calculateResultsImpact(resultsCount)

  // 使用增强的竞争密度质量评估
  const competitionDensity = calculateCompetitionDensity(resultsCount, competitors)

  // 保留原有的标题优化逻辑
  const titleOptimization = (titlePatterns.hasNumberPattern || 0) + (titlePatterns.hasDatePattern || 0)
  const optimizationMultiplier = titleOptimization > 5 ? 1.3 : titleOptimization > 2 ? 1.1 : 1.0

  // 综合计算基础流量预测
  const baseVolume = Math.round(resultsImpact * 1000 * (1 + competitionDensity * 0.1) * optimizationMultiplier)

  // 计算不确定性和预测区间
  const uncertainty = calculateUncertainty(googleSearchData)
  const lowerBound = Math.round(baseVolume * (1 - uncertainty))
  const upperBound = Math.round(baseVolume * (1 + uncertainty))

  // 保留原有的置信度计算逻辑
  const confidence = Math.min(95, Math.max(20,
    (competitorCount > 5 ? 80 : 60) +
    (titlePatterns.averageLength > 30 ? 10 : 0) +
    (searchTime < 0.5 ? 5 : 0)
  ))

  // 生成流量等级
  let level = 'low'
  if (baseVolume > 10000) level = 'high'
  else if (baseVolume > 5000) level = 'medium'
  else if (baseVolume > 1000) level = 'low'
  else level = 'very_low'

  return {
    volume: baseVolume,
    // 新增：预测区间
    lowerBound: lowerBound,
    upperBound: upperBound,
    // 新增：预测置信度（基于不确定性）
    predictionConfidence: Math.round((1 - uncertainty) * 100),
    level: googleSearchData.estimated_volume || level,
    confidence: confidence,
    // 新增：不确定性指标
    uncertainty: Math.round(uncertainty * 100),
    factors: {
      resultsCount,
      resultsImpact: Math.round(resultsImpact * 100) / 100,
      competitionDensity: Math.round(competitionDensity * 100) / 100,
      titleOptimization,
      searchTime,
      // 新增：质量评估因子
      competitorQuality: competitors.length > 0 ?
        Math.round((competitors.filter(c => c.contentType === 'product' || c.contentType === 'review').length / competitors.length) * 100) : 0
    }
  }
}

// 增强版SEO难度分析 (优化版)
export function enhancedSEODifficultyAnalysis(googleSearchData) {
  const competitors = googleSearchData.top_competitors || []
  const resultsCount = parseInt(googleSearchData.results_count || 0)
  const competitorSummary = googleSearchData.competitor_summary || {}

  // 保留原有的基础难度评分逻辑
  let difficultyScore = 0

  // 根据结果数量评分
  if (resultsCount > 50000000) difficultyScore += 40
  else if (resultsCount > 10000000) difficultyScore += 30
  else if (resultsCount > 1000000) difficultyScore += 20
  else if (resultsCount > 100000) difficultyScore += 10
  else difficultyScore += 5

  // 根域名比例影响
  const rootDomainRatio = competitorSummary.rootDomainCount / Math.max(1, competitors.length)
  difficultyScore += rootDomainRatio * 30

  // 标题优化程度影响
  const dateOptimizedRatio = competitorSummary.dateOptimizedCount / Math.max(1, competitors.length)
  difficultyScore += dateOptimizedRatio * 20

  // 平均标题长度影响（更长的标题通常表示更激烈的竞争）
  const avgTitleLength = competitorSummary.averageTitleLength || 0
  if (avgTitleLength > 60) difficultyScore += 10
  else if (avgTitleLength > 40) difficultyScore += 5

  // 新增：竞争对手权威性分析
  const authorityAnalysis = analyzeCompetitorAuthority(competitors)

  // 根据权威性调整难度评分
  if (authorityAnalysis.level === 'very_high') difficultyScore += 15
  else if (authorityAnalysis.level === 'high') difficultyScore += 10
  else if (authorityAnalysis.level === 'medium') difficultyScore += 5

  // 确保分数在0-100范围内
  difficultyScore = Math.min(100, Math.max(0, Math.round(difficultyScore)))

  // 难度等级
  let level = 'easy'
  if (difficultyScore > 70) level = 'hard'
  else if (difficultyScore > 40) level = 'medium'

  // 新增：风险因素识别
  const riskFactors = identifyRiskFactors(authorityAnalysis, googleSearchData)

  // 生成增强的SEO建议
  const recommendations = generateSEORecommendations(difficultyScore, level)

  // 基于权威性和风险因素生成额外建议
  const enhancedRecommendations = [...recommendations]
  if (authorityAnalysis.level === 'very_high') {
    enhancedRecommendations.push('重点投入内容权威性建设')
    enhancedRecommendations.push('考虑长尾关键词策略降低竞争')
  }
  if (riskFactors.length > 2) {
    enhancedRecommendations.push('建议分阶段实施，降低投入风险')
  }

  return {
    score: difficultyScore,
    level: level,
    // 新增：权威性分析结果
    authorityAnalysis: authorityAnalysis,
    // 新增：风险因素
    riskFactors: riskFactors,
    // 新增：风险等级
    riskLevel: riskFactors.length > 2 ? 'high' : riskFactors.length > 0 ? 'medium' : 'low',
    factors: {
      resultsCount,
      rootDomainRatio: Math.round(rootDomainRatio * 100),
      dateOptimizedRatio: Math.round(dateOptimizedRatio * 100),
      avgTitleLength,
      // 新增：权威性相关因子
      authorityScore: authorityAnalysis.score,
      authorityLevel: authorityAnalysis.level,
      competitorCount: competitors.length
    },
    recommendations: enhancedRecommendations,
    // 新增：时间和资源估算
    estimates: {
      timeToRank: estimateRankingTime(difficultyScore, authorityAnalysis),
      resourceRequirement: estimateResourceRequirement(difficultyScore, riskFactors)
    }
  }
}

// 辅助函数：估算排名时间
function estimateRankingTime(difficultyScore, authorityAnalysis) {
  let months = 3 // 基础时间

  if (difficultyScore > 80) months = 12
  else if (difficultyScore > 60) months = 8
  else if (difficultyScore > 40) months = 6
  else if (difficultyScore > 20) months = 4

  // 权威性影响
  if (authorityAnalysis.level === 'very_high') months += 6
  else if (authorityAnalysis.level === 'high') months += 3

  return {
    estimate: months,
    range: `${Math.max(1, months - 2)}-${months + 3}个月`,
    confidence: difficultyScore < 50 ? 'high' : difficultyScore < 80 ? 'medium' : 'low'
  }
}

// 辅助函数：估算资源需求
function estimateResourceRequirement(difficultyScore, riskFactors) {
  let level = 'low'
  let description = '基础SEO优化即可'

  if (difficultyScore > 80) {
    level = 'very_high'
    description = '需要专业团队和大量资源投入'
  } else if (difficultyScore > 60) {
    level = 'high'
    description = '需要专业SEO策略和持续投入'
  } else if (difficultyScore > 40) {
    level = 'medium'
    description = '需要系统性SEO优化'
  }

  // 风险因素影响
  const highRiskCount = riskFactors.filter(r => r.severity === 'high').length
  if (highRiskCount > 1) {
    level = level === 'low' ? 'medium' : level === 'medium' ? 'high' : 'very_high'
  }

  return {
    level: level,
    description: description,
    riskAdjusted: highRiskCount > 0
  }
}

// 搜索意图分析 (优化版 - 8大类型)
export function analyzeSearchIntent(titlePatterns, googleSearchData = {}) {
  const commonWords = titlePatterns.commonWords || []
  const averageLength = titlePatterns.averageLength || 0
  const hasNumberPattern = titlePatterns.hasNumberPattern || 0
  const hasDatePattern = titlePatterns.hasDatePattern || 0

  // 扩展的8大意图关键词映射
  const intentKeywords = {
    // 1. 信息型 (Informational Intent)
    informational: [
      '如何', '什么', '为什么', '教程', '指南', '方法', '步骤',
      'how', 'what', 'why', 'tutorial', 'guide', 'tips', 'examples', 'list'
    ],

    // 2. 导航型 (Navigational Intent)
    navigational: [
      '官网', '登录', '下载', '首页', '平台', '账户',
      'official', 'login', 'homepage', 'download', 'platform', 'dashboard', 'account'
    ],

    // 3. 交易型/商业型 (Transactional/Commercial Intent)
    transactional: [
      '购买', '价格', '优惠', '折扣', '订购', '购物', '订阅',
      'buy', 'purchase', 'price', 'discount', 'deal', 'coupon', 'order', 'shop', 'subscribe'
    ],

    // 4. 本地型 (Local Intent)
    local: [
      '附近', '周边', '本地', '地址', '位置', '营业时间',
      'near me', 'nearby', 'open now', 'location', 'map', 'directions', 'in'
    ],

    // 5. 品牌型 (Branded Intent)
    branded: [
      '品牌', '官方', '应用', '功能', '特色',
      'app', 'features', 'official', 'brand'
    ],

    // 6. 比较型 (Comparative Intent)
    comparative: [
      '比较', '对比', '哪个好', '区别', '替代',
      'vs', 'comparison', 'compare', 'which is better', 'alternative', 'difference'
    ],

    // 7. 问题解决型 (Problem-Solving Intent)
    problem_solving: [
      '不工作', '修复', '错误', '问题', '解决', '故障',
      'not working', 'fix', 'error', 'issue', 'solution', 'problem', 'repair', 'cannot'
    ],

    // 8. 混合型 (Hybrid Intent)
    hybrid: [
      '注册', '免费试用', '获取', '申请',
      'sign up', 'free trial', 'get', 'pricing'
    ]
  }

  // 计算各意图得分
  const intentScores = {}
  Object.keys(intentKeywords).forEach(intent => {
    let score = 0
    const keywords = intentKeywords[intent]

    commonWords.forEach(({ word, count }) => {
      const wordLower = word.toLowerCase()
      if (keywords.some(keyword => wordLower.includes(keyword.toLowerCase()))) {
        score += count * 10
      }
    })

    intentScores[intent] = score
  })

  // 根据其他特征调整得分
  if (hasNumberPattern > 3) {
    intentScores.comparative += 20
    intentScores.informational += 10
    intentScores.transactional += 15
  }

  if (hasDatePattern > 2) {
    intentScores.informational += 15
    intentScores.navigational += 5
  }

  if (averageLength > 50) {
    intentScores.informational += 10
    intentScores.problem_solving += 8
  }

  // 确定主要意图
  const maxIntent = Object.entries(intentScores).reduce((a, b) =>
    intentScores[a[0]] > intentScores[b[0]] ? a : b
  )

  const totalScore = Object.values(intentScores).reduce((sum, score) => sum + score, 0)
  const baseConfidence = totalScore > 0 ? Math.round((maxIntent[1] / totalScore) * 100) : 50

  // 新增：基于内容类型分布的意图验证
  const contentValidation = validateIntentByContent(maxIntent[0], googleSearchData.content_analysis)

  // 调整置信度
  const adjustedConfidence = Math.min(95, Math.max(20,
    Math.round(baseConfidence * contentValidation.multiplier)
  ))

  // 新增：意图强度评估
  const intentStrength = calculateIntentStrength(intentScores)

  // 新增：混合意图检测
  const mixedIntent = detectMixedIntent(intentScores)

  return {
    type: maxIntent[0],
    confidence: adjustedConfidence,
    scores: intentScores,
    // 新增字段
    validation: contentValidation,
    intentStrength: intentStrength,
    mixedIntent: mixedIntent,
    // 新增：意图分类详情
    classification: {
      primary: maxIntent[0],
      primaryScore: maxIntent[1],
      totalScore: totalScore,
      distribution: Object.fromEntries(
        Object.entries(intentScores).map(([key, value]) => [
          key,
          totalScore > 0 ? Math.round((value / totalScore) * 100) : 0
        ])
      )
    },
    recommendations: generateIntentRecommendations(maxIntent[0], adjustedConfidence)
  }
}

// 商业价值分析 (优化版)
export function analyzeCommercialValue(keyword, googleSearchData) {
  const competitors = googleSearchData.top_competitors || []
  const contentAnalysis = googleSearchData.content_analysis || {}
  const resultsCount = parseInt(googleSearchData.results_count || 0)

  let commercialScore = 0

  // 保留原有的关键词商业价值分析
  const commercialKeywords = ['购买', '价格', '优惠', '产品', '服务', 'buy', 'price', 'product', 'service']
  const keywordLower = keyword.toLowerCase()
  commercialKeywords.forEach(word => {
    if (keywordLower.includes(word)) {
      commercialScore += 2
    }
  })

  // 保留原有的竞争对手内容类型分析
  const productCount = competitors.filter(c => c.contentType === 'product').length
  const reviewCount = competitors.filter(c => c.contentType === 'review').length

  if (competitors.length > 0) {
    commercialScore += (productCount / competitors.length) * 3
    commercialScore += (reviewCount / competitors.length) * 2
  }

  // 保留原有的内容分布影响
  const distribution = contentAnalysis.distribution || {}
  if (distribution.product) commercialScore += (distribution.product / 10) * 2
  if (distribution.review) commercialScore += (distribution.review / 10) * 1.5

  // 保留原有的市场成熟度影响
  const maturity = googleSearchData.market_maturity
  if (maturity === 'mature') commercialScore += 1
  else if (maturity === 'developing') commercialScore += 0.5

  // 确保分数在0-10范围内
  commercialScore = Math.min(10, Math.max(0, commercialScore))

  // 新增：基于竞争强度的CPC调整
  const competitionAdjustment = calculateCPCAdjustment(googleSearchData)
  const baseCPC = commercialScore * 0.5 + (resultsCount > 1000000 ? 1 : 0.5)
  const adjustedCPC = baseCPC * competitionAdjustment

  // 新增：转化潜力细化分析
  const conversionAnalysis = analyzeConversionPotential(keyword, googleSearchData)

  // 新增：投资建议生成
  const investmentRecommendation = generateInvestmentAdvice(commercialScore, adjustedCPC)

  return {
    score: Math.round(commercialScore * 10) / 10,
    level: commercialScore > 7 ? 'high' : commercialScore > 4 ? 'medium' : 'low',
    metrics: {
      // 优化：更精确的CPC估算
      estimatedCPC: Math.round(adjustedCPC * 100) / 100,
      baseCPC: Math.round(baseCPC * 100) / 100,
      competitionMultiplier: Math.round(competitionAdjustment * 100) / 100,
      // 新增：详细的转化分析
      conversionAnalysis: conversionAnalysis,
      // 保留原有字段
      conversionPotential: Math.round(commercialScore * 10),
      productContentRatio: Math.round((productCount / Math.max(1, competitors.length)) * 100),
      reviewContentRatio: Math.round((reviewCount / Math.max(1, competitors.length)) * 100)
    },
    // 新增：投资建议
    investmentRecommendation: investmentRecommendation,
    // 新增：ROI预估
    roiEstimate: {
      potential: conversionAnalysis.score > 7 ? 'high' : conversionAnalysis.score > 4 ? 'medium' : 'low',
      timeframe: adjustedCPC > 2 ? 'long_term' : 'medium_term',
      confidence: commercialScore > 6 && adjustedCPC < 2 ? 'high' : 'medium'
    },
    recommendations: generateCommercialRecommendations(commercialScore)
  }
}

// 内容空缺分析
export function analyzeContentGap(googleSearchData) {
  const competitors = googleSearchData.top_competitors || []
  const contentAnalysis = googleSearchData.content_analysis || {}
  const titlePatterns = googleSearchData.title_patterns || {}

  // 分析内容类型分布
  const distribution = contentAnalysis.distribution || {}
  const totalContent = Object.values(distribution).reduce((sum, count) => sum + count, 0)

  // 识别内容空缺
  const contentTypes = ['tutorial', 'review', 'news', 'product', 'blog', 'general']
  const gaps = []
  let gapCount = 0

  contentTypes.forEach(type => {
    const count = distribution[type] || 0
    const ratio = totalContent > 0 ? count / totalContent : 0
    
    if (ratio < 0.1) { // 如果某种内容类型占比小于10%
      gaps.push({
        type,
        opportunity: 'high',
        reason: `${type}类型内容稀缺，占比仅${Math.round(ratio * 100)}%`
      })
      gapCount++
    } else if (ratio < 0.2) {
      gaps.push({
        type,
        opportunity: 'medium',
        reason: `${type}类型内容较少，有优化空间`
      })
      gapCount++
    }
  })

  // 分析标题模式空缺
  const commonWords = titlePatterns.commonWords || []
  const wordDiversity = commonWords.length
  
  if (wordDiversity < 5) {
    gaps.push({
      type: 'keyword_diversity',
      opportunity: 'high',
      reason: '标题关键词多样性不足，存在长尾机会'
    })
    gapCount++
  }

  // 评估整体机会等级
  let opportunities = 'low'
  if (gapCount > 3) opportunities = 'high'
  else if (gapCount > 1) opportunities = 'medium'

  return {
    gapCount,
    opportunities,
    gaps,
    contentSuggestions: generateContentSuggestions(gaps),
    diversityScore: Math.min(10, wordDiversity)
  }
}

// 综合评分算法
export function enhancedComprehensiveScoring(googleSearchData, competitionAnalysis, similarityAnalysis) {
  const volumePrediction = googleSearchData.enhanced_volume_prediction || {}
  const seoDifficulty = googleSearchData.enhanced_seo_analysis || {}
  const commercialValue = googleSearchData.commercial_value_analysis || {}
  const contentGap = googleSearchData.content_gap_analysis || {}

  // 各维度权重
  const weights = {
    volume: 0.25,      // 流量潜力
    difficulty: 0.25,  // SEO难度（反向）
    commercial: 0.20,  // 商业价值
    opportunity: 0.15, // 内容机会
    competition: 0.15  // 竞争强度（反向）
  }

  // 计算各维度得分（0-10分）
  const scores = {
    volume: Math.min(10, (volumePrediction.volume || 0) / 1000),
    difficulty: Math.max(0, 10 - (seoDifficulty.score || 50) / 10), // 难度越低分数越高
    commercial: commercialValue.score || 0,
    opportunity: Math.min(10, (contentGap.gapCount || 0) * 2),
    competition: Math.max(0, 10 - (competitionAnalysis.intensity_score || 5)) // 竞争越低分数越高
  }

  // 计算加权总分
  const totalScore = Object.keys(weights).reduce((sum, key) => {
    return sum + (scores[key] * weights[key])
  }, 0)

  // 生成推荐等级
  let recommendation = 'low'
  if (totalScore > 7) recommendation = 'high'
  else if (totalScore > 5) recommendation = 'medium'

  return {
    score: Math.round(totalScore * 10) / 10,
    recommendation,
    breakdown: scores,
    weights,
    factors: {
      volumeScore: scores.volume,
      difficultyScore: scores.difficulty,
      commercialScore: scores.commercial,
      opportunityScore: scores.opportunity,
      competitionScore: scores.competition
    }
  }
}

// 置信度计算
export function enhancedConfidenceCalculation(googleSearchData, competitionAnalysis, similarityAnalysis) {
  const competitors = googleSearchData.top_competitors || []
  const titlePatterns = googleSearchData.title_patterns || {}
  const resultsCount = parseInt(googleSearchData.results_count || 0)

  let confidence = 50 // 基础置信度

  // 数据完整性影响
  if (competitors.length >= 10) confidence += 20
  else if (competitors.length >= 5) confidence += 10

  if (resultsCount > 0) confidence += 10

  if (titlePatterns.commonWords && titlePatterns.commonWords.length > 0) confidence += 10

  // 数据一致性影响
  if (titlePatterns.averageLength > 0) confidence += 5
  if (googleSearchData.search_time > 0) confidence += 5

  // 算法覆盖度影响
  const algorithmCount = [
    googleSearchData.enhanced_volume_prediction,
    googleSearchData.enhanced_seo_analysis,
    googleSearchData.commercial_value_analysis,
    googleSearchData.content_gap_analysis
  ].filter(Boolean).length

  confidence += algorithmCount * 5

  // 确保置信度在20-95范围内
  confidence = Math.min(95, Math.max(20, confidence))

  return {
    overall: confidence,
    dataQuality: Math.min(100, (competitors.length / 10) * 100),
    algorithmCoverage: (algorithmCount / 4) * 100,
    factors: {
      competitorCount: competitors.length,
      hasResults: resultsCount > 0,
      hasTitlePatterns: titlePatterns.commonWords?.length > 0,
      algorithmCount
    }
  }
}

// 报告完整性分析
export function analyzeReportCompleteness(analysisResults) {
  const requiredSections = [
    'volumePrediction',
    'seoDifficulty', 
    'searchIntent',
    'commercialValue',
    'contentGap',
    'comprehensiveScore',
    'confidenceAnalysis'
  ]

  const completedSections = requiredSections.filter(section => 
    analysisResults[section] && Object.keys(analysisResults[section]).length > 0
  )

  const completeness = (completedSections.length / requiredSections.length) * 100

  return {
    completeness: Math.round(completeness),
    completedSections: completedSections.length,
    totalSections: requiredSections.length,
    missingSections: requiredSections.filter(section => !completedSections.includes(section)),
    quality: completeness > 80 ? 'high' : completeness > 60 ? 'medium' : 'low'
  }
}

// 辅助函数：生成SEO建议
function generateSEORecommendations(score, level) {
  const recommendations = []
  
  if (level === 'hard') {
    recommendations.push('考虑长尾关键词策略')
    recommendations.push('重点关注内容质量和用户体验')
    recommendations.push('建立权威性和专业性')
  } else if (level === 'medium') {
    recommendations.push('优化页面SEO技术要素')
    recommendations.push('创建高质量原创内容')
    recommendations.push('建设相关性强的外链')
  } else {
    recommendations.push('快速占领搜索结果')
    recommendations.push('扩展相关关键词')
    recommendations.push('建立内容集群')
  }
  
  return recommendations
}

// 辅助函数：生成意图建议 (支持8种意图类型)
function generateIntentRecommendations(intent, confidence) {
  const recommendations = []

  switch(intent) {
    case 'informational':
      recommendations.push('创建详细的教程和指南内容')
      recommendations.push('提供权威的信息和数据支持')
      recommendations.push('优化内容结构和可读性')
      break
    case 'navigational':
      recommendations.push('确保品牌页面易于找到')
      recommendations.push('优化官方页面的搜索可见性')
      recommendations.push('提升页面加载速度和用户体验')
      break
    case 'transactional':
      recommendations.push('优化产品页面和购买流程')
      recommendations.push('添加价格比较和评价信息')
      recommendations.push('建立信任标识和安全保障')
      break
    case 'local':
      recommendations.push('优化本地SEO和地图标注')
      recommendations.push('添加详细的位置和联系信息')
      recommendations.push('收集和展示本地客户评价')
      break
    case 'branded':
      recommendations.push('强化品牌识别度和一致性')
      recommendations.push('创建品牌相关的优质内容')
      recommendations.push('建立品牌权威性和信任度')
      break
    case 'comparative':
      recommendations.push('创建详细的产品对比内容')
      recommendations.push('提供客观的优缺点分析')
      recommendations.push('添加用户评价和专家意见')
      break
    case 'problem_solving':
      recommendations.push('创建详细的问题解决指南')
      recommendations.push('提供多种解决方案选择')
      recommendations.push('建立FAQ和技术支持页面')
      break
    case 'hybrid':
      recommendations.push('优化转化路径和用户体验')
      recommendations.push('平衡信息展示和行动引导')
      recommendations.push('提供多种参与方式选择')
      break
    default:
      recommendations.push('分析用户搜索意图，优化内容策略')
      recommendations.push('提升页面相关性和用户体验')
      break
  }

  // 基于置信度添加额外建议
  if (confidence < 60) {
    recommendations.push('建议进一步分析用户意图，优化关键词策略')
  }

  return recommendations
}

// 辅助函数：生成商业建议
function generateCommercialRecommendations(score) {
  const recommendations = []
  
  if (score > 7) {
    recommendations.push('重点投入，商业价值高')
    recommendations.push('考虑付费推广策略')
    recommendations.push('建立完整的转化漏斗')
  } else if (score > 4) {
    recommendations.push('适度投入，有一定商业潜力')
    recommendations.push('关注转化率优化')
  } else {
    recommendations.push('谨慎投入，商业价值较低')
    recommendations.push('可作为流量入口关键词')
  }
  
  return recommendations
}

// 辅助函数：生成内容建议
function generateContentSuggestions(gaps) {
  const suggestions = []
  
  gaps.forEach(gap => {
    switch(gap.type) {
      case 'tutorial':
        suggestions.push('创建详细的教程和操作指南')
        break
      case 'review':
        suggestions.push('制作产品评测和比较内容')
        break
      case 'news':
        suggestions.push('关注行业动态和新闻更新')
        break
      case 'product':
        suggestions.push('优化产品介绍和展示页面')
        break
      case 'blog':
        suggestions.push('发布相关的博客文章和观点')
        break
      case 'keyword_diversity':
        suggestions.push('扩展长尾关键词和相关词汇')
        break
    }
  })
  
  return suggestions
}

// 智能竞争优势分析函数
export function generateCompetitiveAdvantageAnalysis(googleSearchData, searchIntentData) {
  const competitors = googleSearchData.top_competitors || []
  const competitorSummary = googleSearchData.competitor_summary || {}
  const contentAnalysis = googleSearchData.content_analysis || {}

  if (competitors.length === 0) {
    return {
      rootDomainAnalysis: '暂无竞争对手数据，市场机会较大',
      websiteTypeRecommendation: '内容站',
      strategicAdvantages: ['先发优势', '内容空白市场'],
      actionableSteps: ['创建基础内容', '建立品牌认知']
    }
  }

  // 根域名竞争分析
  const rootDomainCount = competitorSummary.rootDomainCount || competitors.filter(c => c.isRootDomain).length
  const totalCompetitors = competitors.length
  const rootDomainRatio = rootDomainCount / totalCompetitors

  // 内容类型分布分析
  const distribution = contentAnalysis.distribution || {}
  const contentTypes = {
    product: distribution.product || 0,
    review: distribution.review || 0,
    tutorial: distribution.tutorial || 0,
    news: distribution.news || 0,
    blog: distribution.blog || 0,
    general: distribution.general || 0
  }

  // 搜索意图分析
  const primaryIntent = searchIntentData.type || 'informational'
  const intentConfidence = searchIntentData.confidence || 50

  // 生成根域名分析
  let rootDomainAnalysis = ''
  if (rootDomainRatio > 0.7) {
    rootDomainAnalysis = `前10名竞争对手中有${rootDomainCount}个根域名（占比${Math.round(rootDomainRatio * 100)}%），竞争激烈。建议考虑长尾关键词策略或寻找细分市场机会。`
  } else if (rootDomainRatio > 0.4) {
    rootDomainAnalysis = `根域名竞争对手${rootDomainCount}个（占比${Math.round(rootDomainRatio * 100)}%），竞争适中。可通过高质量内容和专业化定位获得排名机会。`
  } else {
    rootDomainAnalysis = `根域名竞争对手仅${rootDomainCount}个（占比${Math.round(rootDomainRatio * 100)}%），多为子页面内容。新域名有较好的排名机会，建议重点投入。`
  }

  // 基于搜索意图推荐网站类型
  const websiteTypeMapping = {
    'informational': {
      primary: '内容站/博客站',
      secondary: '工具类网站',
      reason: '信息型搜索需求，适合深度内容创作'
    },
    'navigational': {
      primary: '导航站/品牌占位站',
      secondary: '垂直产品站',
      reason: '导航型搜索，适合品牌建设和用户引导'
    },
    'transactional': {
      primary: '垂直产品站/产品页站',
      secondary: '工具类网站',
      reason: '交易型搜索，适合产品销售和转化'
    },
    'local': {
      primary: '本地服务站/企业官网',
      secondary: '内容站',
      reason: '本地型搜索，适合地理位置相关服务'
    },
    'branded': {
      primary: '导航站/品牌占位站',
      secondary: '内容站',
      reason: '品牌型搜索，适合品牌防护和用户教育'
    },
    'comparative': {
      primary: '对比站/排行榜站',
      secondary: '内容站',
      reason: '比较型搜索，适合评测和推荐内容'
    },
    'problem_solving': {
      primary: '内容站/博客站',
      secondary: '工具类网站',
      reason: '问题解决型搜索，适合教程和解决方案'
    },
    'hybrid': {
      primary: '工具类网站',
      secondary: '垂直产品站',
      reason: '混合型搜索，适合功能性产品'
    }
  }

  const recommendedType = websiteTypeMapping[primaryIntent] || websiteTypeMapping['informational']

  // 分析内容空缺和机会
  const contentGaps = []
  const strategicAdvantages = []

  // 检查内容类型空缺
  if (contentTypes.tutorial < 2) {
    contentGaps.push('教程类内容稀缺')
    strategicAdvantages.push('创建系统性教程内容')
  }
  if (contentTypes.review < 2) {
    contentGaps.push('评测类内容不足')
    strategicAdvantages.push('提供专业评测和对比')
  }
  if (contentTypes.product < 2 && (primaryIntent === 'transactional' || primaryIntent === 'hybrid')) {
    contentGaps.push('产品页面缺失')
    strategicAdvantages.push('建立产品展示页面')
  }

  // 基于根域名比例的策略优势
  if (rootDomainRatio < 0.5) {
    strategicAdvantages.push('新域名排名机会较大')
    strategicAdvantages.push('可通过专业化内容快速获得权威性')
  } else {
    strategicAdvantages.push('通过长尾关键词避开直接竞争')
    strategicAdvantages.push('专注细分领域建立差异化优势')
  }

  // 生成可执行的行动步骤
  const actionableSteps = []

  // 基于搜索意图的行动建议
  switch (primaryIntent) {
    case 'informational':
      actionableSteps.push('创建深度教程和指南内容')
      actionableSteps.push('建立知识库和FAQ系统')
      break
    case 'transactional':
      actionableSteps.push('优化产品页面和购买流程')
      actionableSteps.push('添加用户评价和信任标识')
      break
    case 'comparative':
      actionableSteps.push('制作详细的产品对比表格')
      actionableSteps.push('提供客观的优缺点分析')
      break
    case 'problem_solving':
      actionableSteps.push('创建问题解决方案库')
      actionableSteps.push('提供多种解决方案选择')
      break
    default:
      actionableSteps.push('分析用户需求，创建针对性内容')
      actionableSteps.push('建立用户互动和反馈机制')
  }

  // 基于内容空缺的行动建议
  if (contentGaps.length > 0) {
    actionableSteps.push(`填补${contentGaps.join('、')}的市场空白`)
  }

  return {
    rootDomainAnalysis,
    websiteTypeRecommendation: {
      primary: recommendedType.primary,
      secondary: recommendedType.secondary,
      reason: recommendedType.reason,
      confidence: intentConfidence > 70 ? 'high' : intentConfidence > 50 ? 'medium' : 'low'
    },
    contentGaps,
    strategicAdvantages,
    actionableSteps: actionableSteps.slice(0, 4), // 限制为4个关键步骤
    competitionLevel: rootDomainRatio > 0.7 ? 'high' : rootDomainRatio > 0.4 ? 'medium' : 'low'
  }
}
