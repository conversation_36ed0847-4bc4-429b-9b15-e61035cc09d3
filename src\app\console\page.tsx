'use client'

import { useState, useEffect, useCallback, useRef } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card'
import { Button } from '../../components/ui/button'
import { Badge } from '../../components/ui/badge'
import { Progress } from '../../components/ui/progress'
import { Skeleton } from '../../components/ui/skeleton'
import {
  Upload,
  Database,
  BarChart3,
  Plus,
  Target,
  Activity,
  Zap,
  Clock,
  CheckCircle,
  AlertCircle,
  RefreshCw,
  Settings
} from 'lucide-react'
import ConsoleLayout from '../../components/console-layout'
import Link from 'next/link'

interface Statistics {
  total: number
  analyzed: number
  unanalyzed: number
  category_count: number
}

interface RecentActivity {
  id: string
  type: 'import' | 'analyze' | 'export'
  description: string
  timestamp: string
  count?: number
  filename?: string
  import_date?: string
}

export default function ConsolePage() {
  const { data: session, status } = useSession()
  const router = useRouter()

  // 使用ref来防止组件卸载后的状态更新
  const isMountedRef = useRef(true)
  const fetchTimeoutRef = useRef<NodeJS.Timeout>()

  const [statistics, setStatistics] = useState<Statistics>({
    total: 0,
    analyzed: 0,
    unanalyzed: 0,
    category_count: 0
  })
  const [loading, setLoading] = useState(true)
  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([])

  // 清理函数
  useEffect(() => {
    return () => {
      isMountedRef.current = false
      if (fetchTimeoutRef.current) {
        clearTimeout(fetchTimeoutRef.current)
      }
    }
  }, [])

  // 设置页面标题
  useEffect(() => {
    document.title = '控制台 - CatchIdeas'
  }, [])

  // 权限检查 - 优化依赖，减少不必要的重新渲染
  useEffect(() => {
    if (status === 'loading') return // 还在加载中

    if (!session) {
      router.push('/auth/signin')
      return
    }
  }, [session, status, router])

  // 防抖获取数据函数
  const debouncedFetchData = useCallback(() => {
    // 清除之前的定时器
    if (fetchTimeoutRef.current) {
      clearTimeout(fetchTimeoutRef.current)
    }

    // 设置新的定时器，300ms后执行
    fetchTimeoutRef.current = setTimeout(() => {
      if (isMountedRef.current) {
        fetchDashboardData()
      }
    }, 300)
  }, [])

  // 获取仪表板数据 - 只在初始化时执行一次
  useEffect(() => {
    if (session && status === 'authenticated') {
      debouncedFetchData()
    }
  }, [session, status, debouncedFetchData])

  const fetchDashboardData = useCallback(async () => {
    if (!isMountedRef.current) return

    setLoading(true)
    try {
      // 获取完整的仪表板数据 (允许正常缓存)
      const response = await fetch('/api/dashboard/stats')
      const data = await response.json()

      if (!isMountedRef.current) return // 检查组件是否仍然挂载

      if (data.success) {
        // 使用dashboard API返回的所有数据
        setStatistics(data.data.statistics)
        setRecentActivity(data.data.recentActivity || [])
      } else {
        // 如果dashboard API失败，尝试获取基础统计数据 (允许正常缓存)
        const fallbackResponse = await fetch('/api/keywords/list?page=1&per_page=1')
        const fallbackData = await fallbackResponse.json()

        if (!isMountedRef.current) return // 再次检查组件是否仍然挂载

        if (fallbackData.success && fallbackData.data.statistics) {
          setStatistics(fallbackData.data.statistics)
        } else {
          setStatistics({
            total: 0,
            analyzed: 0,
            unanalyzed: 0,
            category_count: 0
          })
        }

        setRecentActivity([])
      }

    } catch (error) {
      console.error('获取仪表板数据失败:', error)
      if (!isMountedRef.current) return

      // 错误时使用默认数据
      setStatistics({
        total: 0,
        analyzed: 0,
        unanalyzed: 0,
        category_count: 0
      })
      setRecentActivity([])
    } finally {
      if (isMountedRef.current) {
        setLoading(false)
      }
    }
  }, [])

  // 手动刷新数据
  const handleRefresh = useCallback(() => {
    fetchDashboardData()
  }, [fetchDashboardData])

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'import': return Upload
      case 'analyze': return Target
      case 'export': return Database
      default: return Activity
    }
  }

  // 如果还在检查session状态，显示加载
  if (status === 'loading') {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-center">
          <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent mx-auto mb-2"></div>
          <p className="text-muted-foreground">检查登录状态...</p>
        </div>
      </div>
    )
  }

  // 如果未登录，不渲染内容（会被重定向）
  if (!session) {
    return null
  }

  return (
    <ConsoleLayout
      title="智能仪表板"
    >
      <div className="space-y-8">
        {/* 欢迎区域 */}
        <div className="console-gradient-section p-8">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-bold text-foreground mb-2">欢迎回到 CatchIdeas</h2>
              <p className="text-muted-foreground">您的创意关键词智能分析平台</p>
            </div>
            <div className="flex space-x-3">
              <Button
                onClick={handleRefresh}
                disabled={loading}
                variant="ghost"
                size="sm"
                className="btn-ghost-enhanced"
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                刷新数据
              </Button>
              <Button asChild className="btn-primary-enhanced">
                <Link href="/console/upload">
                  <Upload className="h-4 w-4 mr-2" />
                  上传关键词
                </Link>
              </Button>
              <Button asChild variant="outline" className="btn-outline-enhanced">
                <Link href="/console/analyze">
                  <Zap className="h-4 w-4 mr-2" />
                  AI分析
                </Link>
              </Button>
              <Button asChild variant="outline" className="btn-outline-enhanced">
                <Link href="/console/api-test">
                  <Settings className="h-4 w-4 mr-2" />
                  API测试
                </Link>
              </Button>
            </div>
          </div>
        </div>

        {/* 统计卡片 - 现代化设计 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="data-card-enhanced stats-card-primary">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-primary/80">总关键词</CardTitle>
              <Database className="h-5 w-5 text-primary" />
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-foreground mb-1">
                {loading ? <Skeleton className="h-8 w-16" /> : statistics.total.toLocaleString()}
              </div>
              <p className="text-xs text-muted-foreground">
                数据库中的关键词总数
              </p>
              {!loading && statistics.total > 0 && (
                <div className="mt-2">
                  <Progress value={100} className="h-1" />
                </div>
              )}
            </CardContent>
          </Card>

          <Card className="data-card-enhanced stats-card-success">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-success/80">已分析</CardTitle>
              <CheckCircle className="h-5 w-5 text-success" />
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-success mb-1">
                {loading ? <Skeleton className="h-8 w-16" /> : statistics.analyzed.toLocaleString()}
              </div>
              <p className="text-xs text-muted-foreground mb-2">
                已完成AI分析的关键词
              </p>
              {!loading && statistics.total > 0 && (
                <div className="space-y-1">
                  <div className="flex justify-between text-xs">
                    <span>完成率</span>
                    <span className="font-medium">{Math.round((statistics.analyzed / statistics.total) * 100)}%</span>
                  </div>
                  <Progress value={(statistics.analyzed / statistics.total) * 100} className="h-1" />
                </div>
              )}
            </CardContent>
          </Card>

          <Card className="data-card-enhanced stats-card-warning">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-warning/80">待分析</CardTitle>
              <AlertCircle className="h-5 w-5 text-warning" />
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-warning mb-1">
                {loading ? <Skeleton className="h-8 w-16" /> : statistics.unanalyzed.toLocaleString()}
              </div>
              <p className="text-xs text-muted-foreground mb-2">
                等待AI分析的关键词
              </p>
              {!loading && statistics.total > 0 && (
                <div className="space-y-1">
                  <div className="flex justify-between text-xs">
                    <span>待处理</span>
                    <span className="font-medium">{Math.round((statistics.unanalyzed / statistics.total) * 100)}%</span>
                  </div>
                  <Progress value={(statistics.unanalyzed / statistics.total) * 100} className="h-1" />
                </div>
              )}
            </CardContent>
          </Card>

          <Card className="data-card-enhanced stats-card-secondary">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-secondary/80">分类数</CardTitle>
              <BarChart3 className="h-5 w-5 text-secondary" />
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-secondary mb-1">
                {loading ? <Skeleton className="h-8 w-16" /> : statistics.category_count.toLocaleString()}
              </div>
              <p className="text-xs text-muted-foreground">
                已使用的分类数量
              </p>
              <div className="mt-2 flex items-center space-x-1">
                <Badge className="badge-trend text-xs">
                  多样化
                </Badge>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 功能区域 */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* 最近活动 - 现代化设计 */}
          <Card className="idea-card-enhanced">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center space-x-2">
                    <Clock className="h-5 w-5 text-primary" />
                    <span>最近活动</span>
                  </CardTitle>
                  <CardDescription>
                    近7天的操作记录和文件上传历史
                  </CardDescription>
                </div>
                <Button
                  onClick={handleRefresh}
                  disabled={loading}
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0"
                >
                  <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="space-y-4">
                  {Array.from({ length: 3 }).map((_, index) => (
                    <div key={index} className="flex items-center space-x-3">
                      <Skeleton className="h-10 w-10 rounded-full" />
                      <div className="flex-1 space-y-2">
                        <Skeleton className="h-4 w-3/4" />
                        <Skeleton className="h-3 w-1/2" />
                      </div>
                    </div>
                  ))}
                </div>
              ) : recentActivity.length > 0 ? (
                <div className="space-y-4">
                  {recentActivity.map((activity) => {
                    const Icon = getActivityIcon(activity.type)
                    return (
                      <div key={activity.id} className="flex items-center space-x-3 p-3 bg-muted/30 rounded-lg hover:bg-muted/50 transition-colors">
                        <div className={`p-2 rounded-full ${
                          activity.type === 'import' ? 'bg-primary/10 text-primary' :
                          activity.type === 'analyze' ? 'bg-success/10 text-success' :
                          activity.type === 'export' ? 'bg-secondary/10 text-secondary' :
                          'bg-muted text-muted-foreground'
                        }`}>
                          <Icon className="h-4 w-4" />
                        </div>
                        <div className="flex-1">
                          <p className="text-sm font-medium text-foreground">{activity.description}</p>
                          <div className="flex items-center space-x-2 mt-1">
                            <p className="text-xs text-muted-foreground">
                              {activity.import_date || new Date(activity.timestamp).toLocaleDateString()}
                            </p>
                            {activity.filename && (
                              <Badge className="badge-trend text-xs">
                                {activity.filename.length > 20 ? activity.filename.substring(0, 20) + '...' : activity.filename}
                              </Badge>
                            )}
                          </div>
                        </div>
                        {activity.count && (
                          <Badge className="badge-analyzed">{activity.count} 个</Badge>
                        )}
                      </div>
                    )
                  })}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Activity className="h-12 w-12 text-muted-foreground/50 mx-auto mb-3" />
                  <p className="text-sm text-muted-foreground">暂无最近活动</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* 快速操作 */}
          <Card className="idea-card-enhanced">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Zap className="h-5 w-5 text-secondary" />
                <span>快速操作</span>
              </CardTitle>
              <CardDescription>
                常用功能快速入口
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 gap-3">
                <Button asChild className="btn-primary-enhanced justify-start h-12">
                  <Link href="/console/upload">
                    <Upload className="h-5 w-5 mr-3" />
                    <div className="text-left">
                      <div className="font-medium">上传关键词</div>
                      <div className="text-xs opacity-80">批量导入CSV文件</div>
                    </div>
                  </Link>
                </Button>

                <Button asChild variant="outline" className="btn-outline-enhanced justify-start h-12">
                  <Link href="/console/analyze">
                    <Target className="h-5 w-5 mr-3" />
                    <div className="text-left">
                      <div className="font-medium">AI分析</div>
                      <div className="text-xs opacity-70">智能关键词分析</div>
                    </div>
                  </Link>
                </Button>

                <Button asChild variant="outline" className="btn-outline-enhanced justify-start h-12">
                  <Link href="/console/keywords">
                    <Database className="h-5 w-5 mr-3" />
                    <div className="text-left">
                      <div className="font-medium">关键词管理</div>
                      <div className="text-xs opacity-70">查看和管理数据</div>
                    </div>
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 空状态提示 - 现代化设计 */}
        {!loading && statistics.total === 0 && (
          <Card className="idea-card-enhanced text-center py-12">
            <CardContent>
              <div className="max-w-md mx-auto">
                <Database className="h-20 w-20 text-primary/50 mx-auto mb-6" />
                <h3 className="text-2xl font-bold text-foreground mb-3">开始您的创意之旅</h3>
                <p className="text-muted-foreground mb-8">
                  还没有关键词数据？上传您的第一批关键词，开始使用AI智能分析功能。
                </p>
                <div className="flex flex-col sm:flex-row gap-3 justify-center">
                  <Button asChild className="btn-primary-enhanced">
                    <Link href="/console/upload">
                      <Plus className="h-4 w-4 mr-2" />
                      上传关键词
                    </Link>
                  </Button>
                  <Button asChild variant="outline" className="btn-outline-enhanced">
                    <Link href="/console/analyze">
                      <Zap className="h-4 w-4 mr-2" />
                      了解AI分析
                    </Link>
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </ConsoleLayout>
  )
}
