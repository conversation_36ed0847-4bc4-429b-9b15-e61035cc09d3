/**
 * 客户端分段报告生成器
 * 适配Vercel Serverless架构，避免超时问题
 */

export class ClientSideReportGenerator {
  constructor(keywordId) {
    this.keywordId = keywordId
    this.sections = [
      {
        id: 'algorithms',
        name: '算法分析',
        timeout: 25000,
        progress: 15,
        description: '正在执行8大核心算法分析...'
      },
      {
        id: 'competition',
        name: '竞争分析',
        timeout: 30000,
        progress: 35,
        description: '正在分析市场竞争格局...'
      },
      {
        id: 'intent',
        name: '意图分析',
        timeout: 20000,
        progress: 50,
        description: '正在分析用户搜索意图...'
      },
      {
        id: 'traffic',
        name: '流量分析',
        timeout: 20000,
        progress: 65,
        description: '正在评估流量机会...'
      },
      {
        id: 'content',
        name: '内容策略',
        timeout: 20000,
        progress: 80,
        description: '正在挖掘内容机会...'
      },
      {
        id: 'business',
        name: '商业价值',
        timeout: 20000,
        progress: 90,
        description: '正在评估商业价值...'
      },
      {
        id: 'summary',
        name: '综合总结',
        timeout: 20000,
        progress: 100,
        description: '正在生成综合评分和建议...'
      }
    ]
    this.results = {}
    this.onProgress = null
    this.onError = null
    this.onComplete = null
    this.abortController = new AbortController()
  }

  // 设置回调函数
  setCallbacks({ onProgress, onError, onComplete }) {
    this.onProgress = onProgress
    this.onError = onError
    this.onComplete = onComplete
  }

  // 开始生成报告
  async generateReport() {
    try {
      this.updateProgress(0, '开始生成详细分析报告...')

      for (let i = 0; i < this.sections.length; i++) {
        const section = this.sections[i]
        
        // 检查是否被取消
        if (this.abortController.signal.aborted) {
          throw new Error('报告生成已取消')
        }

        // 更新进度
        this.updateProgress(section.progress, section.description)

        try {
          // 检查缓存
          const cachedResult = this.getFromCache(section.id)
          if (cachedResult) {
            this.results[section.id] = cachedResult
            continue
          }

          // 生成段落
          const result = await this.generateSection(section)
          this.results[section.id] = result

          // 保存到缓存
          this.saveToCache(section.id, result)

          // 短暂延迟，避免API限制
          await this.delay(500)

        } catch (error) {
          if (error.name === 'TimeoutError' || error.name === 'AbortError') {
            // 超时重试
            const retryResult = await this.retrySection(section)
            this.results[section.id] = retryResult
          } else {
            throw error
          }
        }
      }

      // 合并结果
      const finalReport = this.combineResults(this.results)
      
      // 保存完整报告到数据库
      await this.saveFinalReport(finalReport)

      this.updateProgress(100, '报告生成完成！')
      
      if (this.onComplete) {
        this.onComplete(finalReport)
      }

      return finalReport

    } catch (error) {
      console.error('报告生成失败:', error)
      
      if (this.onError) {
        this.onError(error.message)
      }
      
      throw error
    }
  }

  // 生成单个段落
  async generateSection(section) {
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('TimeoutError')), section.timeout)
    })

    const requestPromise = fetch('/api/keywords/generate-section', {
      method: 'POST',
      headers: { 
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        keyword_id: this.keywordId,
        section_id: section.id,
        previous_results: this.results
      }),
      signal: this.abortController.signal
    })

    const response = await Promise.race([requestPromise, timeoutPromise])
    
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(errorData.message || `HTTP ${response.status}`)
    }

    const data = await response.json()
    
    if (!data.success) {
      throw new Error(data.message || '段落生成失败')
    }

    return data.result
  }

  // 重试段落生成
  async retrySection(section, maxRetries = 3) {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        this.updateProgress(
          section.progress - 5,
          `${section.description} (重试 ${attempt}/${maxRetries})`
        )

        // 逐步增加超时时间
        const extendedSection = {
          ...section,
          timeout: section.timeout + (10000 * attempt) // 每次重试增加10秒
        }
        return await this.generateSection(extendedSection)

      } catch (error) {
        if (attempt === maxRetries) {
          // 最后一次重试失败，返回默认结果
          return this.getDefaultResult(section.id)
        }

        // 等待后重试，时间逐步增加
        await this.delay(2000 * attempt)
      }
    }
  }

  // 获取默认结果
  getDefaultResult(sectionId) {
    const defaults = {
      algorithms: {
        type: 'algorithms',
        data: { error: '算法分析超时，请稍后重试' },
        summary: { confidence: 0 }
      },
      competition: {
        type: 'competition',
        data: { error: '竞争分析超时，请稍后重试' },
        summary: { intensity: 'unknown' }
      },
      intent: {
        type: 'intent',
        data: { error: '意图分析超时，请稍后重试' }
      },
      traffic: {
        type: 'traffic',
        data: { error: '流量分析超时，请稍后重试' }
      },
      content: {
        type: 'content',
        data: { error: '内容分析超时，请稍后重试' }
      },
      business: {
        type: 'business',
        data: { error: '商业分析超时，请稍后重试' }
      },
      summary: {
        type: 'summary',
        data: { error: '综合分析超时，请稍后重试' }
      }
    }

    return defaults[sectionId] || { type: sectionId, data: { error: '分析超时' } }
  }

  // 合并所有结果
  combineResults(results) {
    const timestamp = new Date().toISOString()
    
    return {
      keyword_id: this.keywordId,
      generated_at: timestamp,
      sections: results,
      summary: {
        algorithm_confidence: results.algorithms?.summary?.confidence || 85,
        competition_intensity: results.competition?.data?.intensity || results.algorithms?.data?.enhanced_seo_analysis?.level || 'medium',
        overall_score: this.calculateOverallScore(results),
        recommendation: this.generateRecommendation(results)
      },
      metadata: {
        generation_method: 'client_segmented',
        total_sections: this.sections.length,
        completed_sections: Object.keys(results).length,
        generation_time: this.getGenerationTime()
      }
    }
  }

  // 计算总体评分
  calculateOverallScore(results) {
    const scores = []

    // 从算法分析中提取置信度评分
    if (results.algorithms?.summary?.confidence) {
      scores.push(results.algorithms.summary.confidence / 10)
    }

    // 从竞争分析中提取评分（竞争越低分数越高）
    if (results.competition?.data?.intensity_score) {
      scores.push((100 - results.competition.data.intensity_score) / 10)
    } else if (results.algorithms?.data?.enhanced_seo_analysis?.score) {
      scores.push((100 - results.algorithms.data.enhanced_seo_analysis.score) / 10)
    }

    // 从商业价值分析中提取评分
    if (results.business?.data?.commercial_score) {
      scores.push(results.business.data.commercial_score)
    } else if (results.algorithms?.data?.commercial_value_analysis?.score) {
      scores.push(results.algorithms.data.commercial_value_analysis.score)
    }

    // 从流量分析中提取评分
    if (results.algorithms?.data?.enhanced_volume_prediction?.volume) {
      const volume = results.algorithms.data.enhanced_volume_prediction.volume
      const volumeScore = Math.min(10, Math.max(1, Math.log10(volume)))
      scores.push(volumeScore)
    }

    return scores.length > 0 ?
      Math.round((scores.reduce((sum, score) => sum + score, 0) / scores.length) * 10) / 10 :
      7 // 默认评分
  }

  // 生成推荐
  generateRecommendation(results) {
    const overallScore = this.calculateOverallScore(results)
    
    if (overallScore > 7) return '强烈推荐'
    if (overallScore > 5) return '推荐'
    if (overallScore > 3) return '谨慎考虑'
    return '不推荐'
  }

  // 保存最终报告到数据库
  async saveFinalReport(report) {
    try {
      // 保存报告到数据库

      const response = await fetch('/api/keywords/save-report', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          keyword_id: this.keywordId,
          report_data: report
        })
      })

      const result = await response.json()

      if (!response.ok) {
        console.error('保存报告失败:', {
          status: response.status,
          error: result
        })
      }
    } catch (error) {
      console.error('保存报告错误:', error)
    }
  }

  // 缓存管理
  getFromCache(sectionId) {
    try {
      const cacheKey = `report_${this.keywordId}_${sectionId}`
      const cached = localStorage.getItem(cacheKey)
      
      if (cached) {
        const data = JSON.parse(cached)
        const now = Date.now()
        
        // 缓存1小时
        if (now - data.timestamp < 3600000) {
          return data.result
        } else {
          localStorage.removeItem(cacheKey)
        }
      }
    } catch (error) {
      // 缓存读取失败，忽略
    }
    
    return null
  }

  saveToCache(sectionId, result) {
    try {
      const cacheKey = `report_${this.keywordId}_${sectionId}`
      const data = {
        result,
        timestamp: Date.now()
      }
      
      localStorage.setItem(cacheKey, JSON.stringify(data))
    } catch (error) {
      // 缓存保存失败，忽略
    }
  }

  // 清除缓存
  clearCache() {
    try {
      for (const section of this.sections) {
        const cacheKey = `report_${this.keywordId}_${section.id}`
        localStorage.removeItem(cacheKey)
      }
    } catch (error) {
      // 缓存清除失败，忽略
    }
  }

  // 取消生成
  cancel() {
    this.abortController.abort()
  }

  // 工具函数
  updateProgress(progress, message) {
    if (this.onProgress) {
      this.onProgress(progress, message)
    }
  }

  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  getGenerationTime() {
    // 这里可以记录开始时间并计算总耗时
    return '未知'
  }
}

// 导出便捷函数
export async function generateReportSegmented(keywordId, callbacks = {}) {
  const generator = new ClientSideReportGenerator(keywordId)
  generator.setCallbacks(callbacks)
  return await generator.generateReport()
}
