import type { Metadata } from 'next'

export const metadata: Metadata = {
  title: '联盟营销完整指南 - CatchIdeas',
  description: '从产品选择到内容撰写，从评测结构到推广策略，掌握联盟营销的所有核心技能。包含亚马逊联盟、产品评测、SEO优化等实用技巧。',
  keywords: '联盟营销,亚马逊联盟,产品评测,SEO优化,内容营销,推广策略,佣金赚钱,产品选择,评测模板',
  openGraph: {
    title: '联盟营销完整指南 - CatchIdeas',
    description: '从产品选择到内容撰写，从评测结构到推广策略，掌握联盟营销的所有核心技能',
    type: 'website',
  },
}

export default function AffiliateGuideLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return children
}
