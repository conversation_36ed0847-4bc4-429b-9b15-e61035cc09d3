<?php
require_once '../utils/Response.php';
require_once '../models/Keyword.php';

/**
 * 手动导入API
 * 处理TXT文件和文本粘贴导入
 */

// 处理CORS预检请求
Response::handleOptions();

// 验证请求方法
$method = Response::validateMethod(['POST']);

try {
    // 获取请求数据
    $data = Response::getRequestData();

    // 验证必需字段
    if (!isset($data['keywords']) || !is_array($data['keywords'])) {
        Response::validationError(['keywords' => '关键词数组不能为空']);
    }

    if (empty($data['keywords'])) {
        Response::validationError(['keywords' => '至少需要一个关键词']);
    }

    if (count($data['keywords']) > 1000) {
        Response::validationError(['keywords' => '关键词数量超过限制（最多1000个）']);
    }
    
    // 获取导入来源
    $source = isset($data['source']) ? $data['source'] : 'manual_import';
    
    // 直接处理关键词，不再进行预过滤（已由AI处理）
    $validKeywords = [];
    $errors = [];
    $seenKeywords = []; // 本次导入中的去重
    $seenIds = []; // 本次导入中的ID去重
    $duplicateCount = 0;
    $errorCount = 0;

    foreach ($data['keywords'] as $index => $keywordData) {
        // 验证关键词格式
        if (!isset($keywordData['keyword']) || empty(trim($keywordData['keyword']))) {
            $errors[] = "第" . ($index + 1) . "个关键词不能为空";
            $errorCount++;
            continue;
        }

        $keyword = trim($keywordData['keyword']);

        // 简单的本批次去重（保持数据一致性）
        $normalizedKeyword = strtolower(trim(preg_replace('/\s+/', ' ', $keyword)));

        if (isset($seenKeywords[$normalizedKeyword])) {
            $duplicateCount++;
            continue; // 跳过重复，不记录为错误
        }

        // 记录已处理的关键词
        $seenKeywords[$normalizedKeyword] = true;

        // 处理ID冲突 - 确保slug ID的唯一性
        $baseId = $keywordData['id'] ?? uniqid('manual_', true);
        $finalId = $baseId;
        $counter = 1;

        // 检查ID是否已存在（简单检查，避免数据库冲突）
        while (isset($seenIds[$finalId])) {
            $finalId = $baseId . '-' . $counter;
            $counter++;
        }
        $seenIds[$finalId] = true;

        // 构建关键词数据
        $keywordRecord = [
            'id' => $finalId,
            'keyword' => $keyword,
            'source' => $source,
            'import_date' => $keywordData['importDate'] ?? date('Y-m-d'),
            'created_at' => $keywordData['importedAt'] ?? date('Y-m-d H:i:s')
        ];

        $validKeywords[] = $keywordRecord;
    }

    // 如果没有有效关键词
    if (empty($validKeywords)) {
        Response::error('没有有效的关键词可以导入', 400);
    }

    // 使用Keyword模型批量导入
    $keywordModel = new Keyword();
    $importResult = $keywordModel->batchCreate($validKeywords);

    // 构建响应数据
    $responseData = [
        'attempted' => count($validKeywords),
        'imported' => $importResult['inserted'],
        'duplicates' => $importResult['skipped'] + $duplicateCount, // 包含本批次去重
        'errors' => $errorCount,
        'details' => [
            'batch_duplicates' => $duplicateCount,
            'database_duplicates' => $importResult['skipped'],
            'validation_errors' => $errorCount,
            'database_errors' => 0
        ]
    ];

    // 记录导入日志
    if (class_exists('ImportLog')) {
        try {
            $importLog = new ImportLog();
            $importLog->create([
                'filename' => $source === 'txt_file' ? 'manual_upload.txt' : 'manual_paste.txt',
                'total_lines' => count($data['keywords']),
                'raw_keywords' => count($data['keywords']),
                'valid_keywords' => count($validKeywords),
                'imported_keywords' => $importResult['imported'],
                'duplicate_keywords' => $responseData['duplicates'],
                'error_keywords' => $responseData['errors'],
                'import_source' => $source,
                'created_at' => date('Y-m-d H:i:s')
            ]);
        } catch (Exception $e) {
            // 日志记录失败不影响主流程
            error_log("导入日志记录失败: " . $e->getMessage());
        }
    }

    // 返回成功响应
    Response::success($responseData, '关键词导入完成');

} catch (Exception $e) {
    error_log("手动导入API错误: " . $e->getMessage());
    
    // 根据错误类型返回不同的错误信息
    $errorMessage = '导入过程中发生错误';
    $statusCode = 500;

    if (strpos($e->getMessage(), '数据库') !== false) {
        $errorMessage = '数据库操作失败，请稍后重试';
        $statusCode = 503;
    } elseif (strpos($e->getMessage(), '连接') !== false) {
        $errorMessage = '数据库连接失败，请稍后重试';
        $statusCode = 503;
    } elseif (strpos($e->getMessage(), 'Duplicate') !== false) {
        $errorMessage = '关键词已存在';
        $statusCode = 409;
    }

    Response::error($errorMessage, $statusCode);
}
?>
