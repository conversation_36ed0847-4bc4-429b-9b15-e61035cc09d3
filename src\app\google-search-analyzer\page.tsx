'use client'

import { useState, useMemo, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card'
import { Badge } from '../../components/ui/badge'
import { Button } from '../../components/ui/button'
import { Textarea } from '../../components/ui/textarea'
import { Alert, AlertDescription } from '../../components/ui/alert'
import { 
  Search, 
  ExternalLink, 
  Download, 
  Trash2, 
  Info, 
  Target,
  Globe,
  Zap,
  CheckCircle,
  XCircle,
  Clock,
  BarChart3,
  FileText,
  Eye
} from 'lucide-react'
import FrontendHeader from '../../components/frontend-header'
import FrontendFooter from '../../components/frontend-footer'
import { Toaster, toast } from 'sonner'

interface SearchResult {
  title: string
  link: string
  displayLink: string
  snippet: string
  pagemap?: {
    metatags?: Array<{
      'og:title'?: string
      'og:description'?: string
      'og:image'?: string
      [key: string]: any
    }>
    cse_image?: Array<{
      src: string
    }>
  }
}

interface KeywordAnalysis {
  keyword: string
  status: 'pending' | 'success' | 'error'
  searchInformation?: {
    formattedTotalResults: string
    searchTime: number
  }
  results?: SearchResult[]
  error?: string
  timestamp?: string
}

export default function GoogleSearchAnalyzerPage() {
  const { data: session, status } = useSession()
  const router = useRouter()

  const [inputText, setInputText] = useState('')
  const [analyses, setAnalyses] = useState<KeywordAnalysis[]>([])
  const [loading, setLoading] = useState(false)
  const [selectedKeywords, setSelectedKeywords] = useState<string[]>([])
  const [dailyUsage, setDailyUsage] = useState(0)
  const [usageLoading, setUsageLoading] = useState(true)

  // 检查是否为管理员
  const isAdmin = Boolean(session?.user?.email === '<EMAIL>')
  const dailyLimit = isAdmin ? Infinity : 10

  // 设置页面标题
  useEffect(() => {
    document.title = 'Google搜索结果分析工具 - CatchIdeas'
  }, [])

  // 不在页面加载时检查登录状态，只在用户操作时检查
  // 这样有利于SEO，让搜索引擎可以正常爬取页面

  // 获取今日使用次数（只在用户已登录时）
  useEffect(() => {
    if (!session || session === null) {
      setUsageLoading(false)
      return
    }

    const fetchDailyUsage = async () => {
      try {
        const response = await fetch('/api/user-usage-stats')
        const data = await response.json()

        if (data.success) {
          setDailyUsage(Number(data.data.dailyUsage) || 0)
        }
      } catch (error) {
        console.error('获取使用统计失败:', error)
      } finally {
        setUsageLoading(false)
      }
    }

    fetchDailyUsage()
  }, [session])

  // 解析输入的关键词
  const parseKeywords = (text: string): string[] => {
    return text
      .split(/[\n\r]+/)
      .map(k => k.trim())
      .filter(k => k.length > 0)
      .slice(0, 10) // 最多10个关键词
  }

  // 当前输入的关键词数量（使用 useMemo 优化）
  const currentKeywordsCount = useMemo(() => {
    return parseKeywords(inputText).length
  }, [inputText])



  // 开始分析
  const startAnalysis = async () => {
    // 首先检查登录状态
    if (!session || session === null) {
      const callbackUrl = encodeURIComponent(window.location.href)
      router.push(`/auth/signin?callbackUrl=${callbackUrl}`)
      return
    }

    const keywords = parseKeywords(inputText)

    if (keywords.length === 0) {
      toast.error('请输入关键词')
      return
    }

    if (keywords.length > 10) {
      toast.error('最多支持10个关键词')
      return
    }

    // 检查使用次数限制（管理员不限制）
    if (!isAdmin && dailyUsage + keywords.length > dailyLimit) {
      toast.error(`今日使用次数不足。您今日已使用 ${dailyUsage} 次，剩余 ${dailyLimit - dailyUsage} 次，本次需要 ${keywords.length} 次。`)
      return
    }

    setLoading(true)
    
    // 初始化分析状态
    const initialAnalyses: KeywordAnalysis[] = keywords.map(keyword => ({
      keyword,
      status: 'pending',
      timestamp: new Date().toISOString()
    }))
    
    setAnalyses(initialAnalyses)
    
    // 逐个分析关键词
    for (let i = 0; i < keywords.length; i++) {
      const keyword = keywords[i]
      
      try {
        // 调用Google搜索API
        const response = await fetch('/api/google-search-analyze', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ keyword })
        })

        const data = await response.json()

        if (data.success) {
          // 更新成功状态
          setAnalyses(prev => prev.map(analysis => 
            analysis.keyword === keyword 
              ? {
                  ...analysis,
                  status: 'success',
                  searchInformation: data.data.searchInformation,
                  results: data.data.items || []
                }
              : analysis
          ))
          toast.success(`"${keyword}" 分析完成`)

          // 更新使用次数（非管理员）
          if (!isAdmin) {
            setDailyUsage(prev => prev + 1)
          }
        } else {
          // 更新失败状态
          setAnalyses(prev => prev.map(analysis => 
            analysis.keyword === keyword 
              ? {
                  ...analysis,
                  status: 'error',
                  error: data.error || '分析失败'
                }
              : analysis
          ))
          toast.error(`"${keyword}" 分析失败，请稍后重试`)
        }
      } catch (error) {
        // 更新错误状态
        setAnalyses(prev => prev.map(analysis => 
          analysis.keyword === keyword 
            ? {
                ...analysis,
                status: 'error',
                error: '网络请求失败'
              }
            : analysis
        ))
        toast.error(`"${keyword}" 分析失败，请检查网络连接后重试`)
      }

      // 添加延迟避免API限制
      if (i < keywords.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 1000))
      }
    }

    setLoading(false)
    toast.success('所有关键词分析完成')
  }

  // 删除成功的分析结果
  const deleteSuccessful = () => {
    const successfulCount = analyses.filter(a => a.status === 'success').length
    setAnalyses(prev => prev.filter(a => a.status !== 'success'))
    setSelectedKeywords([])
    toast.success(`已删除 ${successfulCount} 个成功的分析结果`)
  }

  // 删除选中的分析结果
  const deleteSelected = () => {
    if (selectedKeywords.length === 0) {
      toast.error('请先选择要删除的关键词')
      return
    }
    
    setAnalyses(prev => prev.filter(a => !selectedKeywords.includes(a.keyword)))
    setSelectedKeywords([])
    toast.success(`已删除 ${selectedKeywords.length} 个选中的分析结果`)
  }

  // 切换选中状态
  const toggleSelection = (keyword: string) => {
    setSelectedKeywords(prev => 
      prev.includes(keyword)
        ? prev.filter(k => k !== keyword)
        : [...prev, keyword]
    )
  }

  // 全选/取消全选
  const toggleSelectAll = () => {
    if (selectedKeywords.length === analyses.length) {
      setSelectedKeywords([])
    } else {
      setSelectedKeywords(analyses.map(a => a.keyword))
    }
  }

  // 导出MD格式（每个关键词单独文件）
  const exportToMarkdown = () => {
    const successfulAnalyses = analyses.filter(a => a.status === 'success')

    if (successfulAnalyses.length === 0) {
      toast.error('没有成功的分析结果可以导出')
      return
    }

    // 为每个关键词创建单独的MD文件
    successfulAnalyses.forEach(analysis => {
      const markdown = generateMarkdownForKeyword(analysis)

      // 创建下载链接
      const blob = new Blob([markdown], { type: 'text/markdown' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url

      // 使用关键词作为文件名，处理特殊字符
      const safeKeyword = analysis.keyword.replace(/[^a-zA-Z0-9\u4e00-\u9fa5]/g, '_')
      a.download = `${safeKeyword}.md`

      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
    })

    toast.success(`已单独导出 ${successfulAnalyses.length} 个MD文件，每个关键词一个文件`)
  }

  // 生成单个关键词的Markdown内容（只包含SEO分析Prompt）
  const generateMarkdownForKeyword = (analysis: KeywordAnalysis): string => {
    const { keyword, results = [] } = analysis

    let markdown = `你是资深SEO顾问和内容策略分析专家。我将提供某个关键词在Google搜索结果中的前十名页面信息（JSON格式）。\n\n`

    markdown += `请从以下维度进行**量化分析**和**战略洞察**：\n\n`
    markdown += `## 📊 数据化分析维度\n\n`
    markdown += `### 1. **标题策略深度分析**\n`
    markdown += `- 关键词密度和变体使用（完全匹配/部分匹配/语义匹配）\n`
    markdown += `- 标题长度分布（字符数统计）\n`
    markdown += `- 修饰词分析：数字、年份、品牌词、情绪词、行动词的使用频率\n`
    markdown += `- 标题结构模式识别（品牌前置/后置、分隔符使用习惯）\n`
    markdown += `- CTR优化元素：括号、特殊符号使用情况\n\n`

    markdown += `### 2. **Meta描述与摘要优化**\n`
    markdown += `- 描述长度合规性（150-160字符）\n`
    markdown += `- 关键词自然融入度和密度\n`
    markdown += `- CTA元素使用（立即、免费、下载等）\n`
    markdown += `- 痛点/收益平衡比例\n`
    markdown += `- 与标题的信息互补性\n\n`

    markdown += `### 3. **域名权重与类型分析**\n`
    markdown += `- 域名类型分布：官网(.com/.org)、子域名、路径结构\n`
    markdown += `- 估算域名权重等级（通过域名年龄、知名度判断）\n`
    markdown += `- 垂直领域专业度vs通用性权重\n`
    markdown += `- 品牌域名vs关键词域名策略\n\n`

    markdown += `### 4. **技术SEO指标评估**\n`
    markdown += `- URL结构优化程度（短URL、语义化、层级深度）\n`
    markdown += `- HTTPS使用率\n`
    markdown += `- 移动友好性证据（viewport设置等）\n`
    markdown += `- 页面技术架构线索\n\n`

    markdown += `### 5. **内容架构与用户体验**\n`
    markdown += `- 内容类型分布：教程型、工具型、资讯型、商业型\n`
    markdown += `- 内容深度层级：浅层介绍、中等详述、深度分析\n`
    markdown += `- 多媒体元素丰富度：图片、视频、交互元素\n`
    markdown += `- 用户参与度指标（评论、分享按钮等）\n\n`

    markdown += `### 6. **结构化数据与富摘要**\n`
    markdown += `- Schema标记类型识别和完整性\n`
    markdown += `- Rich Snippet获得情况\n`
    markdown += `- 评分、评论等信任信号\n\n`

    markdown += `## 🎯 战略洞察分析\n\n`
    markdown += `### 7. **搜索意图精准匹配**\n`
    markdown += `- 信息型意图满足度（Know查询）\n`
    markdown += `- 导航型意图满足度（Go查询）\n`
    markdown += `- 交易型意图满足度（Do查询）\n`
    markdown += `- 调研型意图满足度（Buy查询）\n`
    markdown += `- 混合意图的权重分配\n\n`

    markdown += `### 8. **竞争格局量化评估**\n`
    markdown += `- 竞争强度评分（1-10分）：内容质量、域名权重、优化程度\n`
    markdown += `- 竞争对手类型分布比例\n`
    markdown += `- 新进入者机会评估\n`
    markdown += `- 长尾关键词拓展空间\n\n`

    markdown += `### 9. **内容差异化机会识别**\n`
    markdown += `- 内容空白点分析（未覆盖的子话题）\n`
    markdown += `- 角度创新机会（不同视角、用户群体）\n`
    markdown += `- 深度vs广度的平衡点\n`
    markdown += `- 实用性vs娱乐性的最优比例\n\n`

    markdown += `## 📈 量化评分系统\n\n`
    markdown += `为每个分析维度打分（1-10分），并说明评分依据：\n`
    markdown += `- **内容质量分**：原创性、深度、实用性\n`
    markdown += `- **SEO技术分**：标题、meta、结构化数据完整性\n`
    markdown += `- **用户体验分**：易读性、加载速度、交互性\n`
    markdown += `- **权威性分**：域名权重、外链质量、品牌信任度\n`
    markdown += `- **差异化分**：独特性、创新性、竞争优势\n\n`

    markdown += `## 🚀 最终输出要求\n\n`
    markdown += `### 竞争格局总结\n`
    markdown += `- **市场成熟度**：红海/蓝海/细分机会\n`
    markdown += `- **准入门槛**：技术要求、内容要求、资源投入\n`
    markdown += `- **成功关键因素**：排名前3页面的共同特征\n\n`

    markdown += `### 超越策略建议\n`
    markdown += `- **内容策略**：主题拓展、深度增强、角度创新\n`
    markdown += `- **技术优化**：页面速度、结构化数据、用户体验\n`
    markdown += `- **差异化定位**：目标用户细分、独特价值主张\n\n`

    markdown += `### 可执行方案\n`
    markdown += `- **标题优化建议**：3-5个标题候选，含A/B测试建议\n`
    markdown += `- **内容大纲**：详细到H2/H3级别，包含字数分配\n`
    markdown += `- **SEO检查清单**：技术要点、内容要点、发布后优化\n`
    markdown += `- **KPI设定**：排名目标、流量预期、转化指标\n\n`

    markdown += `### 风险评估\n`
    markdown += `- **算法风险**：过度优化、内容质量、用户体验\n`
    markdown += `- **竞争风险**：大厂进入、算法偏好变化\n`
    markdown += `- **执行风险**：资源投入、时间成本、ROI评估\n\n`

    markdown += `关键词：「${keyword}」\n\n`
    markdown += `搜索结果数据（JSON格式）：\n\n`
    markdown += `\`\`\`json\n`
    markdown += JSON.stringify(results, null, 2)
    markdown += `\n\`\`\`\n\n`
    markdown += `请按上述结构化方式输出详细分析，重点关注实用性和可操作性。\n`

    return markdown
  }

  // 统计信息
  const stats = useMemo(() => {
    const total = analyses.length
    const successful = analyses.filter(a => a.status === 'success').length
    const failed = analyses.filter(a => a.status === 'error').length
    const pending = analyses.filter(a => a.status === 'pending').length
    
    return { total, successful, failed, pending }
  }, [analyses])

  return (
    <div className="min-h-screen bg-background">
      <Toaster position="top-right" />
      {/* 使用统一前台顶部组件 */}
      <FrontendHeader />

      {/* 页面标题区域 */}
      <section className="py-8 px-4 bg-gradient-to-br from-blue-50 to-purple-50 border-b">
        <div className="container mx-auto">
          <div className="max-w-4xl mx-auto text-center">
            <div className="flex items-center justify-center mb-4">
              <div className="p-3 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl mr-4">
                <Search className="h-8 w-8 text-white" />
              </div>
              <h1 className="text-4xl font-bold text-foreground">Google搜索结果分析工具</h1>
            </div>
            <p className="text-lg text-muted-foreground mb-4">
              分析Google搜索结果前10名页面的SEO策略，支持批量处理和MD导出
            </p>

            {/* 使用次数显示 */}
            {!usageLoading && (
              <div className="mb-6">
                {!session || session === null ? (
                  <Badge className="bg-blue-100 text-blue-800 border-blue-200 px-4 py-2">
                    请登录Google账户开始使用 - 每日免费10次
                  </Badge>
                ) : isAdmin ? (
                  <Badge className="bg-purple-100 text-purple-800 border-purple-200 px-4 py-2">
                    管理员账户 - 无使用限制
                  </Badge>
                ) : (
                  <Badge className={`px-4 py-2 ${
                    dailyUsage >= dailyLimit
                      ? 'bg-red-100 text-red-800 border-red-200'
                      : dailyUsage >= dailyLimit * 0.8
                        ? 'bg-yellow-100 text-yellow-800 border-yellow-200'
                        : 'bg-green-100 text-green-800 border-green-200'
                  }`}>
                    今日已使用 {dailyUsage}/{dailyLimit} 次
                  </Badge>
                )}
              </div>
            )}
            <div className="flex items-center justify-center gap-6 text-sm text-muted-foreground">
              <div className="flex items-center">
                <Target className="h-4 w-4 mr-2 text-blue-500" />
                批量分析
              </div>
              <div className="flex items-center">
                <BarChart3 className="h-4 w-4 mr-2 text-purple-500" />
                SEO洞察
              </div>
              <div className="flex items-center">
                <FileText className="h-4 w-4 mr-2 text-green-500" />
                MD导出
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 输入区域 */}
      <section className="py-8 px-4">
        <div className="container mx-auto">
          <div className="max-w-4xl mx-auto">
            <Card className="bg-white shadow-lg border-2 border-blue-100">
              <CardHeader>
                <CardTitle className="flex items-center text-xl">
                  <Search className="h-5 w-5 mr-2 text-blue-500" />
                  关键词输入
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Textarea
                    placeholder="请输入关键词，每行一个：&#10;AI writing tools&#10;best content generator&#10;copywriting software&#10;&#10;最多支持10个关键词"
                    value={inputText}
                    onChange={(e) => setInputText(e.target.value)}
                    className="min-h-[120px] border-2 border-blue-200 focus:border-blue-500 focus:ring-blue-500"
                    rows={6}
                    disabled={loading}
                  />
                </div>

                <Alert className="border-blue-200 bg-blue-50">
                  <Info className="h-4 w-4 text-blue-500" />
                  <AlertDescription className="text-blue-700">
                    <strong>使用说明：</strong>
                    每行输入一个关键词，最多支持10个。工具将调用Google搜索API获取每个关键词的前10个搜索结果，并提取标题、链接、域名、摘要、Meta标签等关键信息进行SEO分析。
                  </AlertDescription>
                </Alert>

                <div className="flex gap-3">
                  <Button
                    onClick={startAnalysis}
                    className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white"
                    disabled={Boolean(loading || !inputText.trim() || (Boolean(session) && !isAdmin && dailyUsage + currentKeywordsCount > dailyLimit))}
                  >
                    {loading ? (
                      <>
                        <Clock className="h-4 w-4 mr-2 animate-spin" />
                        分析中...
                      </>
                    ) : !session || session === null ? (
                      <>
                        <Search className="h-4 w-4 mr-2" />
                        登录并开始分析
                      </>
                    ) : (
                      <>
                        <Search className="h-4 w-4 mr-2" />
                        开始分析
                      </>
                    )}
                  </Button>

                  {analyses.length > 0 && (
                    <>
                      <Button
                        onClick={exportToMarkdown}
                        variant="outline"
                        className="bg-white border-green-200 text-green-600 hover:bg-green-50 hover:border-green-300"
                        disabled={stats.successful === 0}
                      >
                        <Download className="h-4 w-4 mr-2" />
                        单独导出MD ({stats.successful})
                      </Button>

                      <Button
                        onClick={deleteSuccessful}
                        variant="outline"
                        className="bg-white border-orange-200 text-orange-600 hover:bg-orange-50 hover:border-orange-300"
                        disabled={stats.successful === 0}
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        删除成功的
                      </Button>
                    </>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* 统计信息区域 */}
      {analyses.length > 0 && (
        <section className="py-6 px-4 bg-muted/30">
          <div className="container mx-auto">
            <div className="max-w-4xl mx-auto">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <Card className="text-center bg-white border border-gray-200">
                  <CardContent className="p-4">
                    <div className="text-2xl font-bold text-blue-600">{stats.total}</div>
                    <div className="text-sm text-muted-foreground">总关键词</div>
                  </CardContent>
                </Card>
                <Card className="text-center bg-white border border-gray-200">
                  <CardContent className="p-4">
                    <div className="text-2xl font-bold text-green-600">{stats.successful}</div>
                    <div className="text-sm text-muted-foreground">分析成功</div>
                  </CardContent>
                </Card>
                <Card className="text-center bg-white border border-gray-200">
                  <CardContent className="p-4">
                    <div className="text-2xl font-bold text-red-600">{stats.failed}</div>
                    <div className="text-sm text-muted-foreground">分析失败</div>
                  </CardContent>
                </Card>
                <Card className="text-center bg-white border border-gray-200">
                  <CardContent className="p-4">
                    <div className="text-2xl font-bold text-yellow-600">{stats.pending}</div>
                    <div className="text-sm text-muted-foreground">等待中</div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </section>
      )}

      {/* 结果显示区域 */}
      {analyses.length > 0 && (
        <section className="py-8 px-4">
          <div className="container mx-auto">
            <div className="max-w-6xl mx-auto">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-2xl font-bold text-foreground">分析结果</h2>
                <div className="flex items-center gap-3">
                  {analyses.length > 0 && (
                    <>
                      <Button
                        onClick={toggleSelectAll}
                        variant="outline"
                        size="sm"
                        className="bg-white border-gray-200 text-gray-600 hover:bg-gray-50 hover:border-gray-300"
                      >
                        {selectedKeywords.length === analyses.length ? '取消全选' : '全选'}
                      </Button>
                      {selectedKeywords.length > 0 && (
                        <Button
                          onClick={deleteSelected}
                          variant="destructive"
                          size="sm"
                          className="bg-red-600 hover:bg-red-700 text-white"
                        >
                          <Trash2 className="h-4 w-4 mr-2" />
                          删除选中 ({selectedKeywords.length})
                        </Button>
                      )}
                    </>
                  )}
                </div>
              </div>

              <div className="space-y-4">
                {analyses.map((analysis) => (
                  <Card key={analysis.keyword} className="bg-white border border-gray-200 hover:shadow-lg transition-all duration-200">
                    <CardHeader className="pb-3">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <input
                            type="checkbox"
                            checked={selectedKeywords.includes(analysis.keyword)}
                            onChange={() => toggleSelection(analysis.keyword)}
                            className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                          />
                          <CardTitle className="text-lg">{analysis.keyword}</CardTitle>
                        </div>
                        <div className="flex items-center space-x-2">
                          {analysis.status === 'success' && (
                            <Badge className="bg-green-100 text-green-800 border-green-200">
                              <CheckCircle className="h-3 w-3 mr-1" />
                              成功
                            </Badge>
                          )}
                          {analysis.status === 'error' && (
                            <Badge className="bg-red-100 text-red-800 border-red-200">
                              <XCircle className="h-3 w-3 mr-1" />
                              失败
                            </Badge>
                          )}
                          {analysis.status === 'pending' && (
                            <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">
                              <Clock className="h-3 w-3 mr-1 animate-spin" />
                              分析中
                            </Badge>
                          )}
                        </div>
                      </div>
                    </CardHeader>

                    <CardContent>
                      {analysis.status === 'success' && analysis.searchInformation && (
                        <div className="space-y-4">
                          {/* 搜索信息 */}
                          <div className="bg-blue-50 p-4 rounded-lg">
                            <h4 className="font-semibold text-blue-800 mb-2">搜索信息</h4>
                            <div className="grid grid-cols-2 gap-4 text-sm">
                              <div>
                                <span className="text-muted-foreground">搜索结果：</span>
                                <span className="font-medium">{analysis.searchInformation.formattedTotalResults}</span>
                              </div>
                              <div>
                                <span className="text-muted-foreground">搜索时间：</span>
                                <span className="font-medium">{analysis.searchInformation.searchTime}秒</span>
                              </div>
                            </div>
                          </div>

                          {/* 搜索结果预览 */}
                          {analysis.results && analysis.results.length > 0 && (
                            <div>
                              <h4 className="font-semibold mb-3">前{analysis.results.length}个搜索结果</h4>
                              <div className="space-y-3 max-h-96 overflow-y-auto">
                                {analysis.results.slice(0, 3).map((result, index) => (
                                  <div key={index} className="border rounded-lg p-3 bg-gray-50">
                                    <div className="flex items-start justify-between mb-2">
                                      <h5 className="font-medium text-sm line-clamp-2">{result.title}</h5>
                                      <Badge variant="outline" className="text-xs ml-2 bg-white border-gray-200">#{index + 1}</Badge>
                                    </div>
                                    <div className="text-xs text-muted-foreground mb-1">
                                      <Globe className="h-3 w-3 inline mr-1" />
                                      {result.displayLink}
                                    </div>
                                    <p className="text-xs text-muted-foreground line-clamp-2">{result.snippet}</p>
                                    <div className="flex items-center justify-between mt-2">
                                      <div className="flex items-center space-x-2 text-xs">
                                        {result.pagemap?.metatags?.[0]?.['og:title'] && (
                                          <Badge variant="secondary" className="text-xs bg-gray-100 text-gray-700">OG标题</Badge>
                                        )}
                                        {result.pagemap?.cse_image?.[0] && (
                                          <Badge variant="secondary" className="text-xs bg-gray-100 text-gray-700">有图片</Badge>
                                        )}
                                      </div>
                                      <Button
                                        onClick={() => window.open(result.link, '_blank')}
                                        size="sm"
                                        variant="ghost"
                                        className="text-xs h-6 px-2 bg-gray-100 hover:bg-gray-200 text-gray-600"
                                      >
                                        <ExternalLink className="h-3 w-3 mr-1" />
                                        访问
                                      </Button>
                                    </div>
                                  </div>
                                ))}
                                {analysis.results.length > 3 && (
                                  <div className="text-center text-sm text-muted-foreground">
                                    还有 {analysis.results.length - 3} 个结果，单独导出MD文件查看完整数据
                                  </div>
                                )}
                              </div>
                            </div>
                          )}
                        </div>
                      )}

                      {analysis.status === 'error' && (
                        <div className="bg-red-50 p-4 rounded-lg">
                          <div className="flex items-center text-red-800">
                            <XCircle className="h-4 w-4 mr-2" />
                            <span className="font-medium">分析失败</span>
                          </div>
                          <p className="text-red-600 text-sm mt-1">{analysis.error}</p>
                        </div>
                      )}

                      {analysis.status === 'pending' && (
                        <div className="bg-yellow-50 p-4 rounded-lg">
                          <div className="flex items-center text-yellow-800">
                            <Clock className="h-4 w-4 mr-2 animate-spin" />
                            <span className="font-medium">正在分析中...</span>
                          </div>
                          <p className="text-yellow-600 text-sm mt-1">请稍等，正在调用Google搜索API获取数据</p>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          </div>
        </section>
      )}

      {/* 使用指南区域 */}
      <section className="py-8 px-4 bg-muted/30">
        <div className="container mx-auto">
          <div className="max-w-4xl mx-auto">
            <Card className="bg-white border-2 border-blue-100">
              <CardHeader>
                <CardTitle className="flex items-center text-xl">
                  <Info className="h-5 w-5 mr-2 text-blue-500" />
                  使用指南
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="font-semibold text-foreground mb-2">🎯 分析维度</h4>
                    <ul className="text-sm text-muted-foreground space-y-1">
                      <li>• 标题策略：关键词密度、长度分布、CTR优化</li>
                      <li>• 域名权重：类型分布、权重评估、专业度分析</li>
                      <li>• 技术SEO：URL结构、HTTPS、移动友好性</li>
                      <li>• 内容架构：类型分布、深度层级、多媒体元素</li>
                      <li>• 竞争格局：量化评分、对手分析、机会识别</li>
                      <li>• 差异化机会：内容空白、角度创新、平衡点</li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="font-semibold text-foreground mb-2">📊 导出功能</h4>
                    <ul className="text-sm text-muted-foreground space-y-1">
                      <li>• 每个关键词单独导出MD文件</li>
                      <li>• 高级SEO竞争分析Prompt（量化评分）</li>
                      <li>• 包含9大分析维度和评分系统</li>
                      <li>• 提供可执行方案和风险评估</li>
                      <li>• 优化后的JSON数据（去除冗余字段）</li>
                    </ul>
                  </div>
                </div>

                <Alert className="border-yellow-200 bg-yellow-50">
                  <Search className="h-4 w-4 text-yellow-600" />
                  <AlertDescription className="text-yellow-700">
                    <strong>提示：</strong>
                    工具会调用Google Custom Search API获取真实搜索结果，每个关键词会获取前10个结果的详细信息。
                    需要登录Google账户使用，普通用户每日免费10次。建议使用英文关键词以获得更准确的分析数据。每个关键词会单独导出一个MD文件，包含高级SEO竞争分析Prompt（涵盖量化评分、战略洞察、可执行方案等专业分析框架）和优化后的JSON数据，可直接复制到AI工具中进行专业级SEO分析。
                  </AlertDescription>
                </Alert>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* 使用统一前台底部组件 */}
      <FrontendFooter />
    </div>
  )
}
