<?php
require_once '../config/database.php';

/**
 * 导入日志模型类
 * 处理import_logs表的数据库操作
 */
class ImportLog {
    private $conn;
    private $table_name = "import_logs";

    public function __construct() {
        $database = new Database();
        $this->conn = $database->getConnection();
    }

    /**
     * 获取最近7天的导入记录
     */
    public function getRecentImports($days = 7) {
        $query = "SELECT 
                    filename,
                    filtered_keywords,
                    import_date,
                    created_at
                  FROM " . $this->table_name . " 
                  WHERE import_date >= DATE_SUB(CURDATE(), INTERVAL :days DAY)
                  ORDER BY import_date DESC, created_at DESC
                  LIMIT 10";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':days', $days, PDO::PARAM_INT);
        $stmt->execute();

        $imports = [];
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $imports[] = [
                'id' => uniqid(),
                'type' => 'import',
                'filename' => $row['filename'],
                'filtered_keywords' => (int)$row['filtered_keywords'],
                'import_date' => $row['import_date'],
                'created_at' => $row['created_at'],
                'description' => $this->generateDescription($row['filename'], $row['filtered_keywords'])
            ];
        }

        return $imports;
    }

    /**
     * 生成活动描述
     */
    private function generateDescription($filename, $filteredKeywords) {
        // 简化文件名显示
        $displayName = $filename;
        if (strlen($filename) > 30) {
            $displayName = substr($filename, 0, 27) . '...';
        }
        
        return "导入文件: {$displayName} ({$filteredKeywords} 个关键词)";
    }

    /**
     * 获取导入统计
     */
    public function getImportStats($days = 7) {
        $query = "SELECT 
                    COUNT(*) as total_imports,
                    SUM(filtered_keywords) as total_keywords,
                    COUNT(CASE WHEN import_date = CURDATE() THEN 1 END) as today_imports,
                    SUM(CASE WHEN import_date = CURDATE() THEN filtered_keywords ELSE 0 END) as today_keywords
                  FROM " . $this->table_name . " 
                  WHERE import_date >= DATE_SUB(CURDATE(), INTERVAL :days DAY)";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':days', $days, PDO::PARAM_INT);
        $stmt->execute();

        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        return [
            'total_imports' => (int)$result['total_imports'],
            'total_keywords' => (int)$result['total_keywords'],
            'today_imports' => (int)$result['today_imports'],
            'today_keywords' => (int)$result['today_keywords']
        ];
    }
}
?>
