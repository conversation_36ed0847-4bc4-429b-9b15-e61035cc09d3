'use client'

import React, { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card'
import { Badge } from '../../components/ui/badge'
import { Button } from '../../components/ui/button'
import { Input } from '../../components/ui/input'
import { Alert, AlertDescription } from '../../components/ui/alert'
import { Textarea } from '../../components/ui/textarea'
import {
  Search,
  Globe,
  Copy,
  Download,
  Info,
  Loader2,
  CheckCircle,
  XCircle,
  Eye,
  Code,
  Clock,
  ExternalLink,

} from 'lucide-react'
import FrontendHeader from '../../components/frontend-header'
import FrontendFooter from '../../components/frontend-footer'
import { Toaster, toast } from 'sonner'

interface SpiderResult {
  url: string
  title: string
  description: string
  keywords: string
  statusCode: number
  responseTime: number
  contentLength: number
  htmlContent: string
  headers: Record<string, string>
  seo: {
    ogTitle: string
    ogImage: string
    ogUrl: string
    ogType: string
    twitterCard: string
    twitterTitle: string
    twitterDescription: string
    twitterImage: string
    canonical: string
    robots: string
    viewport: string
    charset: string
  }
  stats: {
    h1Count: number
    h2Count: number
    h3Count: number
    imgCount: number
    linkCount: number
    internalLinks: number
    externalLinks: number
  }
  timestamp: string
}



// HTML格式化函数 - 添加适当的缩进和换行
const formatHtmlWithIndentation = (html: string): string => {
  let formatted = ''
  let indent = 0
  const indentSize = 2 // 使用2个空格缩进

  // 将HTML分割成标签和文本
  const tokens = html.split(/(<[^>]*>)/).filter(token => token.length > 0)

  for (let i = 0; i < tokens.length; i++) {
    const token = tokens[i].trim()
    if (!token) continue

    if (token.startsWith('<')) {
      // 这是一个HTML标签
      if (token.startsWith('</')) {
        // 结束标签 - 减少缩进
        indent = Math.max(0, indent - indentSize)
        formatted += ' '.repeat(indent) + token + '\n'
      } else if (token.endsWith('/>')) {
        // 自闭合标签
        formatted += ' '.repeat(indent) + token + '\n'
      } else {
        // 开始标签
        formatted += ' '.repeat(indent) + token + '\n'
        // 检查是否是不需要缩进的标签
        const tagName = token.match(/<(\w+)/)?.[1]?.toLowerCase()
        if (tagName && !['br', 'hr', 'img', 'input', 'meta', 'link'].includes(tagName)) {
          indent += indentSize
        }
      }
    } else {
      // 这是文本内容
      if (token.length > 0) {
        formatted += ' '.repeat(indent) + token + '\n'
      }
    }
  }

  // 清理多余的空行，但保留一些结构性空行
  return formatted
    .replace(/\n\s*\n\s*\n/g, '\n\n') // 将多个空行减少为最多两个
    .replace(/^\s+|\s+$/g, '') // 移除开头和结尾的空白
}

export default function SpiderPage() {
  const [inputUrl, setInputUrl] = useState('')
  const [isProcessing, setIsProcessing] = useState(false)
  const [spiderResult, setSpiderResult] = useState<SpiderResult | null>(null)
  const [error, setError] = useState('')

  // 验证URL格式
  const isValidUrl = (url: string) => {
    try {
      new URL(url.startsWith('http') ? url : `https://${url}`)
      return true
    } catch {
      return false
    }
  }

  // 格式化URL
  const formatUrl = (url: string) => {
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      return `https://${url}`
    }
    return url
  }

  // 执行蜘蛛抓取
  const handleSpiderCrawl = async () => {
    if (!inputUrl.trim()) {
      toast.error('请输入要抓取的域名或URL')
      return
    }

    const formattedUrl = formatUrl(inputUrl.trim())
    
    if (!isValidUrl(formattedUrl)) {
      toast.error('请输入有效的域名或URL')
      return
    }

    setIsProcessing(true)
    setError('')
    setSpiderResult(null)

    try {
      const response = await fetch('/api/spider/crawl', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ url: formattedUrl })
      })

      const data = await response.json()

      if (data.success) {
        setSpiderResult(data.data)
        toast.success('页面抓取成功！')
      } else {
        setError(data.error || '抓取失败')
        toast.error(data.error || '抓取失败')
      }
    } catch {
      setError('网络错误，请稍后重试')
      toast.error('网络错误，请稍后重试')
    } finally {
      setIsProcessing(false)
    }
  }

  // 复制HTML内容
  const copyHtmlContent = () => {
    if (spiderResult?.htmlContent) {
      navigator.clipboard.writeText(spiderResult.htmlContent)
      toast.success('HTML内容已复制到剪贴板')
    }
  }

  // 下载HTML文件
  const downloadHtml = () => {
    if (spiderResult?.htmlContent) {
      const blob = new Blob([spiderResult.htmlContent], { type: 'text/html' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `spider-${new Date().getTime()}.html`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
      toast.success('HTML文件下载成功')
    }
  }

  return (
    <div className="min-h-screen bg-background">
      {/* 使用统一前台顶部组件 */}
      <FrontendHeader />

      {/* 页面标题区域 */}
      <section className="py-12 px-4 bg-gradient-to-br from-background via-muted/30 to-background">
        <div className="container mx-auto text-center">
          <div className="max-w-3xl mx-auto">
            <div className="flex items-center justify-center mb-6">
              <h1 className="text-4xl font-bold bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
                谷歌蜘蛛模拟器
              </h1>
            </div>
            <p className="text-xl text-muted-foreground mb-8">
              模拟谷歌蜘蛛访问网站，获取页面信息和HTML源码
            </p>
            
            {/* 功能特点 */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
              <div className="flex items-center justify-center space-x-2 p-3 bg-card rounded-lg border">
                <Globe className="h-5 w-5 text-primary" />
                <span className="text-sm font-medium">真实蜘蛛模拟</span>
              </div>
              <div className="flex items-center justify-center space-x-2 p-3 bg-card rounded-lg border">
                <Code className="h-5 w-5 text-success" />
                <span className="text-sm font-medium">完整HTML获取</span>
              </div>
              <div className="flex items-center justify-center space-x-2 p-3 bg-card rounded-lg border">
                <Eye className="h-5 w-5 text-secondary" />
                <span className="text-sm font-medium">SEO信息提取</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 输入区域 */}
      <section className="py-8 px-4">
        <div className="container mx-auto">
          <div className="max-w-4xl mx-auto">
            <Card className="shadow-lg border-border/50">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Search className="h-5 w-5 text-primary" />
                  <span>输入要抓取的网站</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-col sm:flex-row gap-4">
                  <div className="flex-1">
                    <Input
                      type="text"
                      placeholder="输入域名或完整URL，如：catchideas.com 或 https://catchideas.com"
                      value={inputUrl}
                      onChange={(e) => setInputUrl(e.target.value)}
                      onKeyPress={(e) => e.key === 'Enter' && handleSpiderCrawl()}
                      className="h-12 text-lg border-input focus:border-primary focus:ring-primary"
                    />
                  </div>
                  <Button
                    onClick={handleSpiderCrawl}
                    disabled={isProcessing}
                    className="btn-primary-enhanced h-12 px-8 transition-all duration-300 ease-out hover:scale-105 hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100 disabled:hover:shadow-none"
                  >
                    {isProcessing ? (
                      <>
                        <Loader2 className="h-5 w-5 mr-2 animate-spin" />
                        抓取中...
                      </>
                    ) : (
                      <>
                        <Search className="h-5 w-5 mr-2" />
                        开始抓取
                      </>
                    )}
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* 错误提示 */}
      {error && (
        <section className="px-4">
          <div className="container mx-auto">
            <div className="max-w-4xl mx-auto">
              <Alert className="border-destructive/50 bg-destructive/10">
                <XCircle className="h-4 w-4 text-destructive" />
                <AlertDescription className="text-destructive">
                  {error}
                </AlertDescription>
              </Alert>
            </div>
          </div>
        </section>
      )}

      {/* 抓取结果 */}
      {spiderResult && (
        <section className="py-8 px-4">
          <div className="container mx-auto">
            <div className="max-w-6xl mx-auto space-y-6">
              
              {/* 页面信息概览 */}
              <Card className="shadow-lg border-border/50">
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <CheckCircle className="h-5 w-5 text-success" />
                    <span>页面信息</span>
                    <Badge className="bg-success/10 text-success border-success/20">
                      {spiderResult.statusCode}
                    </Badge>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    {/* 基本信息 */}
                    <div className="space-y-4">
                      <h4 className="font-semibold text-primary border-b border-border pb-2">基本信息</h4>
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">页面标题</label>
                        <p className="text-sm font-semibold text-foreground mt-1">
                          {spiderResult.title || '未获取到标题'}
                        </p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">页面描述</label>
                        <p className="text-xs text-foreground mt-1 line-clamp-3">
                          {spiderResult.description || '未获取到描述'}
                        </p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">关键词</label>
                        <p className="text-xs text-foreground mt-1">
                          {spiderResult.keywords || '未获取到关键词'}
                        </p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">字符编码</label>
                        <p className="text-xs text-foreground mt-1">
                          {spiderResult.seo?.charset || '未检测到'}
                        </p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">Robots</label>
                        <p className="text-xs text-foreground mt-1">
                          {spiderResult.seo?.robots || '未设置'}
                        </p>
                      </div>
                    </div>

                    {/* SEO信息 */}
                    <div className="space-y-4">
                      <h4 className="font-semibold text-success border-b border-border pb-2">SEO信息</h4>
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">OG标题</label>
                        <p className="text-xs text-foreground mt-1">
                          {spiderResult.seo?.ogTitle || '未设置'}
                        </p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">OG图片</label>
                        <p className="text-xs text-foreground mt-1 truncate">
                          {spiderResult.seo?.ogImage || '未设置'}
                        </p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">Twitter卡片</label>
                        <p className="text-xs text-foreground mt-1">
                          {spiderResult.seo?.twitterCard || '未设置'}
                        </p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">规范链接</label>
                        <p className="text-xs text-foreground mt-1 truncate">
                          {spiderResult.seo?.canonical || '未设置'}
                        </p>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">视口设置</label>
                        <p className="text-xs text-foreground mt-1">
                          {spiderResult.seo?.viewport || '未设置'}
                        </p>
                      </div>
                    </div>

                    {/* 页面统计 */}
                    <div className="space-y-4">
                      <h4 className="font-semibold text-secondary border-b border-border pb-2">页面统计</h4>
                      <div className="grid grid-cols-2 gap-3">
                        <div className="text-center p-2 bg-muted/30 rounded-lg">
                          <div className="text-lg font-bold text-primary">{spiderResult.stats?.h1Count || 0}</div>
                          <div className="text-xs text-muted-foreground">H1标签</div>
                        </div>
                        <div className="text-center p-2 bg-muted/30 rounded-lg">
                          <div className="text-lg font-bold text-primary">{spiderResult.stats?.h2Count || 0}</div>
                          <div className="text-xs text-muted-foreground">H2标签</div>
                        </div>
                        <div className="text-center p-2 bg-muted/30 rounded-lg">
                          <div className="text-lg font-bold text-success">{spiderResult.stats?.imgCount || 0}</div>
                          <div className="text-xs text-muted-foreground">图片数量</div>
                        </div>
                        <div className="text-center p-2 bg-muted/30 rounded-lg">
                          <div className="text-lg font-bold text-success">{spiderResult.stats?.linkCount || 0}</div>
                          <div className="text-xs text-muted-foreground">总链接</div>
                        </div>
                        <div className="text-center p-2 bg-muted/30 rounded-lg">
                          <div className="text-lg font-bold text-secondary">{spiderResult.stats?.internalLinks || 0}</div>
                          <div className="text-xs text-muted-foreground">内部链接</div>
                        </div>
                        <div className="text-center p-2 bg-muted/30 rounded-lg">
                          <div className="text-lg font-bold text-secondary">{spiderResult.stats?.externalLinks || 0}</div>
                          <div className="text-xs text-muted-foreground">外部链接</div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* 技术指标 */}
                  <div className="mt-6 pt-6 border-t border-border">
                    <h4 className="font-semibold text-foreground mb-4">技术指标</h4>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      <div className="text-center p-3 bg-muted/50 rounded-lg">
                        <Clock className="h-5 w-5 mx-auto mb-1 text-primary" />
                        <div className="text-sm text-muted-foreground">响应时间</div>
                        <div className="font-semibold">{spiderResult.responseTime}ms</div>
                      </div>
                      <div className="text-center p-3 bg-muted/50 rounded-lg">
                        <Code className="h-5 w-5 mx-auto mb-1 text-secondary" />
                        <div className="text-sm text-muted-foreground">内容大小</div>
                        <div className="font-semibold">{(spiderResult.contentLength / 1024).toFixed(1)}KB</div>
                      </div>
                      <div className="text-center p-3 bg-muted/50 rounded-lg">
                        <Globe className="h-5 w-5 mx-auto mb-1 text-success" />
                        <div className="text-sm text-muted-foreground">状态码</div>
                        <div className="font-semibold">{spiderResult.statusCode}</div>
                      </div>
                      <div className="text-center p-3 bg-muted/50 rounded-lg">
                        <Clock className="h-5 w-5 mx-auto mb-1 text-muted-foreground" />
                        <div className="text-sm text-muted-foreground">抓取时间</div>
                        <div className="font-semibold text-xs">{spiderResult.timestamp.split(' ')[1]}</div>
                      </div>
                    </div>
                    <div className="mt-4">
                      <label className="text-sm font-medium text-muted-foreground">访问URL</label>
                      <div className="flex items-center mt-1">
                        <p className="text-sm text-foreground flex-1 truncate">{spiderResult.url}</p>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => window.open(spiderResult.url, '_blank')}
                          className="ml-2 transition-all duration-300 ease-out hover:scale-105 hover:shadow-md hover:border-primary/50 hover:bg-primary/5"
                        >
                          <ExternalLink className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* HTML源码展示 */}
              <Card className="shadow-lg border-border/50">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="flex items-center space-x-2">
                      <Code className="h-5 w-5 text-primary" />
                      <span>HTML源码</span>
                      <Badge className="bg-primary/10 text-primary border-primary/20">
                        {spiderResult?.htmlContent?.length || 0} 字符
                      </Badge>
                    </CardTitle>
                    <div className="flex space-x-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={copyHtmlContent}
                        className="transition-all duration-300 ease-out hover:scale-105 hover:shadow-md hover:border-primary/50 hover:bg-primary/5"
                      >
                        <Copy className="h-4 w-4 mr-1" />
                        复制
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={downloadHtml}
                        className="transition-all duration-300 ease-out hover:scale-105 hover:shadow-md hover:border-secondary/50 hover:bg-secondary/5"
                      >
                        <Download className="h-4 w-4 mr-1" />
                        下载
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="relative">
                    <div className="min-h-[400px] max-h-[800px] overflow-auto bg-gray-900 rounded-lg border border-border/50">
                      <Textarea
                        value={spiderResult?.htmlContent ?
                          formatHtmlWithIndentation(spiderResult.htmlContent)
                          : ''
                        }
                        readOnly
                        className="w-full h-full min-h-[400px] bg-gray-900 text-gray-100 font-mono text-sm border-0 resize-none focus:ring-0 focus:border-0 p-4"
                        style={{
                          whiteSpace: 'pre',
                          wordWrap: 'normal',
                          overflowWrap: 'normal'
                        }}
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>
      )}

      {/* 使用说明 */}
      <section className="py-8 px-4">
        <div className="container mx-auto">
          <div className="max-w-4xl mx-auto">
            <Alert className="border-primary/30 bg-card border-2">
              <Info className="h-4 w-4 text-primary" />
              <AlertDescription className="text-foreground">
                <strong>使用说明：</strong>
                <br />
                • 输入完整URL（如：https://catchideas.com）或域名（如：catchideas.com）
                <br />
                • 模拟谷歌蜘蛛的User-Agent进行访问，获取真实的页面内容
                <br />
                • 自动提取页面标题、描述、关键词等SEO重要信息
                <br />
                • 支持复制HTML源码到剪贴板或下载为文件
                <br />
                • 显示响应时间、内容大小等技术指标
              </AlertDescription>
            </Alert>
          </div>
        </div>
      </section>

      {/* 使用统一前台底部组件 */}
      <FrontendFooter />

      <Toaster position="top-right" richColors closeButton duration={5000} />
    </div>
  )
}
