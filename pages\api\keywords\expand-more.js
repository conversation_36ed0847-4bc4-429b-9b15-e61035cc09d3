// HTML实体解码函数
function decodeHTMLEntities(text) {
  const entities = {
    '&#39;': "'",
    '&quot;': '"',
    '&amp;': '&',
    '&lt;': '<',
    '&gt;': '>',
    '&nbsp;': ' '
  };

  return text.replace(/&#?\w+;/g, (entity) => {
    return entities[entity] || entity;
  });
}

// 简单的关键词过滤函数 - 过滤包含特殊符号的关键词
function shouldFilterKeyword(keyword) {
  if (!keyword || typeof keyword !== 'string') return true;

  const keywordText = keyword.trim();

  // 过滤包含特殊符号的关键词（保留字母、数字、空格、连字符）
  if (/[^\w\s-]/.test(keywordText)) return true;

  return false;
}

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      message: 'Method not allowed'
    })
  }

  try {
    const { keyword_id, suggestions } = req.body

    // 验证参数
    if (!keyword_id || !suggestions || !Array.isArray(suggestions)) {
      return res.status(400).json({
        success: false,
        message: '参数错误'
      })
    }

    // Google搜索建议API列表
    const googleApis = [
      'https://suggestqueries.google.com/complete/search?output=toolbar&hl=en&q=',
      'https://clients1.google.com/complete/search?hl=en&output=toolbar&q='
    ]

    const allNewSuggestions = []
    let processedCount = 0

    // 逐个处理每个建议，避免并发请求过多
    for (const suggestion of suggestions) {
      processedCount++

      let foundSuggestions = false

      // 尝试两个API
      for (let i = 0; i < googleApis.length && !foundSuggestions; i++) {
        try {
          const apiUrl = googleApis[i] + encodeURIComponent(suggestion)
          
          const response = await fetch(apiUrl, {
            headers: {
              'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
              'Accept': 'text/xml,application/xml,application/xhtml+xml,text/html;q=0.9,text/plain;q=0.8,image/png,*/*;q=0.5',
              'Accept-Language': 'en-US,en;q=0.9',
              'Accept-Encoding': 'gzip, deflate, br',
              'DNT': '1',
              'Connection': 'keep-alive',
              'Upgrade-Insecure-Requests': '1'
            }
          })

          if (response.ok) {
            const xmlText = await response.text()

            // 解析XML响应
            const suggestionMatches = xmlText.match(/<suggestion data="([^"]+)"/g)
            if (suggestionMatches && suggestionMatches.length > 0) {
              const newSuggestions = suggestionMatches.map(match => {
                const matchResult = match.match(/data="([^"]+)"/)
                if (matchResult && matchResult[1]) {
                  // 完整的HTML实体解码
                  return decodeHTMLEntities(matchResult[1])
                }
                return ''
              }).filter(s => s && s !== suggestion && s.length > 1 && !shouldFilterKeyword(s))

              allNewSuggestions.push(...newSuggestions)
              foundSuggestions = true
            }
          }
        } catch {
          // 静默处理API调用错误
        }
      }
      
      // 每个词处理后等待1秒，避免请求过快
      if (processedCount < suggestions.length) {
        await new Promise(resolve => setTimeout(resolve, 1000))
      }
    }

    // 去重并限制数量
    const uniqueSuggestions = [...new Set(allNewSuggestions)]
      .filter(s => s && s.length > 1)
      .slice(0, 30) // 增加到30个



    res.status(200).json({
      success: true,
      message: `成功拓展 ${uniqueSuggestions.length} 个相关词`,
      data: uniqueSuggestions,
      stats: {
        processed: processedCount,
        total_suggestions: allNewSuggestions.length,
        unique_suggestions: uniqueSuggestions.length
      }
    })

  } catch {
    res.status(500).json({
      success: false,
      message: '服务器错误，请稍后重试'
    })
  }
}
