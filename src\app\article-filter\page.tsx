'use client'

import { useState, useRef, useMemo } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card'
import { Badge } from '../../components/ui/badge'
import { Button } from '../../components/ui/button'
import { Textarea } from '../../components/ui/textarea'
import { Alert, AlertDescription } from '../../components/ui/alert'
import { 
  Filter, 
  Upload, 
  Download, 
  Trash2, 
  Info, 
  Link,
  FileText,
  CheckCircle,
  XCircle
} from 'lucide-react'
import FrontendHeader from '../../components/frontend-header'
import FrontendFooter from '../../components/frontend-footer'
import { Toaster, toast } from 'sonner'

interface FilterResult {
  validArticles: string[]
  filteredOut: string[]
  filterReasons: Record<string, string[]>
}

export default function ArticleFilterPage() {
  const [inputText, setInputText] = useState('')
  const [customFilters, setCustomFilters] = useState('')
  const [customDomains, setCustomDomains] = useState('')
  const [results, setResults] = useState<FilterResult | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  // 内置过滤规则
  const builtInFilters = [
    // 基础页面类型
    '/category/', '/tag/', '/archive/', '/page/',
    '/author/', '/feed/', '/search/', '/sitemap',
    '/login', '/register', '/signup', '/admin',
    '/cart', '/checkout', '/payment',
    '/ads', '/advertisement',

    // 海外博客常见页面
    '/about/', '/contact/', '/privacy/', '/terms/',
    '/portfolio/', '/projects/', '/resume/', '/cv/',
    '/services/', '/pricing/', '/testimonials/',
    '/gallery/', '/photos/', '/images/',

    // 个人主页和社交页面
    '/profile/', '/user/', '/users/', '/member/',
    '/dashboard/', '/settings/', '/account/',
    '/bookmarks/', '/favorites/', '/saved/',
    '/pins/', '/pin/', '/collections/',
    '/following/', '/followers/', '/friends/',

    // GitHub 和开发相关
    '/commits/', '/commit/', '/pull/', '/issues/',
    '/releases/', '/wiki/', '/graphs/', '/pulse/',
    '/network/', '/forks/', '/stargazers/',
    '/watchers/', '/contributors/', '/blame/',
    '/tree/', '/blob/', '/raw/', '/compare/',

    // 政府和官方网站
    '.gov', '.gov.', '.mil', '.edu',

    // 主流新闻媒体网站
    'bbc.com', 'cnn.com', 'reuters.com', 'ap.org',
    'nytimes.com', 'washingtonpost.com', 'wsj.com',
    'theguardian.com', 'telegraph.co.uk', 'independent.co.uk',
    'foxnews.com', 'nbcnews.com', 'abcnews.go.com',
    'cbsnews.com', 'usatoday.com', 'npr.org',
    'bloomberg.com', 'fortune.com', 'forbes.com',
    'techcrunch.com', 'wired.com', 'theverge.com',
    'engadget.com', 'arstechnica.com', 'zdnet.com',

    // Vercel/Netlify 等部署平台
    '.vercel.app', '.netlify.app', '.herokuapp.com',
    '.github.io', '.gitlab.io', '.surge.sh',
    '.now.sh', '.firebaseapp.com',

    // 静态文件扩展
    '.js', '.css', '.jpg', '.png', '.gif', '.svg', '.ico',
    '.pdf', '.zip', '.rar', '.tar', '.gz',
    '.mp4', '.mp3', '.avi', '.mov', '.wav',
    '.woff', '.woff2', '.ttf', '.eot',
    '.json', '.xml', '.txt', '.csv',

    // URL参数
    '?replytocom=', '?utm_source=', '?session=', '?ref=',
    '?fbclid=', '?gclid=', '?msclkid=', '?dclid=',
    '?mc_cid=', '?mc_eid=', '?_ga=', '?_gl=',
    '?source=', '?medium=', '?campaign=',

    // 论坛和社区
    '/forum/', '/forums/', '/community/', '/discussion/',
    '/topic/', '/thread/', '/post/', '/reply/',
    '/memberlist/', '/viewtopic/', '/viewforum/',

    // 电商相关
    '/shop/', '/store/', '/product/', '/products/',
    '/wishlist/', '/compare/', '/reviews/',
    '/shipping/', '/returns/', '/warranty/',

    // 多媒体和资源
    '/video/', '/videos/', '/audio/', '/podcast/',
    '/download/', '/downloads/', '/resources/',
    '/tools/', '/utilities/', '/apps/',

    // 其他常见非内容页面
    '/404/', '/error/', '/maintenance/',
    '/coming-soon/', '/under-construction/',
    '/newsletter/', '/subscribe/', '/unsubscribe/',
    '/redirect/', '/goto/', '/link/', '/out/',
    '/api/', '/webhook/', '/callback/',
    '/health/', '/status/', '/ping/'
  ]

  // 过滤链接的核心函数
  const filterLinks = (urls: string[], customKeywords: string[] = [], customDomainList: string[] = []) => {
    const allFilters = [...builtInFilters, ...customKeywords]
    const validArticles: string[] = []
    const filteredOut: string[] = []
    const filterReasons: Record<string, string[]> = {}

    urls.forEach(url => {
      let isFiltered = false
      let filterReason = ''

      // 检查自定义域名过滤
      if (!isFiltered && customDomainList.length > 0) {
        try {
          const urlObj = new URL(url)
          const hostname = urlObj.hostname.toLowerCase()

          for (const domain of customDomainList) {
            const cleanDomain = domain.toLowerCase().replace(/^https?:\/\//, '').replace(/^www\./, '')
            const cleanHostname = hostname.replace(/^www\./, '')

            if (cleanHostname === cleanDomain || cleanHostname.endsWith('.' + cleanDomain)) {
              isFiltered = true
              filterReason = `自定义域名过滤: ${domain}`
              break
            }
          }
        } catch (e) {
          // URL解析失败，继续其他检查
        }
      }

      // 检查是否匹配过滤规则
      if (!isFiltered) {
        for (const filter of allFilters) {
          if (url.toLowerCase().includes(filter.toLowerCase())) {
            isFiltered = true
            filterReason = filter
            break
          }
        }
      }

      // 检查是否只是域名或根路径
      if (!isFiltered) {
        try {
          const urlObj = new URL(url)
          const pathname = urlObj.pathname
          if (pathname === '/' || pathname === '') {
            isFiltered = true
            filterReason = '首页/根路径'
          }
        } catch (e) {
          isFiltered = true
          filterReason = '无效URL'
        }
      }

      if (isFiltered) {
        filteredOut.push(url)
        if (!filterReasons[filterReason]) {
          filterReasons[filterReason] = []
        }
        filterReasons[filterReason].push(url)
      } else {
        validArticles.push(url)
      }
    })

    return { validArticles, filteredOut, filterReasons }
  }

  // 处理文本输入
  const processLinks = () => {
    if (!inputText.trim()) {
      toast.error('请输入链接')
      return
    }

    // 解析链接
    const urls = inputText
      .split(/[\n\r]+/)
      .map(url => url.trim())
      .filter(url => url.length > 0)

    if (urls.length === 0) {
      toast.error('未找到有效链接')
      return
    }

    // 解析自定义过滤规则
    const customKeywords = customFilters
      .split(/[\n\r,]+/)
      .map(keyword => keyword.trim())
      .filter(keyword => keyword.length > 0)

    // 解析自定义域名过滤
    const customDomainList = customDomains
      .split(/[\n\r,]+/)
      .map(domain => domain.trim())
      .filter(domain => domain.length > 0)

    // 执行过滤
    const result = filterLinks(urls, customKeywords, customDomainList)
    setResults(result)
    
    toast.success(`处理完成！找到 ${result.validArticles.length} 个有效文章链接`)
  }

  // 处理文件上传
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    if (!file.name.endsWith('.txt')) {
      toast.error('请上传.txt文件')
      return
    }

    const reader = new FileReader()
    reader.onload = (e) => {
      const content = e.target?.result as string
      setInputText(content)
      toast.success('文件上传成功')
    }
    reader.readAsText(file)
  }

  // 导出结果
  const exportResults = (type: 'valid' | 'filtered') => {
    if (!results) return

    const data = type === 'valid' ? results.validArticles : results.filteredOut
    const content = data.join('\n')
    const blob = new Blob([content], { type: 'text/plain' })
    const url = URL.createObjectURL(blob)
    
    const a = document.createElement('a')
    a.href = url
    a.download = `${type === 'valid' ? '有效文章链接' : '已过滤链接'}.txt`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    
    toast.success(`${type === 'valid' ? '有效链接' : '过滤链接'}导出成功`)
  }

  // 清空所有内容
  const clearAll = () => {
    setInputText('')
    setCustomFilters('')
    setCustomDomains('')
    setResults(null)
    toast.success('已清空所有内容')
  }

  // 统计信息
  const stats = useMemo(() => {
    if (!results) return null
    
    const total = results.validArticles.length + results.filteredOut.length
    const passRate = total > 0 ? ((results.validArticles.length / total) * 100).toFixed(1) : '0'
    
    return {
      total,
      valid: results.validArticles.length,
      filtered: results.filteredOut.length,
      passRate
    }
  }, [results])

  return (
    <div className="min-h-screen bg-background">
      <Toaster position="top-right" />
      <FrontendHeader />

      {/* 页面标题区域 */}
      <section className="py-8 px-4 bg-gradient-to-br from-green-50 to-blue-50 border-b">
        <div className="container mx-auto">
          <div className="max-w-4xl mx-auto text-center">
            <div className="flex items-center justify-center mb-4">
              <div className="p-3 bg-gradient-to-r from-green-500 to-blue-600 rounded-xl mr-4">
                <Filter className="h-8 w-8 text-white" />
              </div>
              <h1 className="text-4xl font-bold text-foreground">文章链接过滤工具</h1>
            </div>
            <p className="text-lg text-muted-foreground mb-6">
              快速过滤出文章URL，自动排除分类页、标签页、静态文件等非内容页面
            </p>
            <div className="flex items-center justify-center gap-6 text-sm text-muted-foreground">
              <div className="flex items-center">
                <Link className="h-4 w-4 mr-2 text-green-500" />
                智能过滤
              </div>
              <div className="flex items-center">
                <FileText className="h-4 w-4 mr-2 text-blue-500" />
                文件导入
              </div>
              <div className="flex items-center">
                <Download className="h-4 w-4 mr-2 text-purple-500" />
                结果导出
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 输入区域 */}
      <section className="py-8 px-4">
        <div className="container mx-auto">
          <div className="max-w-6xl mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              
              {/* 左侧：链接输入 */}
              <Card className="shadow-lg border-2 border-green-100">
                <CardHeader>
                  <CardTitle className="flex items-center text-xl">
                    <Link className="h-5 w-5 mr-2 text-green-500" />
                    链接输入
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Textarea
                      placeholder="请输入链接，每行一个：&#10;https://example.com/article-1&#10;https://example.com/category/tech&#10;https://example.com/2023/07/my-post"
                      value={inputText}
                      onChange={(e) => setInputText(e.target.value)}
                      className="min-h-[200px] max-h-[200px] overflow-y-auto border-2 border-green-200 focus:border-green-500 focus:ring-green-500 resize-none"
                      rows={8}
                    />
                  </div>
                  
                  <div className="flex gap-3">
                    <Button
                      onClick={() => fileInputRef.current?.click()}
                      variant="outline"
                      className="border-green-200 text-green-600 hover:bg-green-50 hover:border-green-300"
                    >
                      <Upload className="h-4 w-4 mr-2" />
                      上传TXT文件
                    </Button>
                    <input
                      ref={fileInputRef}
                      type="file"
                      accept=".txt"
                      onChange={handleFileUpload}
                      className="hidden"
                    />
                  </div>
                </CardContent>
              </Card>

              {/* 右侧：自定义过滤规则 */}
              <Card className="shadow-lg border-2 border-blue-100">
                <CardHeader>
                  <CardTitle className="flex items-center text-xl">
                    <Filter className="h-5 w-5 mr-2 text-blue-500" />
                    自定义过滤规则
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Textarea
                      placeholder="输入额外需要过滤的关键词，每行一个：&#10;/special-page/&#10;/promo/&#10;?campaign="
                      value={customFilters}
                      onChange={(e) => setCustomFilters(e.target.value)}
                      className="min-h-[120px] max-h-[120px] overflow-y-auto border-2 border-blue-200 focus:border-blue-500 focus:ring-blue-500 resize-none"
                      rows={5}
                    />
                  </div>
                  
                  <Alert className="border-blue-200 bg-blue-50">
                    <Info className="h-4 w-4 text-blue-500" />
                    <AlertDescription className="text-blue-700">
                      <strong>内置规则：</strong>
                      已自动过滤分类页(/category/)、标签页(/tag/)、静态文件(.js/.css/.jpg等)、登录页(/login)等常见非文章页面
                    </AlertDescription>
                  </Alert>
                </CardContent>
              </Card>

              {/* 第三列：自定义域名过滤 */}
              <Card className="shadow-lg border-2 border-purple-100">
                <CardHeader>
                  <CardTitle className="flex items-center text-xl">
                    <XCircle className="h-5 w-5 mr-2 text-purple-500" />
                    域名过滤
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Textarea
                      placeholder="输入需要完全过滤的域名，每行一个：&#10;matteowholesale.com&#10;www.example.com&#10;spam-site.net"
                      value={customDomains}
                      onChange={(e) => setCustomDomains(e.target.value)}
                      className="min-h-[120px] max-h-[120px] overflow-y-auto border-2 border-purple-200 focus:border-purple-500 focus:ring-purple-500 resize-none"
                      rows={5}
                    />
                  </div>

                  <Alert className="border-purple-200 bg-purple-50">
                    <Info className="h-4 w-4 text-purple-500" />
                    <AlertDescription className="text-purple-700">
                      <strong>域名过滤：</strong>
                      输入域名后，该域名下的所有链接都会被过滤掉。支持带www和不带www的格式。
                    </AlertDescription>
                  </Alert>
                </CardContent>
              </Card>
            </div>

            {/* 操作按钮 */}
            <div className="flex justify-center gap-4 mt-6">
              <Button
                onClick={processLinks}
                className="bg-gradient-to-r from-green-500 to-blue-600 hover:from-green-600 hover:to-blue-700 text-white px-8"
                disabled={!inputText.trim()}
              >
                <Filter className="h-4 w-4 mr-2" />
                开始过滤
              </Button>
              
              {(inputText || customFilters || customDomains || results) && (
                <Button
                  onClick={clearAll}
                  variant="outline"
                  className="border-red-200 text-red-600 hover:bg-red-50 hover:border-red-300"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  清空所有
                </Button>
              )}
            </div>
          </div>
        </div>
      </section>

      {/* 统计信息区域 */}
      {stats && (
        <section className="py-6 px-4 bg-muted/30">
          <div className="container mx-auto">
            <div className="max-w-4xl mx-auto">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <Card className="text-center">
                  <CardContent className="p-4">
                    <div className="text-2xl font-bold text-blue-600">{stats.total}</div>
                    <div className="text-sm text-muted-foreground">输入总数</div>
                  </CardContent>
                </Card>
                <Card className="text-center">
                  <CardContent className="p-4">
                    <div className="text-2xl font-bold text-green-600">{stats.valid}</div>
                    <div className="text-sm text-muted-foreground">有效文章</div>
                  </CardContent>
                </Card>
                <Card className="text-center">
                  <CardContent className="p-4">
                    <div className="text-2xl font-bold text-red-600">{stats.filtered}</div>
                    <div className="text-sm text-muted-foreground">已过滤</div>
                  </CardContent>
                </Card>
                <Card className="text-center">
                  <CardContent className="p-4">
                    <div className="text-2xl font-bold text-purple-600">{stats.passRate}%</div>
                    <div className="text-sm text-muted-foreground">通过率</div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </section>
      )}

      {/* 结果展示区域 */}
      {results && (
        <section className="py-8 px-4">
          <div className="container mx-auto">
            <div className="max-w-6xl mx-auto">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">

                {/* 有效文章链接 */}
                <Card className="shadow-lg border-2 border-green-100">
                  <CardHeader className="pb-3">
                    <CardTitle className="flex items-center justify-between text-xl">
                      <span className="flex items-center">
                        <CheckCircle className="h-5 w-5 mr-2 text-green-500" />
                        有效文章链接
                      </span>
                      <Badge variant="secondary" className="text-sm">
                        {results.validArticles.length} 个
                      </Badge>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="max-h-[300px] overflow-y-auto space-y-2 border rounded-lg p-3 bg-green-50/30">
                      {results.validArticles.length > 0 ? (
                        results.validArticles.map((url, index) => (
                          <div key={index} className="text-sm p-2 bg-white rounded border border-green-200 break-all">
                            {url}
                          </div>
                        ))
                      ) : (
                        <div className="text-center text-muted-foreground py-4">
                          没有找到有效的文章链接
                        </div>
                      )}
                    </div>

                    {results.validArticles.length > 0 && (
                      <Button
                        onClick={() => exportResults('valid')}
                        className="w-full bg-green-500 hover:bg-green-600 text-white"
                      >
                        <Download className="h-4 w-4 mr-2" />
                        导出有效链接
                      </Button>
                    )}
                  </CardContent>
                </Card>

                {/* 已过滤链接 */}
                <Card className="shadow-lg border-2 border-red-100">
                  <CardHeader className="pb-3">
                    <CardTitle className="flex items-center justify-between text-xl">
                      <span className="flex items-center">
                        <XCircle className="h-5 w-5 mr-2 text-red-500" />
                        已过滤链接
                      </span>
                      <Badge variant="secondary" className="text-sm">
                        {results.filteredOut.length} 个
                      </Badge>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="max-h-[300px] overflow-y-auto space-y-2 border rounded-lg p-3 bg-red-50/30">
                      {results.filteredOut.length > 0 ? (
                        Object.entries(results.filterReasons).map(([reason, urls]) => (
                          <div key={reason} className="space-y-1">
                            <div className="text-xs font-semibold text-red-600 bg-red-100 px-2 py-1 rounded">
                              {reason} ({urls.length}个)
                            </div>
                            {urls.slice(0, 3).map((url, index) => (
                              <div key={index} className="text-xs p-2 bg-white rounded border border-red-200 break-all ml-2">
                                {url}
                              </div>
                            ))}
                            {urls.length > 3 && (
                              <div className="text-xs text-muted-foreground ml-2">
                                ...还有 {urls.length - 3} 个
                              </div>
                            )}
                          </div>
                        ))
                      ) : (
                        <div className="text-center text-muted-foreground py-4">
                          没有被过滤的链接
                        </div>
                      )}
                    </div>

                    {results.filteredOut.length > 0 && (
                      <Button
                        onClick={() => exportResults('filtered')}
                        variant="outline"
                        className="w-full border-red-200 text-red-600 hover:bg-red-50"
                      >
                        <Download className="h-4 w-4 mr-2" />
                        导出过滤链接
                      </Button>
                    )}
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </section>
      )}

      {/* 使用指南区域 */}
      <section className="py-8 px-4 bg-muted/30">
        <div className="container mx-auto">
          <div className="max-w-4xl mx-auto">
            <Card className="border-2 border-blue-100">
              <CardHeader>
                <CardTitle className="flex items-center text-xl">
                  <Info className="h-5 w-5 mr-2 text-blue-500" />
                  使用指南
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div>
                    <h4 className="font-semibold text-foreground mb-2">🎯 内置过滤规则</h4>
                    <ul className="text-sm text-muted-foreground space-y-1">
                      <li>• 分类页面：/category/, /tag/, /archive/</li>
                      <li>• 功能页面：/login, /search/, /feed/</li>
                      <li>• 个人页面：/about/, /contact/, /profile/</li>
                      <li>• 开发平台：GitHub, Vercel, Netlify</li>
                      <li>• 政府网站：.gov, .mil, .edu</li>
                      <li>• 静态文件：.js, .css, .jpg, .png等</li>
                      <li>• 首页根路径：域名后无具体路径</li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="font-semibold text-foreground mb-2">⚙️ 自定义规则</h4>
                    <ul className="text-sm text-muted-foreground space-y-1">
                      <li>• 每行输入一个过滤关键词</li>
                      <li>• 支持路径匹配：/special-page/</li>
                      <li>• 支持参数匹配：?campaign=</li>
                      <li>• 大小写不敏感匹配</li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="font-semibold text-foreground mb-2">🚫 域名过滤</h4>
                    <ul className="text-sm text-muted-foreground space-y-1">
                      <li>• 每行输入一个域名</li>
                      <li>• 支持：example.com</li>
                      <li>• 支持：www.example.com</li>
                      <li>• 过滤该域名下所有链接</li>
                    </ul>
                  </div>
                </div>

                <Alert className="border-yellow-200 bg-yellow-50">
                  <Info className="h-4 w-4 text-yellow-600" />
                  <AlertDescription className="text-yellow-700">
                    <strong>提示：</strong>
                    工具会自动识别并过滤掉非文章页面，保留真正的内容页面。
                    域名过滤功能可以快速排除整个网站的所有链接，适合批量处理。
                    建议先使用默认规则测试，再根据需要添加自定义过滤条件。
                  </AlertDescription>
                </Alert>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      <FrontendFooter />
    </div>
  )
}
