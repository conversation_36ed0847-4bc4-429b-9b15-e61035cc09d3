export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ 
      success: false, 
      message: 'Method not allowed' 
    })
  }

  try {
    const { id } = req.query

    if (!id) {
      return res.status(400).json({
        success: false,
        message: '缺少关键词ID'
      })
    }

    const apiBaseUrl = process.env.NEXT_PUBLIC_PHP_API_URL || 'https://api.xstty.com/api'
    
    // 调用PHP API获取关键词详情
    const response = await fetch(`${apiBaseUrl}/keywords.php?id=${id}`)
    const data = await response.json()

    if (data.success) {
      res.status(200).json({
        success: true,
        data: data.data,
        message: '获取关键词详情成功'
      })
    } else {
      res.status(404).json({
        success: false,
        message: data.message || '关键词不存在'
      })
    }

  } catch (error) {
    console.error('Get keyword detail API error:', error)
    res.status(500).json({
      success: false,
      message: '服务器内部错误: ' + error.message
    })
  }
}
