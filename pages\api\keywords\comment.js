export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      message: 'Method not allowed'
    })
  }

  try {
    const { id, comment } = req.body

    // 验证参数
    if (!id || !comment) {
      return res.status(400).json({
        success: false,
        message: '关键词ID和评价不能为空'
      })
    }

    // 验证评价值
    if (comment !== '有效' && comment !== '无效') {
      return res.status(400).json({
        success: false,
        message: '评价只能是"有效"或"无效"'
      })
    }

    const apiBaseUrl = process.env.NEXT_PUBLIC_PHP_API_URL || 'https://api.xstty.com/api'

    // 调用PHP API更新评价，ID通过URL参数传递
    const response = await fetch(`${apiBaseUrl}/keywords.php?id=${encodeURIComponent(id)}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        user_comment: comment
      })
    })

    const data = await response.json()

    if (data.success) {
      res.status(200).json({
        success: true,
        message: '评价提交成功',
        data: data.data
      })
    } else {
      res.status(400).json({
        success: false,
        message: data.error || '评价提交失败'
      })
    }

  } catch (error) {
    console.error('提交评价失败:', error)
    res.status(500).json({
      success: false,
      message: '服务器错误，请稍后重试'
    })
  }
}
