import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const { keyword, count = 1 } = await request.json()

    if (!keyword || typeof keyword !== 'string') {
      return NextResponse.json({
        success: false,
        error: '缺少关键词参数'
      }, { status: 400 })
    }

    // 验证关键词长度和内容
    if (keyword.length > 100) {
      return NextResponse.json({
        success: false,
        error: '关键词长度不能超过100个字符'
      }, { status: 400 })
    }

    // 清理关键词，移除特殊字符
    const cleanKeyword = keyword.replace(/[^\w\s\u4e00-\u9fff]/g, '').trim()

    if (count < 1 || count > 4) {
      return NextResponse.json({
        success: false,
        error: '生成数量必须在1-4之间'
      }, { status: 400 })
    }

    // 硅基流动API配置 (动态生成10个)
    const apiKeys = [];
    for (let i = 1; i <= 10; i++) {
      const apiKey = process.env[`SILICONFLOW_API_KEY_${i}`];
      if (apiKey) {
        apiKeys.push(apiKey);
      }
    }

    if (apiKeys.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'API密钥未配置'
      }, { status: 500 })
    }

    // 随机选择一个API密钥
    const apiKey = apiKeys[Math.floor(Math.random() * apiKeys.length)]

    // 步骤1：生成简洁的Logo设计Prompt
    const promptGenerationPrompt = `你是专业的Logo设计助手。基于关键词"${cleanKeyword}"，生成一个简洁的英文设计Prompt。

要求：
1. 理解关键词的核心含义和应用场景
2. 设计元素要简洁、现代、易识别
3. 适合扁平化设计风格
4. Prompt长度控制在50词以内

必须严格按照以下JSON格式返回，不要添加任何其他内容：
{"prompt":"A clean, minimalist logo design for ${cleanKeyword}, [具体设计描述], flat design, transparent background, centered icon"}

示例格式：
{"prompt":"A clean, minimalist logo design for education, featuring a simple book icon with graduation cap, modern typography, flat design, transparent background, centered icon"}`

    const promptResponse = await fetch(process.env.SILICONFLOW_API_URL || 'https://api.siliconflow.cn/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model: process.env.SILICONFLOW_LOGO_TEXT_MODEL || 'Qwen/Qwen2.5-7B-Instruct',
        messages: [
          {
            role: 'system',
            content: '你是专业的Logo设计助手。请严格按照要求的JSON格式返回，不要添加任何解释或其他内容。'
          },
          {
            role: 'user',
            content: promptGenerationPrompt
          }
        ],
        temperature: 0.3, // 降低随机性，确保格式一致
        max_tokens: 150, // 减少token数量，避免过长输出
        stream: false,
        enable_thinking: false  // 禁用思考模式，确保快速响应
      })
    })

    if (!promptResponse.ok) {
      return NextResponse.json({
        success: false,
        error: `生成Prompt失败: ${promptResponse.status}`
      }, { status: promptResponse.status })
    }

    const promptData = await promptResponse.json()

    if (!promptData.choices || !promptData.choices[0] || !promptData.choices[0].message) {
      return NextResponse.json({
        success: false,
        error: 'Prompt生成响应格式错误'
      }, { status: 500 })
    }

    const promptContent = promptData.choices[0].message.content.trim()

    // 改进的JSON解析逻辑，增加容错处理
    let logoPrompt = ''

    // 尝试多种解析方法
    const parseAttempts = [
      // 方法1：标准JSON解析
      () => {
        const cleanContent = promptContent.replace(/```json|```/g, '').trim()
        const parsed = JSON.parse(cleanContent)
        return parsed.prompt
      },
      // 方法2：正则匹配JSON
      () => {
        const jsonMatch = promptContent.match(/\{[\s\S]*?\}/)
        if (jsonMatch) {
          const parsed = JSON.parse(jsonMatch[0])
          return parsed.prompt
        }
        throw new Error('No JSON found')
      },
      // 方法3：提取prompt字段值
      () => {
        const promptMatch = promptContent.match(/"prompt"\s*:\s*"([^"]*)"/)
        if (promptMatch) {
          return promptMatch[1]
        }
        throw new Error('No prompt field found')
      },
      // 方法4：生成fallback prompt
      () => {
        return `A clean, minimalist logo design for ${cleanKeyword}, modern and professional style, flat design, transparent background, centered icon`
      }
    ]

    // 依次尝试解析方法
    for (const attempt of parseAttempts) {
      try {
        const result = attempt()
        if (result && typeof result === 'string' && result.length > 10 && result.length < 500) {
          logoPrompt = result
          break
        }
      } catch (error) {
        continue
      }
    }

    // 如果所有方法都失败，使用默认prompt
    if (!logoPrompt) {
      logoPrompt = `A clean, minimalist logo design for ${cleanKeyword}, modern and professional style, flat design, transparent background, centered icon`
    }

    // 步骤2: 生成Logo图像
    const imageResponse = await fetch(process.env.SILICONFLOW_IMAGE_API_URL || 'https://api.siliconflow.cn/v1/images/generations', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model: process.env.SILICONFLOW_LOGO_IMAGE_MODEL || 'Kwai-Kolors/Kolors',
        prompt: logoPrompt,
        image_size: '512x512',
        batch_size: count,
        num_inference_steps: 20,
        guidance_scale: 7.5
      })
    })

    if (!imageResponse.ok) {
      return NextResponse.json({
        success: false,
        error: `生成图像失败: ${imageResponse.status}`
      }, { status: imageResponse.status })
    }

    const imageData = await imageResponse.json()

    if (!imageData.images || imageData.images.length === 0) {
      return NextResponse.json({
        success: false,
        error: '图像生成响应格式错误'
      }, { status: 500 })
    }

    const images = imageData.images.map((img: any) => img.url)

    return NextResponse.json({
      success: true,
      data: {
        keyword,
        prompt: logoPrompt,
        images: images,
        count: images.length,
        seed: imageData.seed || null,
        timings: imageData.timings || null
      }
    })

  } catch (error) {
    return NextResponse.json({
      success: false,
      error: '服务器内部错误'
    }, { status: 500 })
  }
}
