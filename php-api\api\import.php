<?php
require_once '../utils/Response.php';
require_once '../models/Keyword.php';

/**
 * 关键词导入API
 * 处理批量关键词导入
 */

// 处理CORS预检请求
Response::handleOptions();

// 验证请求方法
$method = Response::validateMethod(['POST']);

try {
    // 获取请求数据
    $data = Response::getRequestData();

    // 验证必需字段
    if (!isset($data['keywords']) || !is_array($data['keywords'])) {
        Response::validationError(['keywords' => '关键词数组不能为空']);
    }

    if (empty($data['keywords'])) {
        Response::validationError(['keywords' => '至少需要一个关键词']);
    }
    
    // 直接处理关键词，不再进行预过滤（已由AI处理）
    $validKeywords = [];
    $errors = [];
    $seenKeywords = []; // 本次导入中的去重

    foreach ($data['keywords'] as $index => $keywordData) {
        // 验证关键词格式
        if (!isset($keywordData['keyword']) || empty(trim($keywordData['keyword']))) {
            $errors[] = "第" . ($index + 1) . "个关键词不能为空";
            continue;
        }

        $keyword = trim($keywordData['keyword']);

        // 简单的本批次去重（保持数据一致性）
        $normalizedKeyword = strtolower(trim(preg_replace('/\s+/', ' ', $keyword)));

        if (isset($seenKeywords[$normalizedKeyword])) {
            continue; // 跳过重复，不记录为错误
        }

        // 记录已处理的关键词
        $seenKeywords[$normalizedKeyword] = true;

        $validKeywords[] = [
            'original' => $keyword,
            'normalized' => $normalizedKeyword,
            'data' => $keywordData
        ];
    }

    // 第二步：批量检查数据库中的重复项
    $keywordModel = new Keyword();
    $duplicates = [];
    $processedKeywords = [];

    if (!empty($validKeywords)) {
        // 提取所有标准化的关键词进行批量检查
        $normalizedKeywords = array_column($validKeywords, 'normalized');
        $existingKeywords = $keywordModel->batchExists($normalizedKeywords);
        $existingKeywordsSet = array_flip($existingKeywords);

        // 过滤掉已存在的关键词
        foreach ($validKeywords as $item) {
            if (isset($existingKeywordsSet[$item['normalized']])) {
                $duplicates[] = [
                    'keyword' => $item['original'],
                    'reason' => '数据库中已存在'
                ];
            } else {
                // 生成slug格式的ID
                $id = generateSlugId($item['original']);

                // 只传递基本字段，其他字段保持NULL由其他功能页面处理
                $processedKeywords[] = [
                    'id' => $id,
                    'keyword' => $item['normalized'],
                    'source' => $item['data']['source'] ?? 'google_trends',
                    'import_date' => $item['data']['import_date'] ?? date('Y-m-d')
                ];
            }
        }
    }
    
    // 如果有验证错误，返回错误信息
    if (!empty($errors)) {
        Response::validationError($errors);
    }

    // 如果没有有效的关键词，返回详细信息
    if (empty($processedKeywords)) {
        Response::error('没有有效的关键词可以导入', 400, [
            'total_submitted' => count($data['keywords']),
            'duplicates' => count($duplicates),
            'validation_errors' => count($errors),
            'details' => [
                'duplicates' => $duplicates,
                'errors' => $errors
            ]
        ]);
    }

    // 批量插入关键词
    $insertResult = $keywordModel->batchCreate($processedKeywords);

    if ($insertResult === false) {
        Response::serverError('关键词导入失败');
    }

    // 获取实际插入和跳过的数量
    $actualInserted = $insertResult['inserted'];
    $actualSkipped = $insertResult['skipped'];
    
    // 记录导入日志
    $totalRejected = count($errors) + count($duplicates);

    // 获取AI过滤统计信息
    $aiFilterStats = $data['ai_filter_stats'] ?? null;

    // 构建拒绝原因统计
    $rejectionReasons = [
        'validation_errors' => count($errors),
        'duplicates' => count($duplicates)
    ];

    // 如果有AI过滤统计，添加详细分类
    if ($aiFilterStats && isset($aiFilterStats['rejectionReasons'])) {
        $rejectionReasons = array_merge($rejectionReasons, [
            'ai_brands' => $aiFilterStats['rejectionReasons']['brand'] ?? 0,
            'ai_generic_words' => $aiFilterStats['rejectionReasons']['generic'] ?? 0,
            'ai_person_names' => $aiFilterStats['rejectionReasons']['person_name'] ?? 0,
            'ai_non_english' => $aiFilterStats['rejectionReasons']['non_english'] ?? 0,
            'ai_meaningless' => $aiFilterStats['rejectionReasons']['meaningless'] ?? 0,
            'ai_too_competitive' => $aiFilterStats['rejectionReasons']['too_competitive'] ?? 0,
            'ai_no_seo_value' => $aiFilterStats['rejectionReasons']['no_seo_value'] ?? 0,
            'ai_service_error' => $aiFilterStats['rejectionReasons']['ai_service_error'] ?? 0,
            'ai_other' => $aiFilterStats['rejectionReasons']['other'] ?? 0
        ]);
    }

    $importLog = [
        'filename' => $data['filename'] ?? 'api_import',
        'total_lines' => $aiFilterStats['total'] ?? count($data['keywords']),
        'raw_keywords' => $aiFilterStats['total'] ?? count($data['keywords']),
        'filtered_keywords' => count($processedKeywords),
        'rejected_keywords' => $aiFilterStats ? ($aiFilterStats['rejected'] + $totalRejected) : $totalRejected,
        'pass_rate' => $aiFilterStats && $aiFilterStats['total'] > 0 ?
            round((count($processedKeywords) / $aiFilterStats['total']) * 100, 2) :
            (count($data['keywords']) > 0 ? round((count($processedKeywords) / count($data['keywords'])) * 100, 2) : 0),
        'rejection_reasons' => json_encode($rejectionReasons),
        'import_date' => date('Y-m-d')
    ];

    // 记录导入日志到数据库
    logImport($importLog);

    // 返回成功响应
    $totalProcessed = $aiFilterStats['total'] ?? count($data['keywords']);
    $totalAiRejected = $aiFilterStats['rejected'] ?? 0;
    $totalAllRejected = $totalAiRejected + $totalRejected;

    Response::success([
        'import' => [
            'total_submitted' => $totalProcessed,
            'successfully_imported' => $actualInserted,
            'database_skipped' => $actualSkipped,
            'duplicates' => count($duplicates),
            'validation_errors' => count($errors),
            'ai_rejected' => $totalAiRejected,
            'pass_rate' => $importLog['pass_rate'] . '%',
            'import_date' => date('Y-m-d'),
            'import_time' => date('Y-m-d H:i:s')
        ],
        'details' => [
            'duplicates' => array_slice($duplicates, 0, 10), // 只返回前10个示例
            'errors' => $errors
        ],
        'summary' => [
            'total_processed' => $totalProcessed,
            'accepted' => count($processedKeywords),
            'rejected' => $totalAllRejected,
            'rejection_breakdown' => array_merge([
                'already_exists' => count($duplicates),
                'validation_failed' => count($errors)
            ], $aiFilterStats ? [
                'ai_filtered' => $totalAiRejected
            ] : [])
        ]
    ], $actualInserted > 0 ?
        "成功导入 {$actualInserted} 个关键词，跳过 {$actualSkipped} 个重复，AI过滤 {$totalAiRejected} 个，拒绝 {$totalRejected} 个" :
        "没有新的关键词被导入");
    
} catch (Exception $e) {
    Response::serverError('导入过程中发生错误: ' . $e->getMessage());
}

/**
 * 生成SEO友好的slug ID
 */
function generateSlugId($keyword) {
    // 基础slug生成
    $slug = strtolower(trim($keyword));

    // 移除特殊字符，只保留字母、数字、空格、连字符
    $slug = preg_replace('/[^\w\s\-]/u', '', $slug);

    // 将多个空格替换为单个连字符
    $slug = preg_replace('/\s+/', '-', $slug);

    // 移除多个连续的连字符
    $slug = preg_replace('/\-+/', '-', $slug);

    // 移除开头和结尾的连字符
    $slug = trim($slug, '-');

    // 如果slug为空或太短，使用fallback
    if (empty($slug) || strlen($slug) < 2) {
        $slug = 'keyword-' . time() . '-' . mt_rand(100, 999);
    }

    // 限制长度
    if (strlen($slug) > 50) {
        $slug = substr($slug, 0, 50);
        $slug = rtrim($slug, '-');
    }

    return $slug;
}

/**
 * 记录导入日志
 */
function logImport($logData) {
    try {
        require_once '../config/database.php';
        $database = new Database();
        $conn = $database->getConnection();
        
        $query = "INSERT INTO import_logs 
                  (filename, total_lines, raw_keywords, filtered_keywords, 
                   rejected_keywords, pass_rate, rejection_reasons, import_date) 
                  VALUES 
                  (:filename, :total_lines, :raw_keywords, :filtered_keywords, 
                   :rejected_keywords, :pass_rate, :rejection_reasons, :import_date)";
        
        $stmt = $conn->prepare($query);
        $stmt->execute([
            ':filename' => $logData['filename'],
            ':total_lines' => $logData['total_lines'],
            ':raw_keywords' => $logData['raw_keywords'],
            ':filtered_keywords' => $logData['filtered_keywords'],
            ':rejected_keywords' => $logData['rejected_keywords'],
            ':pass_rate' => $logData['pass_rate'],
            ':rejection_reasons' => $logData['rejection_reasons'],
            ':import_date' => $logData['import_date']
        ]);
    } catch (Exception $e) {
        // 静默处理日志错误
    }
}
?>
