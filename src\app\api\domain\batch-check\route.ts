import { NextRequest, NextResponse } from 'next/server'
import { promises as dns } from 'dns'

// 单个域名DNS查询函数
async function checkSingleDomain(domain: string) {
  try {
    // 验证域名格式（只允许.com域名）
    if (!domain.endsWith('.com')) {
      return {
        registered: null,
        status: 'invalid_format',
        message: '只支持.com域名'
      }
    }

    // 简单的域名格式验证
    const domainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]*[a-zA-Z0-9]*\.com$/
    if (!domainRegex.test(domain)) {
      return {
        registered: null,
        status: 'invalid_format',
        message: '域名格式无效'
      }
    }

    try {
      // 尝试查询A记录
      await dns.resolve(domain, 'A')
      return {
        registered: true,
        status: 'has_records',
        message: '域名已注册'
      }
    } catch (dnsError: any) {
      if (dnsError.code === 'ENOTFOUND') {
        // 域名未找到，可能未注册
        return {
          registered: false,
          status: 'no_records',
          message: '域名可能可用'
        }
      } else if (dnsError.code === 'ENODATA') {
        // 域名存在但没有A记录，尝试其他记录类型
        try {
          // 尝试查询CNAME记录
          await dns.resolve(domain, 'CNAME')
          return {
            registered: true,
            status: 'has_cname',
            message: '域名已注册'
          }
        } catch {
          // 尝试查询MX记录
          try {
            await dns.resolve(domain, 'MX')
            return {
              registered: true,
              status: 'has_mx',
              message: '域名已注册'
            }
          } catch {
            return {
              registered: false,
              status: 'no_records',
              message: '域名可能可用'
            }
          }
        }
      } else if (dnsError.code === 'ETIMEOUT') {
        return {
          registered: null,
          status: 'timeout',
          message: '查询超时'
        }
      } else {
        return {
          registered: null,
          status: 'query_failed',
          message: '查询失败'
        }
      }
    }
  } catch (error) {
    return {
      registered: null,
      status: 'error',
      message: '查询出错'
    }
  }
}

export async function POST(request: NextRequest) {
  try {
    const { domains } = await request.json()

    if (!domains || !Array.isArray(domains)) {
      return NextResponse.json({
        success: false,
        error: '缺少域名列表参数'
      }, { status: 400 })
    }

    if (domains.length === 0) {
      return NextResponse.json({
        success: false,
        error: '域名列表不能为空'
      }, { status: 400 })
    }

    if (domains.length > 50) {
      return NextResponse.json({
        success: false,
        error: '域名数量不能超过50个'
      }, { status: 400 })
    }

    // 批量检查域名
    const results = await Promise.allSettled(
      domains.map(async (domain: string) => {
        const result = await checkSingleDomain(domain)
        return {
          domain,
          ...result
        }
      })
    )

    // 处理结果
    const processedResults = results.map((result, index) => {
      if (result.status === 'fulfilled') {
        return result.value
      } else {
        return {
          domain: domains[index],
          registered: null,
          status: 'error',
          message: '查询失败'
        }
      }
    })

    // 统计结果
    const stats = {
      total: processedResults.length,
      available: processedResults.filter(r => r.registered === false).length,
      registered: processedResults.filter(r => r.registered === true).length,
      failed: processedResults.filter(r => r.registered === null).length
    }

    return NextResponse.json({
      success: true,
      data: {
        results: processedResults,
        stats
      }
    })

  } catch (error) {
    return NextResponse.json({
      success: false,
      error: '服务器内部错误'
    }, { status: 500 })
  }
}
