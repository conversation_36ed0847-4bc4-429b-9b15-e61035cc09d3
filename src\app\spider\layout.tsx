import type { Metadata } from "next";

export const metadata: Metadata = {
  title: "谷歌蜘蛛模拟器 | CatchIdeas",
  description: "专业的谷歌蜘蛛模拟工具，真实模拟Google爬虫访问网站，获取完整HTML源码、SEO信息、页面标题描述等数据。支持网站SEO分析和技术诊断。",
  authors: [{ name: "CatchIdeas团队" }],
  creator: "CatchIdeas",
  publisher: "CatchIdeas",
  robots: "index, follow",
  keywords: "谷歌蜘蛛模拟器,Google爬虫,网站抓取,SEO分析,HTML源码,页面诊断,网站检测",
  openGraph: {
    type: "website",
    locale: "zh_CN",
    url: "https://catchideas.com/spider",
    title: "谷歌蜘蛛模拟器 | CatchIdeas",
    description: "专业的谷歌蜘蛛模拟工具，真实模拟Google爬虫访问网站，获取完整HTML源码、SEO信息、页面标题描述等数据。支持网站SEO分析和技术诊断。",
    siteName: "CatchIdeas",
  },
  twitter: {
    card: "summary_large_image",
    title: "谷歌蜘蛛模拟器 | CatchIdeas",
    description: "专业的谷歌蜘蛛模拟工具，真实模拟Google爬虫访问网站，获取完整HTML源码、SEO信息、页面标题描述等数据。支持网站SEO分析和技术诊断。",
  },
  alternates: {
    canonical: "https://catchideas.com/spider",
  },
};

export default function SpiderLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return children
}
