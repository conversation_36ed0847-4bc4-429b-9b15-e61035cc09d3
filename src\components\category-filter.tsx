'use client'

import { useState, useEffect } from 'react'
import { Badge } from './ui/badge'
import { Button } from './ui/button'

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select'
import { Filter, Target, Calendar } from 'lucide-react'

interface Category {
  id: string
  name: string
  english_name: string
  keyword_count: number
}

interface CategoryFilterProps {
  onFilterChange: (filters: {
    search: string
    category: string
    analyzed_only: string
    competition_level: string
    date_range: string
    sort_by: string
    sort_order: string
  }) => void
  statistics?: {
    total: number
    analyzed: number
    unanalyzed: number
    categories?: Record<string, number>
  }
  loading?: boolean
}

export default function CategoryFilter({ onFilterChange, statistics, loading }: CategoryFilterProps) {


  const [filters, setFilters] = useState({
    search: '',
    category: '',
    analyzed_only: '',
    competition_level: '',
    date_range: '',
    sort_by: 'created_at',
    sort_order: 'desc'
  })

  const [searchInput, setSearchInput] = useState('')
  const [categories, setCategories] = useState<Category[]>([])
  const [categoriesLoading, setCategoriesLoading] = useState(true)

  // 获取分类数据
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const timestamp = Date.now()
        const response = await fetch(`/api/categories/list?t=${timestamp}`, {
          cache: 'no-store',
          headers: {
            'Cache-Control': 'no-cache'
          }
        })
        const data = await response.json()

        if (data.success && data.data.categories) {
          setCategories(data.data.categories)
        }
      } catch (error) {
        console.error('获取分类失败:', error)
      } finally {
        setCategoriesLoading(false)
      }
    }

    fetchCategories()
  }, [])

  // 当筛选条件改变时通知父组件
  useEffect(() => {
    onFilterChange(filters)
  }, [filters, onFilterChange])

  // 处理搜索
  const handleSearch = () => {
    setFilters(prev => ({ ...prev, search: searchInput.trim() }))
  }



  // 处理分类变化，将"all"转换为空字符串，非全部时只显示已分析
  const handleCategoryChange = (value: string) => {
    setFilters(prev => ({
      ...prev,
      category: value === 'all' ? '' : value,
      analyzed_only: value === 'all' ? prev.analyzed_only : 'true'
    }))
  }

  // 处理竞争难度变化，将"all"转换为空字符串，非全部时只显示已分析
  const handleCompetitionLevelChange = (value: string) => {
    setFilters(prev => ({
      ...prev,
      competition_level: value === 'all' ? '' : value,
      analyzed_only: value === 'all' ? prev.analyzed_only : 'true'
    }))
  }

  // 处理日期筛选变化
  const handleDateRangeChange = (value: string) => {
    setFilters(prev => ({
      ...prev,
      date_range: value === 'all' ? '' : value
    }))
  }

  // 快速筛选按钮
  const quickFilters = [
    {
      label: '全部',
      count: statistics?.total || 0,
      active: !filters.analyzed_only && !filters.competition_level && !filters.category && !filters.search,
      onClick: () => {
        setSearchInput('')
        setFilters(prev => ({
          ...prev,
          analyzed_only: '',
          competition_level: '',
          category: '',
          search: ''
        }))
      }
    },
    {
      label: '已分析',
      count: statistics?.analyzed || 0,
      active: filters.analyzed_only === 'true',
      onClick: () => setFilters(prev => ({
        ...prev,
        analyzed_only: 'true',
        // 如果有搜索，清除分类和竞争难度筛选，避免混合筛选
        category: filters.search ? '' : prev.category,
        competition_level: filters.search ? '' : prev.competition_level
      }))
    },
    {
      label: '待分析',
      count: statistics?.unanalyzed || 0,
      active: filters.analyzed_only === 'false',
      onClick: () => setFilters(prev => ({ ...prev, analyzed_only: 'false' }))
    }
  ]

  // 竞争难度筛选
  const competitionFilters = [
    { label: '简单', value: 'easy', color: '#22c55e', bgColor: '#dcfce7' },
    { label: '中等', value: 'medium', color: '#eab308', bgColor: '#fef3c7' },
    { label: '困难', value: 'hard', color: '#ef4444', bgColor: '#fee2e2' }
  ]

  // 日期筛选选项
  const dateRangeFilters = [
    { value: 'today', label: '今天' },
    { value: 'week', label: '近7天' },
    { value: 'month', label: '本月' }
  ]

  return (
    <div className="space-y-6">


      {/* 快速筛选 - 现代化设计 */}
      <div className="space-y-3">
        <div className="flex items-center gap-2">
          <Target className="w-5 h-5 text-primary" />
          <span className="text-sm font-semibold text-foreground">快速筛选</span>
        </div>
        <div className="flex items-center gap-2 flex-wrap">
          {quickFilters.map((filter, index) => (
            <Button
              key={index}
              onClick={filter.onClick}
              variant={filter.active ? "default" : "outline"}
              size="sm"
              className={`text-xs transition-all duration-300 transform ${
                filter.active
                  ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-lg scale-105 border-0'
                  : 'hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50 hover:text-blue-600 hover:border-blue-300 hover:shadow-md hover:scale-105 active:scale-95'
              }`}
              disabled={loading}
            >
              {filter.label}
              <Badge className={`ml-2 text-xs font-semibold ${
                filter.active
                  ? 'bg-white/20 text-white border-white/30'
                  : 'bg-blue-100 text-blue-700 border-blue-200'
              }`}>
                {filter.count}
              </Badge>
            </Button>
          ))}
        </div>
      </div>

      {/* 高级筛选 - 现代化设计 */}
      <div className="space-y-4">
        <div className="flex items-center gap-2">
          <Filter className="w-5 h-5 text-secondary" />
          <span className="text-sm font-semibold text-foreground">高级筛选</span>
        </div>
        <div className="flex flex-col lg:flex-row gap-4 lg:items-end">
          {/* 分类筛选 */}
          <div className="flex-1 space-y-2">
            <label className="block text-sm font-medium text-foreground">
              分类筛选
            </label>
            <Select
              value={filters.category || undefined}
              onValueChange={handleCategoryChange}
              disabled={loading || categoriesLoading}
            >
              <SelectTrigger className="h-11 border-2 border-border/50 focus:border-primary hover:border-primary/70 bg-white shadow-sm hover:shadow-md transition-all duration-200">
                <SelectValue placeholder={categoriesLoading ? "加载中..." : "选择分类"} />
              </SelectTrigger>
              <SelectContent className="bg-white border-2 border-border shadow-lg">
                <SelectItem
                  value="all"
                  className={`hover:bg-blue-50 hover:text-blue-600 focus:bg-blue-50 focus:text-blue-600 cursor-pointer transition-colors duration-200 ${
                    (!filters.category || filters.category === 'all') ? 'bg-blue-100 text-blue-700 font-medium' : ''
                  }`}
                >
                  全部分类
                </SelectItem>
                {categories.map((category) => (
                  <SelectItem
                    key={category.english_name}
                    value={category.english_name}
                    className={`hover:bg-blue-50 hover:text-blue-600 focus:bg-blue-50 focus:text-blue-600 cursor-pointer transition-colors duration-200 ${
                      filters.category === category.english_name ? 'bg-blue-100 text-blue-700 font-medium' : ''
                    }`}
                  >
                    {category.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* 竞争难度筛选 */}
          <div className="flex-1 space-y-2">
            <label className="block text-sm font-medium text-foreground">
              竞争难度
            </label>
            <Select
              value={filters.competition_level || undefined}
              onValueChange={handleCompetitionLevelChange}
              disabled={loading}
            >
              <SelectTrigger className="h-11 border-2 border-border/50 focus:border-primary hover:border-primary/70 bg-white shadow-sm hover:shadow-md transition-all duration-200">
                <SelectValue placeholder="选择难度" />
              </SelectTrigger>
              <SelectContent className="bg-white border-2 border-border shadow-lg">
                <SelectItem
                  value="all"
                  className={`hover:bg-blue-50 hover:text-blue-600 focus:bg-blue-50 focus:text-blue-600 cursor-pointer transition-colors duration-200 ${
                    (!filters.competition_level || filters.competition_level === 'all') ? 'bg-blue-100 text-blue-700 font-medium' : ''
                  }`}
                >
                  全部难度
                </SelectItem>
                {competitionFilters.map((comp) => (
                  <SelectItem
                    key={comp.value}
                    value={comp.value}
                    className={`hover:bg-blue-50 hover:text-blue-600 focus:bg-blue-50 focus:text-blue-600 cursor-pointer transition-colors duration-200 ${
                      filters.competition_level === comp.value ? 'bg-blue-100 text-blue-700 font-medium' : ''
                    }`}
                  >
                    <div className="flex items-center gap-2">
                      <div
                        className="w-3 h-3 rounded-full"
                        style={{ backgroundColor: comp.color }}
                      />
                      <span>{comp.label}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* 日期筛选 */}
          <div className="flex-1 space-y-2">
            <label className="block text-sm font-medium text-foreground">
              日期筛选
            </label>
            <Select
              value={filters.date_range || undefined}
              onValueChange={handleDateRangeChange}
              disabled={loading}
            >
              <SelectTrigger className="h-11 border-2 border-border/50 focus:border-primary hover:border-primary/70 bg-white shadow-sm hover:shadow-md transition-all duration-200">
                <SelectValue placeholder="选择时间范围" />
              </SelectTrigger>
              <SelectContent className="bg-white border-2 border-border shadow-lg">
                <SelectItem
                  value="all"
                  className={`hover:bg-blue-50 hover:text-blue-600 focus:bg-blue-50 focus:text-blue-600 cursor-pointer transition-colors duration-200 ${
                    (!filters.date_range || filters.date_range === 'all') ? 'bg-blue-100 text-blue-700 font-medium' : ''
                  }`}
                >
                  全部时间
                </SelectItem>
                {dateRangeFilters.map((dateRange) => (
                  <SelectItem
                    key={dateRange.value}
                    value={dateRange.value}
                    className={`hover:bg-blue-50 hover:text-blue-600 focus:bg-blue-50 focus:text-blue-600 cursor-pointer transition-colors duration-200 ${
                      filters.date_range === dateRange.value ? 'bg-blue-100 text-blue-700 font-medium' : ''
                    }`}
                  >
                    <div className="flex items-center gap-2">
                      <Calendar className="w-3 h-3 text-blue-500" />
                      <span>{dateRange.label}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
      </div>
    </div>

      {/* 竞争难度快速筛选 */}
      <div className="flex items-center gap-2 flex-wrap">
        <div className="flex items-center gap-1 mr-2">
          <Target className="w-4 h-4 text-gray-500" />
          <span className="text-sm font-medium text-gray-700">竞争难度:</span>
        </div>
        {competitionFilters.map((comp) => (
          <Button
            key={comp.value}
            onClick={() => setFilters(prev => ({
              ...prev,
              competition_level: prev.competition_level === comp.value ? '' : comp.value,
              analyzed_only: prev.competition_level === comp.value ? prev.analyzed_only : 'true'
            }))}
            variant={filters.competition_level === comp.value ? "default" : "outline"}
            size="sm"
            className={`text-xs transition-all duration-300 transform ${
              filters.competition_level === comp.value
                ? 'shadow-lg scale-105 font-semibold'
                : 'hover:shadow-md hover:scale-105 active:scale-95 hover:font-medium'
            }`}
            disabled={loading}
            style={{
              backgroundColor: filters.competition_level === comp.value ? comp.color : undefined,
              borderColor: comp.color,
              color: filters.competition_level === comp.value ? 'white' : comp.color
            }}
            onMouseEnter={(e) => {
              if (filters.competition_level !== comp.value) {
                e.currentTarget.style.backgroundColor = comp.bgColor
                e.currentTarget.style.color = comp.color
              }
            }}
            onMouseLeave={(e) => {
              if (filters.competition_level !== comp.value) {
                e.currentTarget.style.backgroundColor = 'transparent'
                e.currentTarget.style.color = comp.color
              }
            }}
          >
            <div
              className="w-2 h-2 rounded-full mr-1"
              style={{ backgroundColor: comp.color }}
            />
            {comp.label}
          </Button>
        ))}
      </div>

      {/* 当前筛选条件显示 */}
      {(filters.search || filters.category || filters.analyzed_only || filters.competition_level) && (
        <div className="flex items-center gap-2 flex-wrap pt-2 border-t">
          <span className="text-sm font-medium text-gray-700">当前筛选:</span>
          {filters.search && (
            <Badge variant="secondary">
              搜索: {filters.search}
            </Badge>
          )}
          {filters.category && (
            <Badge variant="secondary">
              分类: {categories.find(c => c.english_name === filters.category)?.name || filters.category}
            </Badge>
          )}
          {filters.analyzed_only && (
            <Badge variant="secondary">
              状态: {filters.analyzed_only === 'true' ? '已分析' : '待分析'}
            </Badge>
          )}
          {filters.competition_level && (
            <Badge variant="secondary">
              难度: {competitionFilters.find(c => c.value === filters.competition_level)?.label}
            </Badge>
          )}
          {filters.date_range && (
            <Badge variant="secondary">
              时间: {dateRangeFilters.find(d => d.value === filters.date_range)?.label}
            </Badge>
          )}
        </div>
      )}
    </div>
  )
}
