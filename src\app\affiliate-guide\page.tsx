'use client'

import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card'
import { Badge } from '../../components/ui/badge'
import { But<PERSON> } from '../../components/ui/button'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '../../components/ui/tabs'
import { Alert, AlertDescription } from '../../components/ui/alert'
import { 
  BookOpen, 
  Target, 
  PenTool, 
  TrendingUp,
  Search,
  Star,
  CheckCircle,
  XCircle,
  Info,
  Lightbulb,
  DollarSign,
  Users,
  Eye,
  Award,
  Zap
} from 'lucide-react'
import FrontendHeader from '../../components/frontend-header'
import FrontendFooter from '../../components/frontend-footer'
import { Toaster } from 'sonner'

export default function AffiliateGuidePage() {
  const [activeTab, setActiveTab] = useState('product-selection')

  const guideSteps = [
    {
      id: 'product-selection',
      title: '产品选择策略',
      icon: Target,
      color: 'text-blue-500',
      bgColor: 'bg-blue-50',
      borderColor: 'border-blue-200'
    },
    {
      id: 'content-writing',
      title: '内容撰写技巧',
      icon: PenTool,
      color: 'text-green-500',
      bgColor: 'bg-green-50',
      borderColor: 'border-green-200'
    },
    {
      id: 'review-structure',
      title: '评测结构模板',
      icon: BookOpen,
      color: 'text-purple-500',
      bgColor: 'bg-purple-50',
      borderColor: 'border-purple-200'
    },
    {
      id: 'promotion-strategy',
      title: '推广策略指南',
      icon: TrendingUp,
      color: 'text-orange-500',
      bgColor: 'bg-orange-50',
      borderColor: 'border-orange-200'
    }
  ]

  return (
    <div className="min-h-screen bg-background">
      <Toaster position="top-right" />
      <FrontendHeader />

      {/* 页面标题区域 */}
      <section className="py-8 px-4 bg-gradient-to-br from-blue-50 to-purple-50 border-b">
        <div className="container mx-auto">
          <div className="max-w-4xl mx-auto text-center">
            <div className="flex items-center justify-center mb-4">
              <div className="p-3 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl mr-4">
                <Award className="h-8 w-8 text-white" />
              </div>
              <h1 className="text-4xl font-bold text-foreground">联盟营销完整指南</h1>
            </div>
            <p className="text-lg text-muted-foreground mb-6">
              从产品选择到内容撰写，从评测结构到推广策略，掌握联盟营销的所有核心技能
            </p>
            <div className="flex items-center justify-center gap-6 text-sm text-muted-foreground">
              <div className="flex items-center">
                <Target className="h-4 w-4 mr-2 text-blue-500" />
                产品选择
              </div>
              <div className="flex items-center">
                <PenTool className="h-4 w-4 mr-2 text-green-500" />
                内容撰写
              </div>
              <div className="flex items-center">
                <BookOpen className="h-4 w-4 mr-2 text-purple-500" />
                评测模板
              </div>
              <div className="flex items-center">
                <TrendingUp className="h-4 w-4 mr-2 text-orange-500" />
                推广策略
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 导航标签 */}
      <section className="py-6 px-4 bg-muted/30">
        <div className="container mx-auto">
          <div className="max-w-6xl mx-auto">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="grid w-full grid-cols-4 mb-8">
                {guideSteps.map((step) => {
                  const IconComponent = step.icon
                  return (
                    <TabsTrigger 
                      key={step.id} 
                      value={step.id}
                      className="flex items-center gap-2 text-sm"
                    >
                      <IconComponent className={`h-4 w-4 ${step.color}`} />
                      <span className="hidden sm:inline">{step.title}</span>
                    </TabsTrigger>
                  )
                })}
              </TabsList>

              {/* 产品选择策略 */}
              <TabsContent value="product-selection" className="space-y-6">
                <Card className="shadow-lg border-2 border-blue-100">
                  <CardHeader>
                    <CardTitle className="flex items-center text-2xl">
                      <Target className="h-6 w-6 mr-3 text-blue-500" />
                      产品选择策略
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    
                    {/* 佣金率选择 */}
                    <div className="space-y-4">
                      <h3 className="text-xl font-semibold flex items-center">
                        <DollarSign className="h-5 w-5 mr-2 text-green-500" />
                        选择高佣金产品
                      </h3>
                      <Alert className="border-green-200 bg-green-50">
                        <Info className="h-4 w-4 text-green-600" />
                        <AlertDescription className="text-green-700">
                          <strong>佣金率差异巨大：</strong>
                          推广20美元的电池最多赚0.40美元，而60美元的护肤品可能赚6美元或更多。
                          寻找价格适中但佣金率高的产品类别。
                        </AlertDescription>
                      </Alert>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <Card className="border-green-200">
                          <CardHeader className="pb-3">
                            <CardTitle className="text-lg text-green-600">高佣金类别</CardTitle>
                          </CardHeader>
                          <CardContent>
                            <ul className="space-y-2 text-sm">
                              <li className="flex items-center"><CheckCircle className="h-4 w-4 mr-2 text-green-500" />护肤品与个人护理 (4-10%)</li>
                              <li className="flex items-center"><CheckCircle className="h-4 w-4 mr-2 text-green-500" />家庭整理用品 (4-8%)</li>
                              <li className="flex items-center"><CheckCircle className="h-4 w-4 mr-2 text-green-500" />健身器材 (4-8%)</li>
                              <li className="flex items-center"><CheckCircle className="h-4 w-4 mr-2 text-green-500" />宠物用品 (4-8%)</li>
                            </ul>
                          </CardContent>
                        </Card>
                        
                        <Card className="border-red-200">
                          <CardHeader className="pb-3">
                            <CardTitle className="text-lg text-red-600">低佣金类别</CardTitle>
                          </CardHeader>
                          <CardContent>
                            <ul className="space-y-2 text-sm">
                              <li className="flex items-center"><XCircle className="h-4 w-4 mr-2 text-red-500" />电子产品 (1-2%)</li>
                              <li className="flex items-center"><XCircle className="h-4 w-4 mr-2 text-red-500" />电池类产品 (1-2%)</li>
                              <li className="flex items-center"><XCircle className="h-4 w-4 mr-2 text-red-500" />大型家电 (1-2%)</li>
                              <li className="flex items-center"><XCircle className="h-4 w-4 mr-2 text-red-500" />汽车配件 (1-3%)</li>
                            </ul>
                          </CardContent>
                        </Card>
                      </div>
                    </div>

                    {/* 产品研究工具 */}
                    <div className="space-y-4">
                      <h3 className="text-xl font-semibold flex items-center">
                        <Search className="h-5 w-5 mr-2 text-blue-500" />
                        产品研究工具
                      </h3>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <Card className="border-blue-200">
                          <CardHeader className="pb-3">
                            <CardTitle className="text-lg">免费工具</CardTitle>
                          </CardHeader>
                          <CardContent>
                            <ul className="space-y-2 text-sm">
                              <li>• Amazon Best Sellers (每小时更新)</li>
                              <li>• Movers & Shakers (快速上升产品)</li>
                              <li>• Google Trends + Amazon搜索</li>
                              <li>• 亚马逊精选清单</li>
                            </ul>
                          </CardContent>
                        </Card>
                        
                        <Card className="border-purple-200">
                          <CardHeader className="pb-3">
                            <CardTitle className="text-lg">付费工具</CardTitle>
                          </CardHeader>
                          <CardContent>
                            <ul className="space-y-2 text-sm">
                              <li>• AMZScout PRO AI (综合评分)</li>
                              <li>• Jungle Scout (历史数据)</li>
                              <li>• Helium 10 (关键词排名)</li>
                              <li>• Keitaro Tracker (转化追踪)</li>
                            </ul>
                          </CardContent>
                        </Card>
                      </div>
                    </div>

                    {/* 热门细分领域 */}
                    <div className="space-y-4">
                      <h3 className="text-xl font-semibold flex items-center">
                        <TrendingUp className="h-5 w-5 mr-2 text-orange-500" />
                        2025年热门细分领域
                      </h3>
                      
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <Card className="border-orange-200">
                          <CardContent className="p-4">
                            <h4 className="font-semibold text-orange-600 mb-2">健康与健身</h4>
                            <p className="text-sm text-muted-foreground">家庭健身装备、按摩枪、补充剂、可穿戴设备</p>
                          </CardContent>
                        </Card>
                        
                        <Card className="border-orange-200">
                          <CardContent className="p-4">
                            <h4 className="font-semibold text-orange-600 mb-2">居家办公</h4>
                            <p className="text-sm text-muted-foreground">升降桌、人体工学椅、降噪耳机、补光灯</p>
                          </CardContent>
                        </Card>
                        
                        <Card className="border-orange-200">
                          <CardContent className="p-4">
                            <h4 className="font-semibold text-orange-600 mb-2">家庭整理</h4>
                            <p className="text-sm text-muted-foreground">收纳箱、可折叠搁架、抽屉分隔器</p>
                          </CardContent>
                        </Card>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* 内容撰写技巧 */}
              <TabsContent value="content-writing" className="space-y-6">
                <Card className="shadow-lg border-2 border-green-100">
                  <CardHeader>
                    <CardTitle className="flex items-center text-2xl">
                      <PenTool className="h-6 w-6 mr-3 text-green-500" />
                      内容撰写技巧
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-6">

                    {/* 诚实评测原则 */}
                    <div className="space-y-4">
                      <h3 className="text-xl font-semibold flex items-center">
                        <CheckCircle className="h-5 w-5 mr-2 text-green-500" />
                        诚实评测的核心原则
                      </h3>

                      <Alert className="border-green-200 bg-green-50">
                        <Lightbulb className="h-4 w-4 text-green-600" />
                        <AlertDescription className="text-green-700">
                          <strong>信任是转化的基础：</strong>
                          你的受众能够识别销售话术。诚实的评测应该展现真正的专业知识和权威性。
                        </AlertDescription>
                      </Alert>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="space-y-3">
                          <h4 className="font-semibold text-green-600">✅ 应该做的</h4>
                          <ul className="space-y-2 text-sm">
                            <li>• 从客观事实开始（成分、规格）</li>
                            <li>• 分享真实的购买动机</li>
                            <li>• 提及产品的缺点</li>
                            <li>• 包含个人使用体验</li>
                            <li>• 添加原创图片和视频</li>
                            <li>• 与竞品进行比较</li>
                          </ul>
                        </div>

                        <div className="space-y-3">
                          <h4 className="font-semibold text-red-600">❌ 避免做的</h4>
                          <ul className="space-y-2 text-sm">
                            <li>• 使用标题党标题</li>
                            <li>• 只复制官网描述</li>
                            <li>• 过度推销的语调</li>
                            <li>• 隐藏产品缺陷</li>
                            <li>• 模仿大网站的风格</li>
                            <li>• 使用AI生成完整评测</li>
                          </ul>
                        </div>
                      </div>
                    </div>

                    {/* 未使用产品的评测技巧 */}
                    <div className="space-y-4">
                      <h3 className="text-xl font-semibold flex items-center">
                        <Search className="h-5 w-5 mr-2 text-blue-500" />
                        未使用产品的评测技巧
                      </h3>

                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <Card className="border-blue-200">
                          <CardHeader className="pb-3">
                            <CardTitle className="text-lg">深度研究</CardTitle>
                          </CardHeader>
                          <CardContent>
                            <ul className="space-y-1 text-sm">
                              <li>• 查看竞争对手网站</li>
                              <li>• 观看相关YouTube视频</li>
                              <li>• 研究制造商网站</li>
                              <li>• 了解产品技术细节</li>
                            </ul>
                          </CardContent>
                        </Card>

                        <Card className="border-blue-200">
                          <CardHeader className="pb-3">
                            <CardTitle className="text-lg">用户评价分析</CardTitle>
                          </CardHeader>
                          <CardContent>
                            <ul className="space-y-1 text-sm">
                              <li>• 亚马逊用户评论</li>
                              <li>• 其他电商平台评价</li>
                              <li>• 社交媒体反馈</li>
                              <li>• 专业评测网站</li>
                            </ul>
                          </CardContent>
                        </Card>

                        <Card className="border-blue-200">
                          <CardHeader className="pb-3">
                            <CardTitle className="text-lg">问答研究</CardTitle>
                          </CardHeader>
                          <CardContent>
                            <ul className="space-y-1 text-sm">
                              <li>• 亚马逊Q&A部分</li>
                              <li>• Reddit相关讨论</li>
                              <li>• 论坛用户问题</li>
                              <li>• 常见疑虑点</li>
                            </ul>
                          </CardContent>
                        </Card>
                      </div>
                    </div>

                    {/* SEO优化技巧 */}
                    <div className="space-y-4">
                      <h3 className="text-xl font-semibold flex items-center">
                        <TrendingUp className="h-5 w-5 mr-2 text-purple-500" />
                        SEO优化技巧
                      </h3>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <Card className="border-purple-200">
                          <CardHeader className="pb-3">
                            <CardTitle className="text-lg">关键词策略</CardTitle>
                          </CardHeader>
                          <CardContent>
                            <ul className="space-y-2 text-sm">
                              <li>• 在前150字符内使用主关键词</li>
                              <li>• 自然分布关键词</li>
                              <li>• 在结论中重复主关键词</li>
                              <li>• 使用长尾关键词变体</li>
                            </ul>
                          </CardContent>
                        </Card>

                        <Card className="border-purple-200">
                          <CardHeader className="pb-3">
                            <CardTitle className="text-lg">内容结构</CardTitle>
                          </CardHeader>
                          <CardContent>
                            <ul className="space-y-2 text-sm">
                              <li>• 使用简短句子和段落</li>
                              <li>• 添加副标题和项目符号</li>
                              <li>• 包含FAQ部分</li>
                              <li>• 添加图片打破文字单调</li>
                            </ul>
                          </CardContent>
                        </Card>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* 评测结构模板 */}
              <TabsContent value="review-structure" className="space-y-6">
                <Card className="shadow-lg border-2 border-purple-100">
                  <CardHeader>
                    <CardTitle className="flex items-center text-2xl">
                      <BookOpen className="h-6 w-6 mr-3 text-purple-500" />
                      评测结构模板
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-6">

                    {/* 评测类型 */}
                    <div className="space-y-4">
                      <h3 className="text-xl font-semibold flex items-center">
                        <BookOpen className="h-5 w-5 mr-2 text-purple-500" />
                        三种评测类型
                      </h3>

                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <Card className="border-blue-200">
                          <CardHeader className="pb-3">
                            <CardTitle className="text-lg text-blue-600">简短评测</CardTitle>
                          </CardHeader>
                          <CardContent>
                            <ul className="space-y-2 text-sm">
                              <li>• 适合低价产品</li>
                              <li>• 包含星级评分</li>
                              <li>• 快速购买决策</li>
                              <li>• 可批量处理</li>
                            </ul>
                          </CardContent>
                        </Card>

                        <Card className="border-green-200">
                          <CardHeader className="pb-3">
                            <CardTitle className="text-lg text-green-600">全面评测</CardTitle>
                          </CardHeader>
                          <CardContent>
                            <ul className="space-y-2 text-sm">
                              <li>• 详细深入分析</li>
                              <li>• 回答所有疑问</li>
                              <li>• 需要深度研究</li>
                              <li>• 高转化潜力</li>
                            </ul>
                          </CardContent>
                        </Card>

                        <Card className="border-orange-200">
                          <CardHeader className="pb-3">
                            <CardTitle className="text-lg text-orange-600">产品比较</CardTitle>
                          </CardHeader>
                          <CardContent>
                            <ul className="space-y-2 text-sm">
                              <li>• 多产品对比</li>
                              <li>• 表格形式展示</li>
                              <li>• 突出优势特性</li>
                              <li>• 帮助选择决策</li>
                            </ul>
                          </CardContent>
                        </Card>
                      </div>
                    </div>

                    {/* 标准评测结构 */}
                    <div className="space-y-4">
                      <h3 className="text-xl font-semibold flex items-center">
                        <Star className="h-5 w-5 mr-2 text-yellow-500" />
                        标准评测结构模板
                      </h3>

                      <Card className="border-yellow-200 bg-yellow-50">
                        <CardContent className="p-6">
                          <div className="space-y-4">
                            <div className="flex items-start space-x-3">
                              <div className="w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold">1</div>
                              <div>
                                <h4 className="font-semibold">吸引眼球的标题</h4>
                                <p className="text-sm text-muted-foreground">避免标题党，保持诚实直接</p>
                              </div>
                            </div>

                            <div className="flex items-start space-x-3">
                              <div className="w-8 h-8 bg-green-500 text-white rounded-full flex items-center justify-center text-sm font-bold">2</div>
                              <div>
                                <h4 className="font-semibold">摘要框/TLDR</h4>
                                <p className="text-sm text-muted-foreground">包含评分、优缺点、适用人群、CTA按钮</p>
                              </div>
                            </div>

                            <div className="flex items-start space-x-3">
                              <div className="w-8 h-8 bg-purple-500 text-white rounded-full flex items-center justify-center text-sm font-bold">3</div>
                              <div>
                                <h4 className="font-semibold">简短介绍</h4>
                                <p className="text-sm text-muted-foreground">几句话概述，包含联盟链接</p>
                              </div>
                            </div>

                            <div className="flex items-start space-x-3">
                              <div className="w-8 h-8 bg-orange-500 text-white rounded-full flex items-center justify-center text-sm font-bold">4</div>
                              <div>
                                <h4 className="font-semibold">产品概述</h4>
                                <p className="text-sm text-muted-foreground">关键特性、规格列表（项目符号）</p>
                              </div>
                            </div>

                            <div className="flex items-start space-x-3">
                              <div className="w-8 h-8 bg-red-500 text-white rounded-full flex items-center justify-center text-sm font-bold">5</div>
                              <div>
                                <h4 className="font-semibold">详细评测</h4>
                                <p className="text-sm text-muted-foreground">个人体验、功能分析、使用感受</p>
                              </div>
                            </div>

                            <div className="flex items-start space-x-3">
                              <div className="w-8 h-8 bg-teal-500 text-white rounded-full flex items-center justify-center text-sm font-bold">6</div>
                              <div>
                                <h4 className="font-semibold">优缺点分析</h4>
                                <p className="text-sm text-muted-foreground">平衡展示，至少包含一些缺点</p>
                              </div>
                            </div>

                            <div className="flex items-start space-x-3">
                              <div className="w-8 h-8 bg-pink-500 text-white rounded-full flex items-center justify-center text-sm font-bold">7</div>
                              <div>
                                <h4 className="font-semibold">适用人群分析</h4>
                                <p className="text-sm text-muted-foreground">谁应该买，谁不应该买</p>
                              </div>
                            </div>

                            <div className="flex items-start space-x-3">
                              <div className="w-8 h-8 bg-indigo-500 text-white rounded-full flex items-center justify-center text-sm font-bold">8</div>
                              <div>
                                <h4 className="font-semibold">竞品比较</h4>
                                <p className="text-sm text-muted-foreground">与同类产品对比，展示公正性</p>
                              </div>
                            </div>

                            <div className="flex items-start space-x-3">
                              <div className="w-8 h-8 bg-gray-500 text-white rounded-full flex items-center justify-center text-sm font-bold">9</div>
                              <div>
                                <h4 className="font-semibold">替代方案</h4>
                                <p className="text-sm text-muted-foreground">提供其他选择，增加可信度</p>
                              </div>
                            </div>

                            <div className="flex items-start space-x-3">
                              <div className="w-8 h-8 bg-yellow-500 text-white rounded-full flex items-center justify-center text-sm font-bold">10</div>
                              <div>
                                <h4 className="font-semibold">最终评价 + FAQ</h4>
                                <p className="text-sm text-muted-foreground">总结推荐理由，回答常见问题</p>
                              </div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    </div>

                    {/* 联盟链接放置策略 */}
                    <div className="space-y-4">
                      <h3 className="text-xl font-semibold flex items-center">
                        <Zap className="h-5 w-5 mr-2 text-blue-500" />
                        联盟链接放置策略
                      </h3>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <Card className="border-blue-200">
                          <CardHeader className="pb-3">
                            <CardTitle className="text-lg">最佳位置</CardTitle>
                          </CardHeader>
                          <CardContent>
                            <ul className="space-y-2 text-sm">
                              <li>• 摘要框中的CTA按钮</li>
                              <li>• 介绍段落后</li>
                              <li>• 产品图片链接</li>
                              <li>• 文章结尾处</li>
                            </ul>
                          </CardContent>
                        </Card>

                        <Card className="border-red-200">
                          <CardHeader className="pb-3">
                            <CardTitle className="text-lg">注意事项</CardTitle>
                          </CardHeader>
                          <CardContent>
                            <ul className="space-y-2 text-sm">
                              <li>• 避免过多链接</li>
                              <li>• 使用自然锚文本</li>
                              <li>• 添加透明度声明</li>
                              <li>• 测试不同CTA文案</li>
                            </ul>
                          </CardContent>
                        </Card>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* 推广策略指南 */}
              <TabsContent value="promotion-strategy" className="space-y-6">
                <Card className="shadow-lg border-2 border-orange-100">
                  <CardHeader>
                    <CardTitle className="flex items-center text-2xl">
                      <TrendingUp className="h-6 w-6 mr-3 text-orange-500" />
                      推广策略指南
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-6">

                    {/* 内容推广渠道 */}
                    <div className="space-y-4">
                      <h3 className="text-xl font-semibold flex items-center">
                        <Users className="h-5 w-5 mr-2 text-blue-500" />
                        多渠道推广策略
                      </h3>

                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <Card className="border-blue-200">
                          <CardHeader className="pb-3">
                            <CardTitle className="text-lg text-blue-600">Instagram</CardTitle>
                          </CardHeader>
                          <CardContent>
                            <ul className="space-y-1 text-sm">
                              <li>• Reels产品演示</li>
                              <li>• Stories快速评测</li>
                              <li>• 轮播帖子对比</li>
                              <li>• Linktree链接集合</li>
                            </ul>
                          </CardContent>
                        </Card>

                        <Card className="border-red-200">
                          <CardHeader className="pb-3">
                            <CardTitle className="text-lg text-red-600">YouTube</CardTitle>
                          </CardHeader>
                          <CardContent>
                            <ul className="space-y-1 text-sm">
                              <li>• 开箱评测视频</li>
                              <li>• 产品对比视频</li>
                              <li>• "必备清单"合集</li>
                              <li>• 教程式内容</li>
                            </ul>
                          </CardContent>
                        </Card>

                        <Card className="border-green-200">
                          <CardHeader className="pb-3">
                            <CardTitle className="text-lg text-green-600">TikTok</CardTitle>
                          </CardHeader>
                          <CardContent>
                            <ul className="space-y-1 text-sm">
                              <li>• 产品演示短视频</li>
                              <li>• 前后对比效果</li>
                              <li>• 流行音频结合</li>
                              <li>• 故事化展示</li>
                            </ul>
                          </CardContent>
                        </Card>

                        <Card className="border-purple-200">
                          <CardHeader className="pb-3">
                            <CardTitle className="text-lg text-purple-600">Pinterest</CardTitle>
                          </CardHeader>
                          <CardContent>
                            <ul className="space-y-1 text-sm">
                              <li>• 垂直型产品图钉</li>
                              <li>• 生活方式场景</li>
                              <li>• "最佳XX"合集</li>
                              <li>• 季节性内容</li>
                            </ul>
                          </CardContent>
                        </Card>

                        <Card className="border-yellow-200">
                          <CardHeader className="pb-3">
                            <CardTitle className="text-lg text-yellow-600">邮件营销</CardTitle>
                          </CardHeader>
                          <CardContent>
                            <ul className="space-y-1 text-sm">
                              <li>• 周/月产品推荐</li>
                              <li>• 细分受众列表</li>
                              <li>• 季节性促销</li>
                              <li>• 独家折扣码</li>
                            </ul>
                          </CardContent>
                        </Card>

                        <Card className="border-indigo-200">
                          <CardHeader className="pb-3">
                            <CardTitle className="text-lg text-indigo-600">博客SEO</CardTitle>
                          </CardHeader>
                          <CardContent>
                            <ul className="space-y-1 text-sm">
                              <li>• 长尾关键词优化</li>
                              <li>• 深度评测文章</li>
                              <li>• 内链建设</li>
                              <li>• 定期内容更新</li>
                            </ul>
                          </CardContent>
                        </Card>
                      </div>
                    </div>

                    {/* 付费广告策略 */}
                    <div className="space-y-4">
                      <h3 className="text-xl font-semibold flex items-center">
                        <DollarSign className="h-5 w-5 mr-2 text-green-500" />
                        付费广告策略
                      </h3>

                      <Alert className="border-red-200 bg-red-50">
                        <Info className="h-4 w-4 text-red-600" />
                        <AlertDescription className="text-red-700">
                          <strong>重要提醒：</strong>
                          不要直接将流量发送到亚马逊！这违反服务条款。应该引导到着陆页、评测文章或产品比较页面。
                        </AlertDescription>
                      </Alert>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <Card className="border-blue-200">
                          <CardHeader className="pb-3">
                            <CardTitle className="text-lg">推荐平台</CardTitle>
                          </CardHeader>
                          <CardContent>
                            <ul className="space-y-2 text-sm">
                              <li>• <strong>Facebook/Instagram:</strong> 清单式广告、轮播广告</li>
                              <li>• <strong>Google搜索:</strong> 高意向长尾关键词</li>
                              <li>• <strong>Pinterest:</strong> 生活方式相关产品</li>
                              <li>• <strong>TikTok Spark:</strong> 优质有机内容推广</li>
                            </ul>
                          </CardContent>
                        </Card>

                        <Card className="border-green-200">
                          <CardHeader className="pb-3">
                            <CardTitle className="text-lg">成功要素</CardTitle>
                          </CardHeader>
                          <CardContent>
                            <ul className="space-y-2 text-sm">
                              <li>• 移动端优化的着陆页</li>
                              <li>• 详细的转化追踪</li>
                              <li>• A/B测试不同创意</li>
                              <li>• 相似受众扩展</li>
                            </ul>
                          </CardContent>
                        </Card>
                      </div>
                    </div>

                    {/* 7步推广流程 */}
                    <div className="space-y-4">
                      <h3 className="text-xl font-semibold flex items-center">
                        <CheckCircle className="h-5 w-5 mr-2 text-purple-500" />
                        7步推广流程
                      </h3>

                      <Card className="border-purple-200 bg-purple-50">
                        <CardContent className="p-6">
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div className="space-y-3">
                              <div className="flex items-center space-x-3">
                                <Badge className="bg-purple-500">1</Badge>
                                <span className="font-semibold">选择合适产品</span>
                              </div>
                              <div className="flex items-center space-x-3">
                                <Badge className="bg-purple-500">2</Badge>
                                <span className="font-semibold">深度研究收集信息</span>
                              </div>
                              <div className="flex items-center space-x-3">
                                <Badge className="bg-purple-500">3</Badge>
                                <span className="font-semibold">试用产品(如可能)</span>
                              </div>
                              <div className="flex items-center space-x-3">
                                <Badge className="bg-purple-500">4</Badge>
                                <span className="font-semibold">构建评测结构</span>
                              </div>
                            </div>
                            <div className="space-y-3">
                              <div className="flex items-center space-x-3">
                                <Badge className="bg-purple-500">5</Badge>
                                <span className="font-semibold">创建吸引人内容</span>
                              </div>
                              <div className="flex items-center space-x-3">
                                <Badge className="bg-purple-500">6</Badge>
                                <span className="font-semibold">透明披露联盟关系</span>
                              </div>
                              <div className="flex items-center space-x-3">
                                <Badge className="bg-purple-500">7</Badge>
                                <span className="font-semibold">多渠道推广分享</span>
                              </div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    </div>

                    {/* 成功关键要素 */}
                    <div className="space-y-4">
                      <h3 className="text-xl font-semibold flex items-center">
                        <Award className="h-5 w-5 mr-2 text-yellow-500" />
                        长期成功的关键
                      </h3>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <Card className="border-yellow-200">
                          <CardHeader className="pb-3">
                            <CardTitle className="text-lg text-yellow-600">建立信任</CardTitle>
                          </CardHeader>
                          <CardContent>
                            <ul className="space-y-2 text-sm">
                              <li>• 诚实评测建立权威</li>
                              <li>• 长期关系胜过短期利益</li>
                              <li>• 透明度声明</li>
                              <li>• 持续提供价值</li>
                            </ul>
                          </CardContent>
                        </Card>

                        <Card className="border-green-200">
                          <CardHeader className="pb-3">
                            <CardTitle className="text-lg text-green-600">持续优化</CardTitle>
                          </CardHeader>
                          <CardContent>
                            <ul className="space-y-2 text-sm">
                              <li>• 追踪转化数据</li>
                              <li>• 测试不同策略</li>
                              <li>• 关注用户反馈</li>
                              <li>• 适应市场变化</li>
                            </ul>
                          </CardContent>
                        </Card>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </section>

      <FrontendFooter />
    </div>
  )
}
