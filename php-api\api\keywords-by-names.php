<?php
require_once '../utils/Response.php';
require_once '../models/Keyword.php';

/**
 * 根据关键词名称获取ID的API
 * 支持slug格式的ID
 */

// 处理CORS预检请求
Response::handleOptions();

// 验证请求方法
$method = Response::validateMethod(['POST']);

try {
    $keywordModel = new Keyword();
    handleGetKeywordIds($keywordModel);
    
} catch (Exception $e) {
    Response::serverError('获取关键词ID失败: ' . $e->getMessage());
}

/**
 * 处理获取关键词ID请求
 */
function handleGetKeywordIds($keywordModel) {
    $data = Response::getRequestData();
    
    // 验证参数
    if (!isset($data['keywords']) || !is_array($data['keywords']) || empty($data['keywords'])) {
        Response::validationError(['keywords' => '关键词数组不能为空']);
    }
    
    $keywords = $data['keywords'];
    $results = [];
    
    foreach ($keywords as $keyword) {
        if (empty(trim($keyword))) {
            continue;
        }
        
        // 通过关键词名称查找记录
        $keywordRecord = $keywordModel->getByKeyword(trim($keyword));
        
        if ($keywordRecord) {
            $results[] = [
                'keyword' => $keyword,
                'id' => $keywordRecord['id'],
                'source' => $keywordRecord['source'] ?? 'unknown'
            ];
        }
    }
    
    Response::success($results, count($results) > 0 ? 
        "找到 " . count($results) . " 个关键词ID" : 
        "未找到匹配的关键词");
}
?>
