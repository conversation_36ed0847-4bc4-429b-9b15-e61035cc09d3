<?php
require_once __DIR__ . '/../config/cors.php';
require_once __DIR__ . '/../models/KeywordReport.php';
require_once __DIR__ . '/../utils/Response.php';

// 处理CORS预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {
    $keywordReportModel = new KeywordReport();
    
    switch ($_SERVER['REQUEST_METHOD']) {
        case 'GET':
            handleGet($keywordReportModel);
            break;
        case 'POST':
            handlePost($keywordReportModel);
            break;
        case 'PUT':
            handlePut($keywordReportModel);
            break;
        case 'DELETE':
            handleDelete($keywordReportModel);
            break;
        default:
            Response::methodNotAllowed();
    }
} catch (Exception $e) {
    Response::serverError('服务器内部错误: ' . $e->getMessage());
}

/**
 * 处理GET请求 - 获取关键词报告
 */
function handleGet($keywordReportModel) {
    if (!isset($_GET['keyword_id'])) {
        Response::validationError(['keyword_id' => '关键词ID不能为空']);
    }
    
    $keywordId = $_GET['keyword_id'];
    $report = $keywordReportModel->getByKeywordId($keywordId);
    
    if ($report) {
        // 解析JSON字段
        if ($report['related_keywords']) {
            $report['related_keywords'] = json_decode($report['related_keywords'], true);
        }
        if ($report['google_search_results']) {
            $report['google_search_results'] = json_decode($report['google_search_results'], true);
        }
        if ($report['search_metrics']) {
            $report['search_metrics'] = json_decode($report['search_metrics'], true);
        }
        if ($report['top_competitors_analysis']) {
            $report['top_competitors_analysis'] = json_decode($report['top_competitors_analysis'], true);
        }
        if ($report['title_patterns_analysis']) {
            $report['title_patterns_analysis'] = json_decode($report['title_patterns_analysis'], true);
        }
        if ($report['action_suggestions']) {
            $report['action_suggestions'] = json_decode($report['action_suggestions'], true);
        }
        if ($report['top_competitors']) {
            $report['top_competitors'] = json_decode($report['top_competitors'], true);
        }
        
        Response::success($report);
    } else {
        Response::notFound('报告不存在');
    }
}

/**
 * 处理POST请求 - 创建关键词报告
 */
function handlePost($keywordReportModel) {
    $data = Response::getRequestData();
    
    // 验证必需字段
    if (!isset($data['keyword_id']) || empty($data['keyword_id'])) {
        Response::validationError(['keyword_id' => '关键词ID不能为空']);
    }
    
    // 检查是否已存在报告
    if ($keywordReportModel->hasReport($data['keyword_id'])) {
        // 如果已存在，则更新
        $success = $keywordReportModel->update($data['keyword_id'], $data);
        $message = '报告更新成功';
    } else {
        // 如果不存在，则创建
        $success = $keywordReportModel->create($data);
        $message = '报告创建成功';
    }
    
    if (!$success) {
        Response::serverError('报告保存失败');
    }
    
    // 获取保存后的报告
    $report = $keywordReportModel->getByKeywordId($data['keyword_id']);
    
    Response::success($report, $message, 201);
}

/**
 * 处理PUT请求 - 更新关键词报告
 */
function handlePut($keywordReportModel) {
    if (!isset($_GET['keyword_id'])) {
        Response::validationError(['keyword_id' => '关键词ID不能为空']);
    }
    
    $keywordId = $_GET['keyword_id'];
    $data = Response::getRequestData();
    
    $success = $keywordReportModel->update($keywordId, $data);
    
    if (!$success) {
        Response::serverError('报告更新失败');
    }
    
    // 获取更新后的报告
    $report = $keywordReportModel->getByKeywordId($keywordId);
    
    Response::success($report, '报告更新成功');
}

/**
 * 处理DELETE请求 - 删除关键词报告
 */
function handleDelete($keywordReportModel) {
    if (!isset($_GET['keyword_id'])) {
        Response::validationError(['keyword_id' => '关键词ID不能为空']);
    }
    
    $keywordId = $_GET['keyword_id'];
    $success = $keywordReportModel->deleteByKeywordId($keywordId);
    
    if (!$success) {
        Response::serverError('报告删除失败');
    }
    
    Response::success(['keyword_id' => $keywordId], '报告删除成功');
}
?>
