'use client'

import { useState } from 'react'
import { Button } from './ui/button'
import { Input } from './ui/input'
import { 
  ChevronLeft, 
  ChevronRight, 
  ChevronsLeft, 
  ChevronsRight,
  MoreHorizontal
} from 'lucide-react'

interface PaginationProps {
  currentPage: number
  totalPages: number
  onPageChange: (page: number) => void
  className?: string
  showQuickJumper?: boolean
  showSizeChanger?: boolean
  pageSizeOptions?: number[]
  pageSize?: number
  onPageSizeChange?: (size: number) => void
  total?: number
}

export default function Pagination({
  currentPage,
  totalPages,
  onPageChange,
  className = '',
  showQuickJumper = true,
  showSizeChanger = false,
  pageSizeOptions = [6, 12, 24, 48],
  pageSize = 6,
  onPageSizeChange,
  total = 0
}: PaginationProps) {
  const [jumpPage, setJumpPage] = useState('')

  // 生成页码数组
  const generatePageNumbers = () => {
    const pages: (number | string)[] = []
    const maxVisible = 5 // 最多显示5个页码

    if (totalPages <= maxVisible) {
      // 总页数小于等于5，显示所有页码
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i)
      }
    } else {
      // 总页数大于5，需要省略
      if (currentPage <= 3) {
        // 当前页在前3页
        pages.push(1, 2, 3, 4, '...', totalPages)
      } else if (currentPage >= totalPages - 2) {
        // 当前页在后3页
        pages.push(1, '...', totalPages - 3, totalPages - 2, totalPages - 1, totalPages)
      } else {
        // 当前页在中间
        pages.push(1, '...', currentPage - 1, currentPage, currentPage + 1, '...', totalPages)
      }
    }

    return pages
  }

  const handleJumpToPage = () => {
    const page = parseInt(jumpPage)
    if (page >= 1 && page <= totalPages) {
      onPageChange(page)
      setJumpPage('')
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleJumpToPage()
    }
  }

  if (totalPages <= 1) return null

  const pageNumbers = generatePageNumbers()

  return (
    <div className={`flex flex-col sm:flex-row items-center justify-between gap-4 ${className}`}>
      {/* 分页信息 */}
      <div className="text-sm text-muted-foreground">
        显示第 {((currentPage - 1) * pageSize) + 1} - {Math.min(currentPage * pageSize, total)} 条，
        共 {total} 条数据
      </div>

      {/* 分页控件 */}
      <div className="flex items-center gap-2">
        {/* 首页 */}
        <Button
          variant="outline"
          size="sm"
          onClick={() => onPageChange(1)}
          disabled={currentPage === 1}
          className="h-8 w-8 p-0 transition-all duration-300 ease-out hover:scale-105 hover:shadow-md hover:border-primary/50 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100 disabled:hover:shadow-none"
        >
          <ChevronsLeft className="h-4 w-4" />
        </Button>

        {/* 上一页 */}
        <Button
          variant="outline"
          size="sm"
          onClick={() => onPageChange(currentPage - 1)}
          disabled={currentPage === 1}
          className="h-8 w-8 p-0 transition-all duration-300 ease-out hover:scale-105 hover:shadow-md hover:border-primary/50 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100 disabled:hover:shadow-none"
        >
          <ChevronLeft className="h-4 w-4" />
        </Button>

        {/* 页码 */}
        <div className="flex items-center gap-1">
          {pageNumbers.map((page, index) => (
            <div key={index}>
              {page === '...' ? (
                <div className="flex items-center justify-center h-8 w-8">
                  <MoreHorizontal className="h-4 w-4 text-muted-foreground" />
                </div>
              ) : (
                <Button
                  variant={currentPage === page ? "default" : "outline"}
                  size="sm"
                  onClick={() => onPageChange(page as number)}
                  className={`h-8 w-8 p-0 transition-all duration-300 ease-out hover:scale-105 hover:shadow-md ${
                    currentPage === page
                      ? "bg-primary text-primary-foreground shadow-lg scale-105 border-primary"
                      : "hover:border-primary/50 hover:bg-primary/5"
                  }`}
                >
                  {page}
                </Button>
              )}
            </div>
          ))}
        </div>

        {/* 下一页 */}
        <Button
          variant="outline"
          size="sm"
          onClick={() => onPageChange(currentPage + 1)}
          disabled={currentPage === totalPages}
          className="h-8 w-8 p-0 transition-all duration-300 ease-out hover:scale-105 hover:shadow-md hover:border-primary/50 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100 disabled:hover:shadow-none"
        >
          <ChevronRight className="h-4 w-4" />
        </Button>

        {/* 末页 */}
        <Button
          variant="outline"
          size="sm"
          onClick={() => onPageChange(totalPages)}
          disabled={currentPage === totalPages}
          className="h-8 w-8 p-0 transition-all duration-300 ease-out hover:scale-105 hover:shadow-md hover:border-primary/50 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100 disabled:hover:shadow-none"
        >
          <ChevronsRight className="h-4 w-4" />
        </Button>
      </div>

      {/* 快速跳转 */}
      {showQuickJumper && (
        <div className="flex items-center gap-2 text-sm">
          <span className="text-muted-foreground">跳转到</span>
          <Input
            type="number"
            min={1}
            max={totalPages}
            value={jumpPage}
            onChange={(e) => setJumpPage(e.target.value)}
            onKeyPress={handleKeyPress}
            className="h-8 w-16 text-center transition-all duration-300 ease-out border-input hover:border-primary/50 focus:border-primary focus:ring-2 focus:ring-primary/20 focus:scale-105"
            placeholder={currentPage.toString()}
          />
          <span className="text-muted-foreground">页</span>
          <Button
            variant="outline"
            size="sm"
            onClick={handleJumpToPage}
            disabled={!jumpPage || parseInt(jumpPage) < 1 || parseInt(jumpPage) > totalPages}
            className="h-8 transition-all duration-300 ease-out hover:scale-105 hover:shadow-md hover:border-primary/50 hover:bg-primary/5 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100 disabled:hover:shadow-none"
          >
            跳转
          </Button>
        </div>
      )}

      {/* 每页条数选择器 */}
      {showSizeChanger && onPageSizeChange && (
        <div className="flex items-center gap-2 text-sm">
          <span className="text-muted-foreground">每页</span>
          <select
            value={pageSize}
            onChange={(e) => onPageSizeChange(parseInt(e.target.value))}
            className="h-8 px-2 border border-input bg-background rounded-md text-sm transition-all duration-300 ease-out hover:border-primary/50 focus:border-primary focus:ring-2 focus:ring-primary/20 focus:scale-105 cursor-pointer"
          >
            {pageSizeOptions.map(size => (
              <option key={size} value={size}>{size}</option>
            ))}
          </select>
          <span className="text-muted-foreground">条</span>
        </div>
      )}
    </div>
  )
}
