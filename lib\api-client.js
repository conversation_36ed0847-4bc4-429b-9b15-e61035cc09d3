/**
 * PHP API 客户端
 * 处理与PHP后端API的通信
 */

// API基础URL - 需要根据实际部署地址修改
const API_BASE_URL = process.env.NEXT_PUBLIC_PHP_API_URL || 'https://api.xstty.com/api';

/**
 * 通用API请求函数
 */
async function apiRequest(endpoint, options = {}) {
  const url = `${API_BASE_URL}/${endpoint}`;
  
  const defaultOptions = {
    headers: {
      'Content-Type': 'application/json',
    },
  };

  const config = {
    ...defaultOptions,
    ...options,
    headers: {
      ...defaultOptions.headers,
      ...options.headers,
    },
  };

  try {
    const response = await fetch(url, config);
    
    // 检查HTTP状态码
    if (!response.ok) {
      const errorText = await response.text();

      let errorMessage;
      try {
        const errorJson = JSON.parse(errorText);
        errorMessage = errorJson.message || errorJson.error || `请求失败 (${response.status})`;
      } catch (e) {
        errorMessage = `请求失败 (${response.status}): ${errorText.substring(0, 100)}`;
      }
      
      throw new Error(errorMessage);
    }

    const contentType = response.headers.get('content-type');
    if (!contentType || !contentType.includes('application/json')) {
      throw new Error('API返回格式错误，预期JSON但收到: ' + contentType);
    }

    const data = await response.json();

    if (!data.success) {
      throw new Error(data.message || data.error || '请求失败');
    }

    return data;
  } catch (error) {
    throw error;
  }
}

/**
 * 关键词相关API
 */
export const keywordAPI = {
  /**
   * 获取关键词列表
   */
  async getList(params = {}) {
    const queryString = new URLSearchParams(params).toString();
    const endpoint = queryString ? `keywords.php?${queryString}` : 'keywords.php';
    return apiRequest(endpoint);
  },

  /**
   * 获取单个关键词
   */
  async getById(id) {
    return apiRequest(`keywords.php?id=${id}`);
  },

  /**
   * 创建关键词
   */
  async create(keywordData) {
    return apiRequest('keywords.php', {
      method: 'POST',
      body: JSON.stringify(keywordData),
    });
  },

  /**
   * 更新关键词
   */
  async update(id, keywordData) {
    return apiRequest(`keywords.php?id=${id}`, {
      method: 'PUT',
      body: JSON.stringify(keywordData),
    });
  },

  /**
   * 删除关键词
   */
  async delete(id) {
    return apiRequest(`keywords.php?id=${id}`, {
      method: 'DELETE',
    });
  },

  /**
   * 批量导入关键词
   */
  async batchImport(keywords, filename = 'batch_import', aiFilterStats = null) {
    return apiRequest('import.php', {
      method: 'POST',
      body: JSON.stringify({
        keywords,
        filename,
        ai_filter_stats: aiFilterStats,
      }),
    });
  },
};

/**
 * 分类相关API
 */
export const categoryAPI = {
  /**
   * 获取分类列表
   */
  async getList() {
    return apiRequest('categories.php');
  },
};

/**
 * 统计相关API
 */
export const statsAPI = {
  /**
   * 获取关键词统计信息
   */
  async getKeywordStats() {
    return apiRequest('keywords.php?stats=1');
  },
};

/**
 * 工具函数
 */
export const apiUtils = {
  /**
   * 处理API错误
   */
  handleError(error) {
    if (error.response?.data?.message) {
      return error.response.data.message;
    }
    return error.message || '未知错误';
  },

  /**
   * 格式化关键词数据用于导入
   */
  formatKeywordsForImport(rawKeywords) {
    return rawKeywords.map(item => ({
      keyword: item.keyword,
      source: item.source || 'google_trends',
      import_date: item.importDate || new Date().toISOString().split('T')[0],
      user_intent: item.userIntent || null,
      user_pain_point: item.userPainPoint || null,
      competition_level: item.competitionLevel || null,
      competition_score: item.competitionScore || null,
      competition_color: item.competitionColor || null,
      competition_description: item.competitionDescription || null,
      category: item.category || null,
    }));
  },

  /**
   * 构建查询参数
   */
  buildQueryParams(filters) {
    const params = {};
    
    if (filters.category) params.category = filters.category;
    if (filters.source) params.source = filters.source;
    if (filters.import_date) params.import_date = filters.import_date;
    if (filters.search) params.search = filters.search;
    if (filters.page) params.page = filters.page;
    if (filters.limit) params.limit = filters.limit;
    
    return params;
  },
};



/**
 * 默认导出
 */
export default {
  keywordAPI,
  categoryAPI,
  statsAPI,
  apiUtils,
};
