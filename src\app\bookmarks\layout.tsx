import type { Metadata } from "next";

export const metadata: Metadata = {
  title: "书签导航 | CatchIdeas",
  description: "精选优质网站资源导航，涵盖AI工具、开发技术、支付平台、营销工具等9大分类，助力您的工作与学习效率提升。专业的互联网项目分析平台。",
  authors: [{ name: "CatchIdeas团队" }],
  creator: "CatchIdeas",
  publisher: "CatchIdeas",
  robots: "index, follow",
  openGraph: {
    type: "website",
    locale: "zh_CN",
    url: "https://catchideas.com/bookmarks",
    title: "书签导航 | CatchIdeas",
    description: "精选优质网站资源导航，涵盖AI工具、开发技术、支付平台、营销工具等9大分类，助力您的工作与学习效率提升。专业的互联网项目分析平台。",
    siteName: "CatchIdeas",
  },
  twitter: {
    card: "summary_large_image",
    title: "书签导航 | CatchIdeas",
    description: "精选优质网站资源导航，涵盖AI工具、开发技术、支付平台、营销工具等9大分类，助力您的工作与学习效率提升。专业的互联网项目分析平台。",
  },
  alternates: {
    canonical: "https://catchideas.com/bookmarks",
  },
};

export default function BookmarksLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}
