<?php
require_once '../utils/Response.php';
require_once '../models/Keyword.php';

/**
 * 关键词分析API
 * 处理关键词AI分析结果的保存和更新
 */

// 处理CORS预检请求
Response::handleOptions();

// 验证请求方法
$method = Response::validateMethod(['POST', 'PUT']);

try {
    $keywordModel = new Keyword();
    
    switch ($method) {
        case 'POST':
            handleBatchAnalyze($keywordModel);
            break;
            
        case 'PUT':
            handleUpdateAnalysis($keywordModel);
            break;
    }
    
} catch (Exception $e) {
    Response::serverError('分析操作失败: ' . $e->getMessage());
}

/**
 * 生成SEO友好的slug ID
 */
function generateSlugId($keyword) {
    // 基础slug生成
    $slug = strtolower(trim($keyword));

    // 移除特殊字符，只保留字母、数字、空格、连字符
    $slug = preg_replace('/[^\w\s\-]/u', '', $slug);

    // 将多个空格替换为单个连字符
    $slug = preg_replace('/\s+/', '-', $slug);

    // 移除多个连续的连字符
    $slug = preg_replace('/\-+/', '-', $slug);

    // 移除开头和结尾的连字符
    $slug = trim($slug, '-');

    // 如果slug为空或太短，使用fallback
    if (empty($slug) || strlen($slug) < 2) {
        $slug = 'keyword-' . time() . '-' . mt_rand(100, 999);
    }

    // 限制长度
    if (strlen($slug) > 50) {
        $slug = substr($slug, 0, 50);
        $slug = rtrim($slug, '-');
    }

    return $slug;
}

/**
 * 处理批量分析结果保存
 */
function handleBatchAnalyze($keywordModel) {
    $data = Response::getRequestData();
    
    // 验证必需字段
    if (!isset($data['results']) || !is_array($data['results'])) {
        Response::validationError(['results' => '分析结果数组不能为空']);
    }
    
    if (empty($data['results'])) {
        Response::validationError(['results' => '至少需要一个分析结果']);
    }
    
    $successCount = 0;
    $errorCount = 0;
    $errors = [];
    $processed = [];
    
    foreach ($data['results'] as $index => $result) {
        try {
            // 验证结果格式
            if (!isset($result['keyword']) || !isset($result['analysis'])) {
                $errors[] = "第" . ($index + 1) . "个结果缺少必需字段";
                $errorCount++;
                continue;
            }
            
            $keyword = $result['keyword'];
            $analysis = $result['analysis'];

            // 检查关键词是否存在
            $existingKeyword = $keywordModel->getByKeyword($keyword);
            $keywordId = null;

            if (!$existingKeyword) {
                // 关键词不存在，创建新的
                $keywordId = generateSlugId($keyword);
                $createData = [
                    'id' => $keywordId,
                    'keyword' => $keyword,
                    'user_intent' => $analysis['user_intent'] ?? null,
                    'user_pain_point' => $analysis['user_pain_point'] ?? null,
                    'competition_level' => $analysis['competition_level'] ?? null,
                    'competition_score' => $analysis['competition_score'] ?? null,
                    'competition_color' => $analysis['competition_color'] ?? null,
                    'competition_description' => $analysis['competition_description'] ?? null,
                    'serp_analysis' => $analysis['serp_analysis'] ?? null,
                    'category' => $analysis['category'] ?? null,
                    'source' => 'ai_analysis',
                    'import_date' => date('Y-m-d'),
                    'analyzed_at' => date('Y-m-d H:i:s')
                ];

                $success = $keywordModel->create($createData);

            } else {
                // 关键词存在，检查是否已经分类且锁定
                if ($existingKeyword['category_locked']) {
                    $errors[] = "关键词 '{$keyword}' 已分类锁定，无法重新分析";
                    $errorCount++;
                    continue;
                }

                $keywordId = $existingKeyword['id'];

                // 准备更新数据
                $updateData = [
                    'user_intent' => $analysis['user_intent'] ?? null,
                    'user_pain_point' => $analysis['user_pain_point'] ?? null,
                    'competition_level' => $analysis['competition_level'] ?? null,
                    'competition_score' => $analysis['competition_score'] ?? null,
                    'competition_color' => $analysis['competition_color'] ?? null,
                    'competition_description' => $analysis['competition_description'] ?? null,
                    'serp_analysis' => $analysis['serp_analysis'] ?? null,
                    'category' => $analysis['category'] ?? null,
                    'analyzed_at' => date('Y-m-d H:i:s')
                ];

                // 更新关键词
                $success = $keywordModel->update($existingKeyword['id'], $updateData);
            }
            
            if ($success) {
                $successCount++;
                $processed[] = [
                    'id' => $keywordId,
                    'keyword' => $keyword,
                    'status' => 'success',
                    'category' => $analysis['category'] ?? null
                ];
            } else {
                $errorCount++;
                $errors[] = "关键词 '{$keyword}' 更新失败";
            }
            
        } catch (Exception $e) {
            $errorCount++;
            $errors[] = "处理关键词 '{$keyword}' 时发生错误: " . $e->getMessage();
        }
    }
    

    
    // 返回结果
    Response::success([
        'batch_analysis' => [
            'total_submitted' => count($data['results']),
            'successful_updates' => $successCount,
            'failed_updates' => $errorCount,
            'success_rate' => count($data['results']) > 0 ? 
                round(($successCount / count($data['results'])) * 100, 2) . '%' : '0%',
            'analysis_time' => date('Y-m-d H:i:s')
        ],
        'processed' => $processed,
        'errors' => $errors
    ], $successCount > 0 ? 
        "成功分析 {$successCount} 个关键词，失败 {$errorCount} 个" : 
        "批量分析完成，但没有成功更新的关键词");
}

/**
 * 处理单个关键词分析更新
 */
function handleUpdateAnalysis($keywordModel) {
    if (!isset($_GET['id'])) {
        Response::validationError(['id' => '关键词ID不能为空']);
    }
    
    $id = $_GET['id'];
    $data = Response::getRequestData();
    
    // 检查关键词是否存在
    $existingKeyword = $keywordModel->getById($id);
    if (!$existingKeyword) {
        Response::notFound('关键词不存在');
    }
    
    // 检查是否已经分类且锁定
    if ($existingKeyword['category_locked'] && isset($data['category'])) {
        Response::error('该关键词的分类已锁定，无法修改', 403);
    }
    
    // 准备更新数据
    $updateData = [];
    $allowedFields = [
        'user_intent', 'user_pain_point', 'competition_level', 
        'competition_score', 'competition_color', 'competition_description',
        'serp_analysis', 'category', 'user_comment', 'notes'
    ];
    
    foreach ($allowedFields as $field) {
        if (isset($data[$field])) {
            $updateData[$field] = $data[$field];
        }
    }
    
    if (empty($updateData)) {
        Response::validationError(['data' => '没有有效的更新字段']);
    }
    
    // 添加分析时间
    $updateData['analyzed_at'] = date('Y-m-d H:i:s');
    
    // 更新关键词
    $success = $keywordModel->update($id, $updateData);
    
    if (!$success) {
        Response::serverError('关键词分析更新失败');
    }
    
    // 获取更新后的关键词
    $updatedKeyword = $keywordModel->getById($id);
    Response::success($updatedKeyword, '关键词分析更新成功');
}

/**
 * 生成批次ID
 */
function generateBatchId() {
    return 'batch_' . date('Ymd_His') . '_' . substr(md5(uniqid()), 0, 8);
}

/**
 * 记录分析日志
 */
function logAnalysis($logData) {
    try {
        $database = new Database();
        $conn = $database->getConnection();
        
        $query = "INSERT INTO analysis_logs 
                  (batch_id, total_submitted, successful_updates, failed_updates, 
                   analysis_date, errors, created_at) 
                  VALUES 
                  (:batch_id, :total_submitted, :successful_updates, :failed_updates, 
                   :analysis_date, :errors, NOW())";
        
        $stmt = $conn->prepare($query);
        $stmt->execute([
            ':batch_id' => $logData['batch_id'],
            ':total_submitted' => $logData['total_submitted'],
            ':successful_updates' => $logData['successful_updates'],
            ':failed_updates' => $logData['failed_updates'],
            ':analysis_date' => $logData['analysis_date'],
            ':errors' => $logData['errors']
        ]);
    } catch (Exception $e) {
        // 静默处理日志错误
    }
}
?>
