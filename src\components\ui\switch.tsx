"use client"

import * as React from "react"
import * as SwitchPrimitives from "@radix-ui/react-switch"

import { cn } from "../../lib/utils"

const Switch = React.forwardRef<
  React.ElementRef<typeof SwitchPrimitives.Root>,
  React.ComponentPropsWithoutRef<typeof SwitchPrimitives.Root>
>(({ className, ...props }, ref) => (
  <SwitchPrimitives.Root
    className={cn(
      // 基础样式
      "peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2",
      // 过渡动画
      "transition-all duration-300 ease-in-out",
      // 焦点样式
      "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-green-500 focus-visible:ring-offset-2 focus-visible:ring-offset-background",
      // 禁用状态
      "disabled:cursor-not-allowed disabled:opacity-50",
      // 选中状态（开启）- 绿色边框 + 绿色背景（颜色一致）
      "data-[state=checked]:bg-green-500 data-[state=checked]:border-green-500",
      // 未选中状态（关闭）- 黑色边框 + 灰色背景
      "data-[state=unchecked]:bg-gray-300 data-[state=unchecked]:border-black",
      // 深色模式下的未选中状态
      "dark:data-[state=unchecked]:bg-gray-600 dark:data-[state=unchecked]:border-black",
      // 悬停效果
      "hover:shadow-md",
      className
    )}
    {...props}
    ref={ref}
  >
    <SwitchPrimitives.Thumb
      className={cn(
        // 基础样式 - 白色滑块确保对比度
        "pointer-events-none block h-5 w-5 rounded-full bg-white shadow-lg ring-0",
        // 平滑过渡动画
        "transition-all duration-300 ease-in-out",
        // 位置变换
        "data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0",
        // 悬停时的微妙缩放效果
        "group-hover:scale-105"
      )}
    />
  </SwitchPrimitives.Root>
))
Switch.displayName = SwitchPrimitives.Root.displayName

export { Switch }
