/**
 * CSV导入API - 处理Google Trends CSV文件上传和导入
 */

import { IncomingForm } from 'formidable'
import fs from 'fs'
import path from 'path'
import os from 'os'
import { v4 as uuidv4 } from 'uuid'
import { parseCSV, aiFilterKeywords, saveToAPI } from '../../../lib/csv-parser.js'

// 禁用默认的body解析器
export const config = {
  api: {
    bodyParser: false,
  },
}

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      error: 'Method not allowed. Use POST.'
    })
  }

  try {
    // 解析上传的文件
    const { files } = await parseFormData(req).catch(error => {
      throw new Error(`文件上传失败: ${error.message}`)
    })
    
    if (!files.csvFile) {
      return res.status(400).json({
        success: false,
        error: '请选择CSV文件上传'
      })
    }

    const csvFile = Array.isArray(files.csvFile) ? files.csvFile[0] : files.csvFile
    
    // 验证文件类型
    if (!csvFile.originalFilename?.toLowerCase().endsWith('.csv')) {
      return res.status(400).json({
        success: false,
        error: '请上传CSV格式文件'
      })
    }

    // 读取文件内容
    let csvContent
    try {
      csvContent = fs.readFileSync(csvFile.filepath, 'utf8')
    } catch (error) {
      throw new Error(`文件读取失败: ${error.message}`)
    }

    // 生成随机文件名，避免Google Trends同名文件冲突
    const originalName = csvFile.originalFilename || 'unknown.csv'
    const fileExtension = path.extname(originalName)
    const baseName = path.basename(originalName, fileExtension)
    const randomFilename = `${baseName}_${uuidv4().substring(0, 8)}_${Date.now()}${fileExtension}`

    // 解析CSV
    const parseResult = parseCSV(csvContent, randomFilename)
    
    if (!parseResult.success) {
      return res.status(400).json({
        success: false,
        error: `CSV解析失败: ${parseResult.error}`
      })
    }

    // 直接进行AI智能过滤，不再使用系统预过滤
    const aiFilterResult = await aiFilterKeywords(parseResult.rawKeywords)

    // 如果AI过滤失败，返回错误
    if (!aiFilterResult.success) {
      return res.status(503).json({
        success: false,
        error: aiFilterResult.error || 'AI过滤服务暂时不可用'
      })
    }

    // 保存到PHP API (只保存通过AI过滤的关键词)
    const apiSaveResult = await saveToAPI(parseResult, aiFilterResult.accepted, aiFilterResult.stats)

    if (!apiSaveResult.success) {
      return res.status(500).json({
        success: false,
        error: `数据保存失败: ${apiSaveResult.error}`
      })
    }

    // 清理临时文件
    try {
      fs.unlinkSync(csvFile.filepath)
    } catch {
      // 继续执行，不影响主流程
    }

    // 返回处理结果
    res.status(200).json({
      success: true,
      data: {
        parse: {
          totalLines: parseResult.totalLines,
          totalKeywords: parseResult.validKeywords
        },
        import: {
          filename: parseResult.filename,
          totalLines: parseResult.totalLines,
          rawKeywords: parseResult.validKeywords,
          importedAt: parseResult.importedAt
        },
        aiFilter: {
          total: aiFilterResult.stats.total,
          accepted: aiFilterResult.stats.accepted,
          rejected: aiFilterResult.stats.rejected,
          passRate: aiFilterResult.stats.total > 0 ? ((aiFilterResult.stats.accepted / aiFilterResult.stats.total) * 100).toFixed(2) + '%' : '0%',
          rejectionReasons: {
            brands: aiFilterResult.stats.rejectionReasons?.brand || 0,
            genericWords: aiFilterResult.stats.rejectionReasons?.generic || 0,
            personNames: aiFilterResult.stats.rejectionReasons?.person_name || 0,
            nonEnglish: aiFilterResult.stats.rejectionReasons?.non_english || 0,
            meaningless: aiFilterResult.stats.rejectionReasons?.meaningless || 0,
            tooCompetitive: aiFilterResult.stats.rejectionReasons?.too_competitive || 0,
            noSeoValue: aiFilterResult.stats.rejectionReasons?.no_seo_value || 0,
            serviceError: aiFilterResult.stats.rejectionReasons?.ai_service_error || 0,
            other: aiFilterResult.stats.rejectionReasons?.other || 0
          },
          rejectedKeywords: {
            brands: aiFilterResult.rejected.filter(k => k.rejectionReason === 'brand').map(k => k.keyword).slice(0, 20),
            genericWords: aiFilterResult.rejected.filter(k => k.rejectionReason === 'generic').map(k => k.keyword).slice(0, 20),
            personNames: aiFilterResult.rejected.filter(k => k.rejectionReason === 'person_name').map(k => k.keyword).slice(0, 20),
            nonEnglish: aiFilterResult.rejected.filter(k => k.rejectionReason === 'non_english').map(k => k.keyword).slice(0, 20),
            meaningless: aiFilterResult.rejected.filter(k => k.rejectionReason === 'meaningless').map(k => k.keyword).slice(0, 20),
            tooCompetitive: aiFilterResult.rejected.filter(k => k.rejectionReason === 'too_competitive').map(k => k.keyword).slice(0, 20),
            noSeoValue: aiFilterResult.rejected.filter(k => k.rejectionReason === 'no_seo_value').map(k => k.keyword).slice(0, 20),
            serviceError: aiFilterResult.rejected.filter(k => k.rejectionReason === 'ai_service_error').map(k => k.keyword).slice(0, 20),
            other: aiFilterResult.rejected.filter(k => k.rejectionReason === 'other').map(k => k.keyword).slice(0, 20)
          }
        },
        api: {
          totalImported: apiSaveResult.totalImported,
          duplicates: apiSaveResult.duplicates?.length || 0,
          apiErrors: apiSaveResult.errors?.length || 0
        }
      },
      message: `成功导入 ${apiSaveResult.totalImported} 个关键词，AI过滤 ${aiFilterResult.stats.rejected} 个，数据库去重 ${apiSaveResult.duplicates?.length || 0} 个`
    })

  } catch (error) {
    // 确保错误响应始终是JSON格式
    res.status(500).json({
      success: false,
      error: error.message || '服务器内部错误'
    })
  }
}

/**
 * 解析表单数据
 * @param {Object} req - 请求对象
 * @returns {Promise<Object>} 解析结果
 */
function parseFormData(req) {
  return new Promise((resolve, reject) => {
    // 使用系统临时目录，避免在Vercel等无服务器环境中创建目录
    const uploadDir = os.tmpdir()

    const form = new IncomingForm({
      uploadDir: uploadDir,
      keepExtensions: true,
      maxFileSize: 10 * 1024 * 1024, // 10MB
      maxFiles: 1
    })

    form.parse(req, (err, fields, files) => {
      if (err) {
        reject(err)
      } else {
        resolve({ fields, files })
      }
    })
  })
}
