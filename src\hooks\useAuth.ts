'use client'

import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { useEffect } from 'react'

export interface AuthUser {
  name?: string | null
  email?: string | null
  image?: string | null
  role?: 'admin' | 'user'
}

export interface UseAuthReturn {
  user: AuthUser | null
  isLoading: boolean
  isAuthenticated: boolean
  isAdmin: boolean
  requireAuth: () => void
  requireAdmin: () => void
}

export function useAuth(): UseAuthReturn {
  const { data: session, status } = useSession()
  const router = useRouter()

  const user = session?.user as AuthUser | null
  const isLoading = status === 'loading'
  const isAuthenticated = !!session
  const isAdmin = user?.role === 'admin'

  const requireAuth = () => {
    if (!isLoading && !isAuthenticated) {
      router.push('/auth/signin')
    }
  }

  const requireAdmin = () => {
    if (!isLoading) {
      if (!isAuthenticated) {
        router.push('/auth/signin')
      } else if (!isAdmin) {
        router.push('/unauthorized')
      }
    }
  }

  return {
    user,
    isLoading,
    isAuthenticated,
    isAdmin,
    requireAuth,
    requireAdmin
  }
}

// 权限检查Hook，用于页面级权限控制
export function useRequireAuth() {
  const auth = useAuth()

  useEffect(() => {
    auth.requireAuth()
  }, [auth, auth.isAuthenticated, auth.isLoading])

  return auth
}

// 管理员权限检查Hook
export function useRequireAdmin() {
  const auth = useAuth()

  useEffect(() => {
    auth.requireAdmin()
  }, [auth, auth.isAuthenticated, auth.isAdmin, auth.isLoading])

  return auth
}
