import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '../../../../pages/api/auth/[...nextauth]'

export async function GET(request: NextRequest) {
  try {
    // 检查用户登录状态
    const session = await getServerSession(authOptions)
    
    if (!session || !session.user?.email) {
      return NextResponse.json({
        success: false,
        error: '请先登录'
      }, { status: 401 })
    }

    const userEmail = session.user.email
    const isAdmin = userEmail === '<EMAIL>'
    const today = new Date().toISOString().split('T')[0]

    // 管理员不限制使用次数
    if (isAdmin) {
      return NextResponse.json({
        success: true,
        data: {
          userEmail,
          isAdmin: true,
          dailyUsage: 0,
          dailyLimit: Infinity,
          date: today
        }
      })
    }

    try {
      // 获取用户今日使用次数
      const apiBaseUrl = process.env.NEXT_PUBLIC_PHP_API_URL || 'https://api.xstty.com/api'
      const response = await fetch(`${apiBaseUrl}/user-usage.php?email=${encodeURIComponent(userEmail)}&date=${today}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        }
      })

      let dailyUsage = 0
      
      if (response.ok) {
        const data = await response.json()
        if (data.success && data.data) {
          dailyUsage = Number(data.data.usage_count) || 0
        }
      }

      return NextResponse.json({
        success: true,
        data: {
          userEmail,
          isAdmin: false,
          dailyUsage,
          dailyLimit: 10,
          date: today
        }
      })

    } catch (error) {
      // 如果获取失败，返回默认值
      return NextResponse.json({
        success: true,
        data: {
          userEmail,
          isAdmin: false,
          dailyUsage: 0,
          dailyLimit: 10,
          date: today
        }
      })
    }

  } catch (error: any) {
    console.error('User usage stats error:', error)
    
    return NextResponse.json({
      success: false,
      error: '服务器内部错误'
    }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    // 检查用户登录状态
    const session = await getServerSession(authOptions)
    
    if (!session || !session.user?.email) {
      return NextResponse.json({
        success: false,
        error: '请先登录'
      }, { status: 401 })
    }

    const userEmail = session.user.email
    const isAdmin = userEmail === '<EMAIL>'

    // 管理员不记录使用次数
    if (isAdmin) {
      return NextResponse.json({
        success: true,
        message: '管理员账户无需记录使用次数'
      })
    }

    const { count = 1 } = await request.json()
    const today = new Date().toISOString().split('T')[0]

    try {
      // 记录用户使用次数
      const apiBaseUrl = process.env.NEXT_PUBLIC_PHP_API_URL || 'https://api.xstty.com/api'
      const response = await fetch(`${apiBaseUrl}/user-usage.php`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: userEmail,
          count: count,
          date: today
        })
      })

      if (response.ok) {
        const data = await response.json()
        return NextResponse.json({
          success: true,
          data: data.data || {},
          message: '使用次数记录成功'
        })
      } else {
        return NextResponse.json({
          success: false,
          error: '记录使用次数失败'
        }, { status: 500 })
      }

    } catch (error) {
      return NextResponse.json({
        success: false,
        error: '记录使用次数失败'
      }, { status: 500 })
    }

  } catch (error: any) {
    console.error('Record user usage error:', error)
    
    return NextResponse.json({
      success: false,
      error: '服务器内部错误'
    }, { status: 500 })
  }
}
