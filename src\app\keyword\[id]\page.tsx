'use client'

import { useState, useEffect, useMemo } from 'react'
import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation'


import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../../components/ui/card'
import { Badge } from '../../../components/ui/badge'
import { Button } from '../../../components/ui/button'

import { Toaster, toast } from 'sonner'
import {
  ArrowLeft,
  ExternalLink,
  Target,
  TrendingUp,
  Globe,
  Star,
  User,
  AlertTriangle,
  BarChart3,
  Search,
  Loader2,
  Navigation,
  Palette,
  Download,
  Copy,
  RotateCcw
} from 'lucide-react'
import FrontendHeader from '../../../components/frontend-header'
import FrontendFooter from '../../../components/frontend-footer'
import { ClientSideReportGenerator } from '../../../../lib/client-report-generator'

// 类型定义
interface ReportContent {
  type: 'list' | 'bold' | 'text'
  text: string
}

interface ReportSection {
  title: string
  emoji: string
  type: string
  content: ReportContent[]
}

// 解析报告内容为结构化数据
function parseReportSections(reportText: string): ReportSection[] {
  if (!reportText) return []

  const sections: ReportSection[] = []
  const lines = reportText.split('\n').filter(line => line.trim())

  let currentSection: ReportSection | null = null

  for (const line of lines) {
    const trimmedLine = line.trim()

    // 检测标题行 (### 开头)
    if (trimmedLine.startsWith('###')) {
      // 保存上一个section
      if (currentSection) {
        sections.push(currentSection)
      }

      // 创建新section
      const title = trimmedLine.replace(/^###\s*/, '').trim()
      const { emoji, cleanTitle, type } = extractEmojiAndType(title)

      currentSection = {
        title: cleanTitle,
        emoji: emoji,
        type: type,
        content: []
      }
    }
    // 检测列表项 (- 或 * 开头)
    else if (trimmedLine.match(/^[-*]\s+/)) {
      if (currentSection) {
        const text = trimmedLine.replace(/^[-*]\s+/, '').trim()
        currentSection.content.push({
          type: 'list',
          text: cleanMarkdown(text)
        })
      }
    }
    // 检测粗体文本 (**text**)
    else if (trimmedLine.includes('**')) {
      if (currentSection) {
        const text = trimmedLine.replace(/\*\*/g, '').trim()
        if (text) {
          currentSection.content.push({
            type: 'bold',
            text: cleanMarkdown(text)
          })
        }
      }
    }
    // 普通文本
    else if (trimmedLine && !trimmedLine.startsWith('#')) {
      if (currentSection) {
        currentSection.content.push({
          type: 'text',
          text: cleanMarkdown(trimmedLine)
        })
      }
    }
  }

  // 添加最后一个section
  if (currentSection) {
    sections.push(currentSection)
  }

  return sections.filter(section => section.content.length > 0)
}

// 提取emoji和确定卡片类型
function extractEmojiAndType(title: string) {
  // 检测标题中是否已经包含emoji
  const emojiRegex = /[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]/u
  const hasEmoji = emojiRegex.test(title)

  if (hasEmoji) {
    // 如果标题已有emoji，提取第一个emoji并移除它，避免重复
    const emojiMatch = title.match(emojiRegex)
    const extractedEmoji = emojiMatch ? emojiMatch[0] : '📋'
    const cleanTitle = title.replace(emojiRegex, '').trim()

    // 根据内容确定类型
    const typeMap = {
      '关键词': 'keyword',
      '相似词': 'similarity',
      '相关': 'similarity',
      '竞争': 'competition',
      '市场': 'market',
      '内容': 'content',
      '策略': 'strategy',
      '建议': 'suggestion',
      '机会': 'opportunity',
      '分析': 'analysis',
      '趋势': 'trend',
      '用户': 'user',
      '受众': 'user',
      '变现': 'monetization',
      '优势': 'advantage'
    }

    let detectedType = 'default'
    for (const [keyword, type] of Object.entries(typeMap)) {
      if (title.includes(keyword)) {
        detectedType = type
        break
      }
    }

    return {
      emoji: extractedEmoji,
      cleanTitle: cleanTitle,
      type: detectedType
    }
  }

  // 如果没有emoji，使用原来的逻辑
  const emojiMap = {
    '关键词': { emoji: '🔍', type: 'keyword' },
    '相似词': { emoji: '🔗', type: 'similarity' },
    '相关': { emoji: '🔗', type: 'similarity' },
    '竞争': { emoji: '⚔️', type: 'competition' },
    '市场': { emoji: '📈', type: 'market' },
    '内容': { emoji: '📝', type: 'content' },
    '策略': { emoji: '🎯', type: 'strategy' },
    '建议': { emoji: '💡', type: 'suggestion' },
    '机会': { emoji: '🚀', type: 'opportunity' },
    '分析': { emoji: '📊', type: 'analysis' },
    '趋势': { emoji: '📈', type: 'trend' },
    '用户': { emoji: '👥', type: 'user' },
    '受众': { emoji: '👥', type: 'user' },
    '变现': { emoji: '💰', type: 'monetization' },
    '优势': { emoji: '⭐', type: 'advantage' }
  }

  // 查找匹配的关键词
  for (const [keyword, config] of Object.entries(emojiMap)) {
    if (title.includes(keyword)) {
      return {
        emoji: config.emoji,
        cleanTitle: title,
        type: config.type
      }
    }
  }

  // 默认配置
  return {
    emoji: '📋',
    cleanTitle: title,
    type: 'default'
  }
}

// 清理Markdown符号
function cleanMarkdown(text: string) {
  return text
    .replace(/\\n/g, ' ')  // 将\n转换为空格
    .replace(/\n/g, ' ')   // 将换行符转换为空格
    .replace(/\*\*/g, '') // 移除粗体标记
    .replace(/\*/g, '')   // 移除斜体标记
    .replace(/#{1,6}\s*/g, '') // 移除标题标记
    .replace(/`/g, '')    // 移除代码标记
    .replace(/\s+/g, ' ') // 将多个空格合并为一个
    .trim()
}

// 格式化分析字段为列表格式的函数
function formatAnalysisField(content: string | any): string[] {
  if (!content) return []

  try {
    // 如果是JSON字符串，尝试解析
    if (typeof content === 'string') {
      // 检查是否是JSON格式
      if (content.trim().startsWith('[') || content.trim().startsWith('{')) {
        try {
          const parsed = JSON.parse(content)
          if (Array.isArray(parsed)) {
            return parsed.map(item =>
              typeof item === 'string' ? item.replace(/^\d+[\.\)]\s*/, '').trim() : item
            )
          }
        } catch (e) {
          // 继续处理为文本
        }
      }

      // 按行分割并清理
      return content
        .split(/\n|。|；/)
        .map(line => line.trim())
        .filter(line => line.length > 0 && !line.match(/^[\d\s\-\*\•]+$/))
        .map(line => {
          // 移除开头的数字编号 (1. 2. 3. 或 1) 2) 3) 等)
          return line.replace(/^\d+[\.\)]\s*/, '').replace(/^[\d\s\-\*\•]+/, '').trim()
        })
        .filter(line => line.length > 10) // 过滤太短的内容
    }

    // 如果已经是数组
    if (Array.isArray(content)) {
      return content.map(item =>
        typeof item === 'string' ? item.replace(/^\d+[\.\)]\s*/, '').trim() : item
      )
    }

    return []
  } catch (error) {
    return []
  }
}

// 域名状态显示组件
const DomainStatusIndicator = ({ status, message }: { status: string, message: string }) => {
  const getStatusDisplay = () => {
    switch (status) {
      case 'has_records':
      case 'has_cname':
      case 'has_mx':
        return { text: '已注册', color: 'text-red-600', bgColor: 'bg-red-50', icon: '❌' }
      case 'no_records':
        return { text: '可能可用', color: 'text-green-600', bgColor: 'bg-green-50', icon: '✅' }
      case 'timeout':
        return { text: '查询超时', color: 'text-yellow-600', bgColor: 'bg-yellow-50', icon: '⏱️' }
      case 'query_failed':
      case 'error':
        return { text: '查询失败', color: 'text-gray-600', bgColor: 'bg-gray-50', icon: '❓' }
      default:
        return { text: '未知状态', color: 'text-gray-600', bgColor: 'bg-gray-50', icon: '❓' }
    }
  }

  const { text, color, bgColor, icon } = getStatusDisplay()

  return (
    <div className={`inline-flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium ${color} ${bgColor}`}>
      <span>{icon}</span>
      <span>{text}</span>
    </div>
  )
}

// 获取卡片样式
function getSectionCardStyle(type: string) {
  const styles: Record<string, string> = {
    keyword: 'bg-blue-50 border-blue-200',
    similarity: 'bg-purple-50 border-purple-200',
    competition: 'bg-red-50 border-red-200',
    market: 'bg-green-50 border-green-200',
    content: 'bg-yellow-50 border-yellow-200',
    strategy: 'bg-indigo-50 border-indigo-200',
    suggestion: 'bg-orange-50 border-orange-200',
    opportunity: 'bg-emerald-50 border-emerald-200',
    analysis: 'bg-gray-50 border-gray-200',
    trend: 'bg-teal-50 border-teal-200',
    user: 'bg-pink-50 border-pink-200',
    monetization: 'bg-amber-50 border-amber-200',
    advantage: 'bg-cyan-50 border-cyan-200',
    default: 'bg-gray-50 border-gray-200'
  }
  return styles[type] || styles.default
}

// 获取标题样式
function getSectionTitleStyle(type: string) {
  const styles: Record<string, string> = {
    keyword: 'text-blue-900',
    similarity: 'text-purple-900',
    competition: 'text-red-900',
    market: 'text-green-900',
    content: 'text-yellow-900',
    strategy: 'text-indigo-900',
    suggestion: 'text-orange-900',
    opportunity: 'text-emerald-900',
    analysis: 'text-gray-900',
    trend: 'text-teal-900',
    user: 'text-pink-900',
    monetization: 'text-amber-900',
    advantage: 'text-cyan-900',
    default: 'text-gray-900'
  }
  return styles[type] || styles.default
}

// 获取内容样式
function getSectionContentStyle(type: string) {
  const styles: Record<string, string> = {
    keyword: 'text-blue-800',
    similarity: 'text-purple-800',
    competition: 'text-red-800',
    market: 'text-green-800',
    content: 'text-yellow-800',
    strategy: 'text-indigo-800',
    suggestion: 'text-orange-800',
    opportunity: 'text-emerald-800',
    analysis: 'text-gray-800',
    trend: 'text-teal-800',
    user: 'text-pink-800',
    monetization: 'text-amber-800',
    advantage: 'text-cyan-800',
    default: 'text-gray-800'
  }
  return styles[type] || styles.default
}

interface Category {
  id: string
  name: string
  english_name: string
  keyword_count: number
}

interface Keyword {
  id: string
  keyword: string
  user_intent?: string
  user_pain_point?: string
  competition_level?: 'easy' | 'medium' | 'hard'
  competition_score?: number
  competition_color?: string
  competition_description?: string
  serp_analysis?: string
  user_comment?: string
  category?: string
  recommended_domains?: Array<{
    domain: string
    check_url: string
  }>
  is_analyzed: boolean
  created_at_formatted?: string
  analyzed_at_formatted?: string
  source?: string
}

export default function KeywordDetailPage() {
  const params = useParams()
  const router = useRouter()
  const [keyword, setKeyword] = useState<Keyword | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [categories, setCategories] = useState<Category[]>([])
  const [suggestions, setSuggestions] = useState<any[]>([])
  const [report, setReport] = useState<any>(null)
  const [generatingReport, setGeneratingReport] = useState(false)
  const [currentStep, setCurrentStep] = useState('')
  const [progress, setProgress] = useState(0)
  const [prevKeyword, setPrevKeyword] = useState<{id: string, keyword: string} | null>(null)
  const [nextKeyword, setNextKeyword] = useState<{id: string, keyword: string} | null>(null)
  const [generatingDomains, setGeneratingDomains] = useState(false)

  // SEO生成函数
  const generateSEOData = useMemo(() => {
    if (!keyword) return { title: 'CatchIdeas', description: '关键词分析平台' }

    const keywordText = keyword.keyword
    // 获取分类名称（内联实现避免函数调用顺序问题）
    const categoryName = keyword.category
      ? (categories.find(c => c.english_name === keyword.category)?.name || keyword.category)
      : undefined
    const isAnalyzed = keyword.is_analyzed
    const competitionLevel = keyword.competition_level
    const competitionScore = keyword.competition_score

    // 生成分析状态描述
    const getAnalysisStatus = (analyzed: boolean) => {
      return analyzed ? '已深度分析' : '待深度分析'
    }

    // 生成竞争等级描述
    const getCompetitionDesc = (level?: string, score?: number) => {
      if (!level && !score) return '竞争待评估'
      if (level === 'easy') return '竞争较低'
      if (level === 'medium') return '竞争中等'
      if (level === 'hard') return '竞争激烈'
      if (score) return `竞争${score}/10分`
      return '竞争中等'
    }

    // 生成分类描述
    const getCategoryDesc = (catName?: string) => {
      return catName ? `${catName}领域` : '通用领域'
    }

    // 生成数据亮点
    const getDataHighlight = () => {
      if (!isAnalyzed || !report) return ''
      const score = report.recommendation_score
      if (score) return `推荐评分${score}分，`
      return ''
    }

    // 标题格式: {keyword} | CatchIdeas
    const title = `${keywordText} | CatchIdeas`

    // 描述格式: {keyword}关键词分析 - {分析状态}，{分类}领域，竞争{竞争等级}。{数据亮点}专业的互联网项目分析平台。
    const description = `${keywordText}关键词分析 - ${getAnalysisStatus(isAnalyzed)}，${getCategoryDesc(categoryName)}，${getCompetitionDesc(competitionLevel, competitionScore)}。${getDataHighlight()}专业的互联网项目分析平台。`

    return { title, description }
  }, [keyword, report, categories])

  // 从报告中提取完整性分析数据
  const completenessAnalysis = useMemo(() => {
    if (!report) return null
    try {
      // 如果报告有ai_analysis字段，解析它
      if (report.ai_analysis) {
        const aiAnalysis = typeof report.ai_analysis === 'string'
          ? JSON.parse(report.ai_analysis)
          : report.ai_analysis
        const algorithmData = aiAnalysis?.sections?.algorithms?.data || {}
        return algorithmData.report_completeness_analysis || null
      }
      // 如果报告直接包含完整性分析数据
      return report.report_completeness_analysis || null
    } catch (error) {
      return null
    }
  }, [report])
  const [aiDomains, setAiDomains] = useState<Array<{domain: string, reason: string, check_url: string}>>([])
  const [showDomainCountDialog, setShowDomainCountDialog] = useState(false)
  const [selectedDomainCount, setSelectedDomainCount] = useState(10)
  const [generatingLogo, setGeneratingLogo] = useState(false)
  const [logoData, setLogoData] = useState<{prompt: string, images: string[]} | null>(null)
  const [logoStep, setLogoStep] = useState('')
  const [showLogoCountDialog, setShowLogoCountDialog] = useState(false)
  const [selectedLogoCount, setSelectedLogoCount] = useState(1)
  const [domainCheckStatus, setDomainCheckStatus] = useState<{
    checking: boolean
    result: {
      registered: boolean | null
      status: string
      message: string
    } | null
  }>({ checking: false, result: null })
  const [batchDomainCheck, setBatchDomainCheck] = useState<{
    checking: boolean
    progress: number
    results: Array<{
      domain: string
      suggestion: string
      registered: boolean | null
      status: string
      message: string
    }>
    stats: {
      total: number
      available: number
      registered: number
      failed: number
    } | null
  }>({
    checking: false,
    progress: 0,
    results: [],
    stats: null
  })

  useEffect(() => {
    if (params && params.id) {
      fetchKeywordDetail(params.id as string)
      fetchSuggestions(params.id as string)
      fetchAdjacentKeywords(params.id as string)
      // 注意：fetchReport 将在 fetchKeywordDetail 成功后根据分析状态调用
    }
    fetchCategories()
  }, [params])



  // 获取分类数据
  const fetchCategories = async () => {
    try {
      const timestamp = Date.now()
      const response = await fetch(`/api/categories/list?t=${timestamp}`, {
        cache: 'no-store',
        headers: {
          'Cache-Control': 'no-cache'
        }
      })

      // 检查响应状态，避免404错误在控制台显示
      if (!response.ok) {
        // 如果是404或其他错误，静默处理，不抛出错误
        return
      }

      const data = await response.json()

      if (data.success && data.data.categories) {
        setCategories(data.data.categories)
      }
    } catch (error) {
      // 获取分类失败，静默处理
    }
  }

  const fetchKeywordDetail = async (id: string) => {
    setLoading(true)
    setError('')

    try {
      const response = await fetch(`/api/keywords/${id}`)

      // 检查响应状态
      if (!response.ok) {
        if (response.status === 404) {
          setError('关键词不存在')
        } else {
          setError(`请求失败 (${response.status})`)
        }
        return
      }

      const data = await response.json()

      if (data.success) {
        setKeyword(data.data)

        // 检查关键词是否已分析，如果已分析则获取报告
        const isAnalyzed = data.data.user_intent || data.data.user_pain_point || data.data.competition_level
        if (isAnalyzed) {
          fetchReport(id)
        } else {
          // 确保report状态为null，避免显示旧数据
          setReport(null)
        }

        // 动态更新页面标题和描述
        if (data.data.keyword) {
          document.title = `${data.data.keyword} - 需求分析与项目建议 | CatchIdeas`

          // 更新meta描述
          const metaDescription = document.querySelector('meta[name="description"]')
          if (metaDescription) {
            metaDescription.setAttribute('content',
              `分析「${data.data.keyword}」搜索背景、用户痛点、解决方案空白，推荐适合开发的网站类型和功能方向。帮您评估${data.data.keyword}项目的市场前景。`
            )
          }
        }
      } else {
        setError(data.error || '获取关键词详情失败')
      }
    } catch (err) {
      setError('网络错误，请稍后重试')
    } finally {
      setLoading(false)
    }
  }

  // 获取相关词
  const fetchSuggestions = async (keywordId: string) => {
    try {
      const timestamp = Date.now()
      const response = await fetch(`/api/keywords/suggestions?keyword_id=${encodeURIComponent(keywordId)}&t=${timestamp}`)

      // 检查响应状态，避免404错误在控制台显示
      if (!response.ok) {
        // 如果是404或其他错误，静默处理，不抛出错误
        return
      }

      const data = await response.json()

      if (data.success && data.data.suggestions) {
        setSuggestions(data.data.suggestions)
      }
    } catch (error) {
      // 获取相关词失败，静默处理
    }
  }

  // 获取报告
  const fetchReport = async (keywordId: string) => {
    try {
      const timestamp = Date.now()
      const response = await fetch(`/api/keywords/report?keyword_id=${encodeURIComponent(keywordId)}&t=${timestamp}`, {
        // 添加请求配置，减少浏览器错误日志
        cache: 'no-store',
        headers: {
          'Cache-Control': 'no-cache'
        }
      })

      const data = await response.json()

      // 处理API响应
      if (data.success && data.data) {
        // 检查是否是新的分段格式
        if (data.data.ai_analysis) {
          try {
            const aiAnalysis = JSON.parse(data.data.ai_analysis)
            if (aiAnalysis.sections) {
              // 新的分段格式，需要转换
              const convertedReport = convertReportFormat(aiAnalysis)
              const flattenedReport = {
                ...convertedReport,
                recommendation_score: convertedReport.comprehensive_score_analysis?.recommendation_score || 7.5,
                confidence_score: (convertedReport.comprehensive_score_analysis?.confidence_score || 85) / 100,
                seo_difficulty: convertedReport.competition_landscape_analysis?.intensity || 'medium',
                sections: convertedReport
              }
              setReport(flattenedReport)
            } else {
              // 旧格式，直接使用
              setReport(data.data)
            }
          } catch (e) {
            // JSON解析失败，使用原始数据
            setReport(data.data)
          }
        } else {
          // 没有ai_analysis字段，直接使用
          setReport(data.data)
        }
      } else {
        // 没有报告数据，这是正常情况
        setReport(null)
      }
    } catch (error) {
      // 获取报告失败，静默处理
      setReport(null)
    }
  }

  // 获取相邻关键词（更早和更新的词条）
  const fetchAdjacentKeywords = async (keywordId: string) => {
    try {
      const timestamp = Date.now()
      const response = await fetch(`/api/keywords/adjacent?keyword_id=${encodeURIComponent(keywordId)}&t=${timestamp}`)

      // 检查响应状态，避免404错误在控制台显示
      if (!response.ok) {
        // 如果是404或其他错误，设置为null
        setPrevKeyword(null)
        setNextKeyword(null)
        return
      }

      const data = await response.json()

      if (data.success && data.data) {
        setPrevKeyword(data.data.prev)
        setNextKeyword(data.data.next)
      } else {
        // API失败时设置为null，显示"暂无更早/更新词条"
        setPrevKeyword(null)
        setNextKeyword(null)
      }
    } catch (error) {
      // 获取相邻关键词失败，静默处理
      setPrevKeyword(null)
      setNextKeyword(null)
    }
  }

  // 生成智能域名推荐
  const generateAIDomains = async () => {
    if (!keyword?.keyword) return

    setGeneratingDomains(true)
    try {
      const response = await fetch('/api/domains/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          keyword: keyword.keyword,
          count: selectedDomainCount
        })
      })

      const data = await response.json()

      if (data.success && data.data && data.data.domains) {
        setAiDomains(data.data.domains)
      } else {
        toast.error('生成域名推荐失败，请稍后重试')
      }
    } catch (error) {
      toast.error('生成域名推荐失败，请稍后重试')
    } finally {
      setGeneratingDomains(false)
      setShowDomainCountDialog(false)
    }
  }

  // 生成Logo
  const generateLogo = async () => {
    if (!keyword?.keyword) return

    setGeneratingLogo(true)
    setLogoStep('理解中...')

    try {
      // 模拟步骤进度
      setTimeout(() => setLogoStep('生成Prompt中...'), 1000)
      setTimeout(() => setLogoStep('生成中...'), 3000)

      const response = await fetch('/api/logo/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          keyword: keyword.keyword,
          count: selectedLogoCount
        })
      })

      const data = await response.json()

      if (data.success && data.data) {
        // 验证返回的数据
        if (data.data.images && data.data.images.length > 0) {
          setLogoData({
            prompt: data.data.prompt || '生成的Logo设计',
            images: data.data.images
          })
          toast.success(`成功生成${data.data.images.length}个Logo设计`)
        } else {
          toast.error('Logo生成成功但未返回图像，请重试')
        }
      } else {
        const errorMsg = data.error || '生成Logo失败，请稍后重试'
        toast.error(errorMsg)
        console.error('Logo生成失败:', data)
      }
    } catch (error) {
      console.error('Logo生成错误:', error)
      if (error instanceof TypeError && error.message.includes('fetch')) {
        toast.error('网络连接失败，请检查网络后重试')
      } else {
        toast.error('生成Logo失败，请稍后重试')
      }
    } finally {
      setGeneratingLogo(false)
      setLogoStep('')
      setShowLogoCountDialog(false)
    }
  }

  // 下载Logo
  const downloadLogo = async (imageUrl: string, index: number = 0) => {
    if (!imageUrl) return

    try {
      const response = await fetch(imageUrl)
      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `${keyword?.keyword || 'logo'}-logo-${index + 1}.png`
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)
    } catch (error) {
      toast.error('下载失败，请稍后重试')
    }
  }

  // 下载所有Logo
  const downloadAllLogos = async () => {
    if (!logoData?.images) return

    for (let i = 0; i < logoData.images.length; i++) {
      await downloadLogo(logoData.images[i], i)
      // 添加延迟避免浏览器阻止多个下载
      if (i < logoData.images.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 500))
      }
    }
  }

  // 复制Prompt
  const copyLogoPrompt = async () => {
    if (!logoData?.prompt) return

    try {
      await navigator.clipboard.writeText(logoData.prompt)
      toast.success('Prompt已复制到剪贴板')
    } catch (error) {
      toast.error('复制失败，请手动复制')
    }
  }

  // 检查域名注册状态
  const checkDomainRegistration = async (domain: string) => {
    if (!domain) return

    setDomainCheckStatus({ checking: true, result: null })

    try {
      const response = await fetch('/api/domain/check', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ domain })
      })

      const data = await response.json()

      if (data.success && data.data) {
        setDomainCheckStatus({
          checking: false,
          result: {
            registered: data.data.registered,
            status: data.data.status,
            message: data.data.message
          }
        })
      } else {
        setDomainCheckStatus({
          checking: false,
          result: {
            registered: null,
            status: 'error',
            message: '查询失败'
          }
        })
        toast.error('域名查询失败，请稍后重试')
      }
    } catch (error) {
      setDomainCheckStatus({
        checking: false,
        result: {
          registered: null,
          status: 'error',
          message: '查询失败'
        }
      })
      toast.error('域名查询失败，请稍后重试')
    }
  }

  // 从相关词生成域名
  const generateDomainFromSuggestion = (suggestion: string): string | null => {
    // 去除空格，转小写
    const cleaned = suggestion.replace(/\s+/g, '').toLowerCase()

    // 只保留英文字母和数字
    const alphanumeric = cleaned.replace(/[^a-z0-9]/g, '')

    // 验证是否为空或过短
    if (!alphanumeric || alphanumeric.length < 3) {
      return null
    }

    return `${alphanumeric}.com`
  }

  // 批量检查相关词域名
  const batchCheckSuggestionDomains = async () => {
    if (!suggestions || suggestions.length === 0) return

    // 生成域名列表
    const domainMappings: Array<{domain: string, suggestion: string}> = []

    suggestions.forEach(suggestion => {
      const domain = generateDomainFromSuggestion(suggestion.suggestion)
      if (domain) {
        domainMappings.push({
          domain,
          suggestion: suggestion.suggestion
        })
      }
    })

    if (domainMappings.length === 0) {
      toast.error('没有可检查的域名')
      return
    }

    setBatchDomainCheck({
      checking: true,
      progress: 0,
      results: [],
      stats: null
    })

    try {
      const domains = domainMappings.map(item => item.domain)

      const response = await fetch('/api/domain/batch-check', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ domains })
      })

      const data = await response.json()

      if (data.success && data.data) {
        // 合并域名和相关词信息
        const results = data.data.results.map((result: any) => {
          const mapping = domainMappings.find(m => m.domain === result.domain)
          return {
            ...result,
            suggestion: mapping?.suggestion || ''
          }
        })

        setBatchDomainCheck({
          checking: false,
          progress: 100,
          results,
          stats: data.data.stats
        })
      } else {
        setBatchDomainCheck({
          checking: false,
          progress: 0,
          results: [],
          stats: null
        })
        toast.error('批量检查失败，请稍后重试')
      }
    } catch (error) {
      setBatchDomainCheck({
        checking: false,
        progress: 0,
        results: [],
        stats: null
      })
      toast.error('批量检查失败，请稍后重试')
    }
  }

  // 复制未注册域名
  const copyAvailableDomains = () => {
    const availableDomains = batchDomainCheck.results
      .filter(result => result.registered === false)
      .map(result => result.domain)

    if (availableDomains.length === 0) {
      toast.error('没有可复制的未注册域名')
      return
    }

    navigator.clipboard.writeText(availableDomains.join('\n'))
      .then(() => {
        toast.success(`已复制${availableDomains.length}个未注册域名`)
      })
      .catch(() => {
        toast.error('复制失败，请手动复制')
      })
  }

  // 导出报告功能
  const exportReport = (format: 'markdown' | 'text') => {
    if (!report || !keyword) return

    const currentDate = new Date().toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })

    let content = ''

    if (format === 'markdown') {
      content = generateMarkdownReport(keyword, report, currentDate)
    } else {
      content = generateTextReport(keyword, report, currentDate)
    }

    // 创建下载链接
    const blob = new Blob([content], { type: 'text/plain;charset=utf-8' })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `${keyword.keyword}_分析报告_${new Date().toISOString().split('T')[0]}.${format === 'markdown' ? 'md' : 'txt'}`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)

    toast.success(`报告已导出为${format === 'markdown' ? 'Markdown' : '文本'}格式`)
  }

  // 生成Markdown格式报告
  const generateMarkdownReport = (keyword: any, report: any, date: string) => {
    const searchMetrics = typeof report.search_metrics === 'string'
      ? JSON.parse(report.search_metrics)
      : report.search_metrics || {}

    const googleSearchResults = typeof report.google_search_results === 'string'
      ? JSON.parse(report.google_search_results)
      : report.google_search_results || {}

    const topCompetitorsAnalysis = typeof report.top_competitors_analysis === 'string'
      ? JSON.parse(report.top_competitors_analysis)
      : report.top_competitors_analysis || []

    const titlePatternsAnalysis = typeof report.title_patterns_analysis === 'string'
      ? JSON.parse(report.title_patterns_analysis)
      : report.title_patterns_analysis || {}

    const contentGapAnalysis = typeof report.content_gap_analysis === 'string'
      ? JSON.parse(report.content_gap_analysis)
      : report.content_gap_analysis || {}

    const currentUrl = `https://catchideas.com/keyword/${keyword.id}`

    return `# ${keyword.keyword} - 关键词分析报告

## 📊 基本信息
- **关键词**: ${keyword.keyword}
- **推荐评分**: ${report.recommendation_score || 'N/A'}/10
- **置信度**: ${report.confidence_score ? Math.round(report.confidence_score * 100) : 'N/A'}%

## 🔍 搜索数据
- **月搜索量**: ${(searchMetrics.volume || 0).toLocaleString()}
- **SEO难度**: ${searchMetrics.difficulty || 50}/100
- **Google搜索结果**: ${searchMetrics.totalResults ? parseInt(searchMetrics.totalResults).toLocaleString() : 'N/A'}条
- **竞争强度**: ${report.competition_intensity === 'high' ? '激烈' : report.competition_intensity === 'medium' ? '中等' : '较低'}
- **SEO难度等级**: ${report.seo_difficulty === 'hard' ? '困难' : report.seo_difficulty === 'medium' ? '中等' : '简单'}

## 🔍 相似关键词分析
${(() => {
  try {
    const relatedKeywords = typeof report.related_keywords === 'string'
      ? JSON.parse(report.related_keywords)
      : report.related_keywords
    if (Array.isArray(relatedKeywords) && relatedKeywords.length > 0) {
      return relatedKeywords.map((item: any, index: number) =>
        `${index + 1}. **${item.keyword}** - ${Math.round((item.similarity_score || 0) * 100)}%`
      ).join('\n')
    }
    return '暂无相似关键词数据'
  } catch {
    return '暂无相似关键词数据'
  }
})()}

${report.similarity_analysis ? `\n**相似度分析说明**: ${report.similarity_analysis}\n` : ''}

## 🎯 用户分析
${keyword.user_intent ? `**用户意图**: ${keyword.user_intent}\n\n` : ''}${keyword.user_pain_point ? `**用户痛点**: ${keyword.user_pain_point}\n\n` : ''}

## 🏆 竞争分析
- **竞争强度**: ${keyword.competition_level || 'N/A'}
- **竞争评分**: ${keyword.competition_score || 'N/A'}/10
${keyword.competition_description ? `\n**竞争描述**: ${keyword.competition_description}\n` : ''}

### 🏆 前10名竞争对手分析
${topCompetitorsAnalysis.length > 0
  ? topCompetitorsAnalysis.slice(0, 10).map((comp: any, index: number) => {
      const urlTypeText = comp.urlDescription || comp.pathInfo || (comp.domainType === 'root_domain' ? '根域名' : '二级域名')
      return `**#${comp.rank || index + 1}** ${comp.url}\n- 标题: ${comp.title}\n- 类型: ${comp.contentType === 'commercial' ? '商业' : comp.contentType === 'informational' ? '信息' : comp.contentType === 'list' ? '列表' : comp.contentType || '未知'}\n- URL类型: ${urlTypeText}`
    }).join('\n\n')
  : '暂无竞争对手数据'}

## ⚡ 竞争优势分析
${report.competitive_advantages || '暂无竞争优势分析'}

## 🎯 市场机会分析
${report.market_opportunity || '暂无市场机会分析'}

## 📋 标题模式分析
${titlePatternsAnalysis.averageLength ? `- **平均标题长度**: ${titlePatternsAnalysis.averageLength}字符\n` : ''}${titlePatternsAnalysis.averageWordCount ? `- **平均词数**: ${titlePatternsAnalysis.averageWordCount}个词\n` : ''}${titlePatternsAnalysis.hasDatePattern ? `- **包含日期的标题**: ${titlePatternsAnalysis.hasDatePattern}个\n` : ''}${titlePatternsAnalysis.hasNumberPattern ? `- **包含数字的标题**: ${titlePatternsAnalysis.hasNumberPattern}个\n` : ''}${titlePatternsAnalysis.hasQuestionPattern ? `- **疑问句标题**: ${titlePatternsAnalysis.hasQuestionPattern}个\n` : ''}

${titlePatternsAnalysis.commonWords && titlePatternsAnalysis.commonWords.length > 0
  ? `\n**常用词汇**: ${titlePatternsAnalysis.commonWords.map((w: any) => `${w.word}(${w.count}次)`).join(', ')}\n`
  : ''}

## 📋 内容空缺分析
${contentGapAnalysis.gapCount ? `**发现空缺数量**: ${contentGapAnalysis.gapCount}个\n` : ''}${contentGapAnalysis.opportunities ? `**机会等级**: ${contentGapAnalysis.opportunities === 'high' ? '高' : contentGapAnalysis.opportunities === 'medium' ? '中' : '低'}\n` : ''}

${contentGapAnalysis.missingContentTypes && contentGapAnalysis.missingContentTypes.length > 0
  ? `\n**缺少的内容类型**: ${contentGapAnalysis.missingContentTypes.map((type: string) => {
      const typeMap: { [key: string]: string } = {
        'guide': '指南', 'tutorial': '教程', 'review': '评测',
        'comparison': '对比', 'list': '列表', 'news': '新闻',
        'blog': '博客', 'commercial': '商业', 'discussion': '讨论',
        'informational': '信息'
      }
      return typeMap[type] || type
    }).join(', ')}\n`
  : ''}

${contentGapAnalysis.gaps && contentGapAnalysis.gaps.length > 0
  ? `\n**具体空缺内容**:\n${contentGapAnalysis.gaps.map((gap: string, i: number) => `${i + 1}. ${gap}`).join('\n')}\n`
  : ''}

${contentGapAnalysis.recommendations && contentGapAnalysis.recommendations.length > 0
  ? `\n**优化建议**:\n${contentGapAnalysis.recommendations.map((rec: string, i: number) => `${i + 1}. ${rec}`).join('\n')}\n`
  : ''}

## 💡 内容策略建议
${report.content_strategy || '暂无内容策略建议'}

## 👥 目标受众分析
${report.target_audience || '暂无目标受众分析'}

## 💰 变现潜力分析
${report.monetization_potential || '暂无变现潜力分析'}

## 📈 趋势分析
${report.trend_analysis || '暂无趋势分析'}

## 🗓️ 季节性模式
${report.seasonal_patterns || '暂无季节性模式分析'}

## 🌐 推荐域名
${keyword.recommended_domains && keyword.recommended_domains.length > 0
  ? keyword.recommended_domains.map((domain: any, index: number) =>
      `${index + 1}. **${domain.domain}** - [查询可用性](${domain.check_url})`
    ).join('\n')
  : '暂无推荐域名'}

## 🏢 当前主要同行
${(() => {
  try {
    const topCompetitors = typeof report.top_competitors === 'string'
      ? JSON.parse(report.top_competitors)
      : report.top_competitors
    return Array.isArray(topCompetitors) && topCompetitors.length > 0
      ? topCompetitors.slice(0, 10).map((comp: any, index: number) =>
          `${index + 1}. ${comp.domain || '未知域名'}${comp.isRootDomain ? ' (根域名)' : ''}`
        ).join('\n')
      : '暂无主要同行数据'
  } catch {
    return '暂无主要同行数据'
  }
})()}

## 🎯 快速行动建议
${(() => {
  try {
    const suggestions = typeof report.action_suggestions === 'string'
      ? JSON.parse(report.action_suggestions)
      : report.action_suggestions
    return Array.isArray(suggestions)
      ? suggestions.map((s: string, i: number) => `${i + 1}. ${s}`).join('\n')
      : suggestions || '暂无行动建议'
  } catch {
    return '暂无行动建议'
  }
})()}

---
*导出时间: ${new Date().toLocaleString('zh-CN')}*
*详情页面: [${keyword.keyword}](${currentUrl})*
*数据来源: [CatchIdeas](https://catchideas.com/)*
`
  }

  // 生成文本格式报告
  const generateTextReport = (keyword: any, report: any, date: string) => {
    const currentUrl = `https://catchideas.com/keyword/${keyword.id}`

    return generateMarkdownReport(keyword, report, date)
      .replace(/^#+ /gm, '')
      .replace(/\*\*(.*?)\*\*/g, '$1')
      .replace(/\*(.*?)\*/g, '$1')
      .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '$1 ($2)')
      .replace(/导出时间: .*?\n/g, `导出时间: ${new Date().toLocaleString('zh-CN')}\n`)
      .replace(/详情页面: .*?\n/g, `详情页面: ${currentUrl}\n`)
      .replace(/数据来源: .*?\n/g, `数据来源: https://catchideas.com/\n`)
  }

  // 生成报告 (一次性处理)
  const generateReport = async () => {
    if (!keyword?.id) {
      toast.error('关键词ID不存在')
      return
    }

    setGeneratingReport(true)
    setProgress(0)
    setCurrentStep('🚀 开始生成详细分析报告...')

    // 模拟进度更新
    const progressSteps = [
      { progress: 10, message: '🔍 正在获取关键词信息...' },
      { progress: 25, message: '📝 正在获取相关关键词...' },
      { progress: 40, message: '🔍 正在分析关键词相似度...' },
      { progress: 60, message: '🌐 正在执行Google搜索分析...' },
      { progress: 80, message: '🤖 正在使用AI模型进行深度分析...' },
      { progress: 95, message: '💾 正在保存报告到数据库...' }
    ]

    // 启动进度模拟
    let currentStepIndex = 0
    let progressInterval: NodeJS.Timeout | null = null

    progressInterval = setInterval(() => {
      if (currentStepIndex < progressSteps.length) {
        const step = progressSteps[currentStepIndex]
        setProgress(step.progress)
        setCurrentStep(step.message)
        currentStepIndex++
      }
    }, 2000) // 每2秒更新一次进度

    try {
      // 使用原API，Google搜索已修复 - 添加时间戳避免缓存
      const timestamp = Date.now()
      const response = await fetch(`/api/keywords/generate-complete-report?t=${timestamp}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          keyword_id: keyword.id,
          keyword: keyword.keyword
        })
      })

      // 清除进度模拟
      if (progressInterval) clearInterval(progressInterval)

      if (!response.ok) {
        console.error('=== API响应失败 ===', response.status, response.statusText)
        const errorText = await response.text()
        console.error('=== 错误响应内容 ===', errorText)
        throw new Error(`HTTP ${response.status}: ${errorText}`)
      }

      // 直接等待完整响应，不使用流式处理
      const data = await response.json()

      if (data.error) {
        throw new Error(data.error)
      }

      if (data.success && data.report) {
        // 直接使用返回的报告数据
        const reportData = data.report
        setReport({
          ...reportData,
          recommendation_score: reportData.recommendation_score || 7.5,
          confidence_score: Math.min(1, (reportData.confidence_score || 85) / 100), // 确保不超过1
          seo_difficulty: reportData.competition_level || 'medium'
        })
        setProgress(100)
        setCurrentStep('✅ 报告生成完成！')
        toast.success('详细分析报告生成成功！')

        // 报告生成完成后，更新本地状态以确保所有字段都显示
        if (keyword?.id) {
          setTimeout(() => {
            fetchReport(keyword.id)
          }, 1000)
        }

        // 重置生成状态
        setTimeout(() => {
          setGeneratingReport(false)
          setProgress(0)
          setCurrentStep('')
        }, 2000)
      } else {
        throw new Error('报告生成失败')
      }

      setGeneratingReport(false)

    } catch (error: any) {
      // 清除进度模拟
      if (progressInterval) clearInterval(progressInterval)

      console.error('详细分析报告生成失败:', error)
      toast.error('报告生成失败，请稍后重试')
      setGeneratingReport(false)
      setProgress(0)
      setCurrentStep('')
    }
  }

  // 转换报告格式以适配原有UI
  const convertReportFormat = (reportData: any) => {
    try {

      const sections = reportData.sections || {}

      // 从算法分析中提取基础数据
      const algorithmData = sections.algorithms?.data || {}
      const algorithmSummary = sections.algorithms?.summary || {}

      // 提取各种分析数据
      const intentData = sections.intent?.data || {}
      const trafficData = sections.traffic?.data || {}
      const competitionData = sections.competition?.data || {}
      const contentData = sections.content?.data || {}
      const businessData = sections.business?.data || {}
      const summaryData = sections.summary?.data || {}

      // 从算法数据中提取更详细的信息
      const volumePrediction = algorithmData.enhanced_volume_prediction || {}
      const seoDifficulty = algorithmData.enhanced_seo_analysis || {}
      const commercialValue = algorithmData.commercial_value_analysis || {}
      const searchIntentData = algorithmData.search_intent_analysis || {}
      const comprehensiveScore = algorithmData.enhanced_comprehensive_score || {}
      const confidenceAnalysis = algorithmData.enhanced_confidence_analysis || {}
      const completenessAnalysis = algorithmData.report_completeness_analysis || {}

      const convertedReport = {
        search_intent_analysis: {
          intent_type: intentData.intent_type || searchIntentData.type || '信息型',
          confidence: intentData.confidence || searchIntentData.confidence || 75,
          analysis: intentData.analysis || `基于搜索意图算法分析，关键词主要表现为${searchIntentData.type || '信息型'}意图，置信度${searchIntentData.confidence || 75}%。${searchIntentData.type === 'transactional' ? '用户有明确购买意图' : searchIntentData.type === 'commercial' ? '用户在比较和研究阶段' : searchIntentData.type === 'navigational' ? '用户寻找特定网站或品牌' : '用户主要寻求信息和知识'}。`
        },
        traffic_opportunity_assessment: {
          predicted_volume: trafficData.predicted_volume || volumePrediction.volume || 1000,
          traffic_level: trafficData.traffic_level || volumePrediction.level || 'medium',
          analysis: trafficData.analysis || `基于流量预测算法，关键词预测月搜索量为${volumePrediction.volume || 1000}次，流量等级为${volumePrediction.level || 'medium'}，具有一定的流量获取潜力。`
        },
        competition_landscape_analysis: {
          intensity: competitionData.intensity || seoDifficulty.level || 'medium',
          intensity_score: competitionData.intensity_score || seoDifficulty.score || 50,
          analysis: competitionData.analysis || competitionData.opportunity || `竞争分析显示，该关键词的SEO难度为${seoDifficulty.score || 50}/100，竞争强度为${seoDifficulty.level || 'medium'}。${competitionData.opportunity || '存在一定的市场机会。'}`
        },
        content_opportunity_mining: {
          gap_count: contentData.gap_count || algorithmData.content_gap_analysis?.gapCount || 3,
          opportunities: contentData.opportunities || algorithmData.content_gap_analysis?.opportunities || 'medium',
          analysis: contentData.analysis || `内容空缺分析发现${algorithmData.content_gap_analysis?.gapCount || 3}个内容空缺，机会等级为${algorithmData.content_gap_analysis?.opportunities || 'medium'}。建议创建针对性的内容来填补市场空白。`
        },
        business_value_analysis: {
          commercial_score: businessData.commercial_score || commercialValue.score || 6,
          estimated_cpc: businessData.estimated_cpc || commercialValue.metrics?.estimatedCPC || 1.5,
          analysis: businessData.analysis || `商业价值评估显示，关键词的商业价值评分为${commercialValue.score || 6}/10，预估CPC为$${commercialValue.metrics?.estimatedCPC || 1.5}，具有良好的商业化潜力。`
        },
        comprehensive_score_analysis: {
          recommendation_score: summaryData.recommendation_score || comprehensiveScore.score || reportData.summary?.overall_score || 6.0,
          confidence_score: algorithmSummary.confidence || confidenceAnalysis.overall || 85,
          seo_difficulty: seoDifficulty.level || 'medium',
          analysis: summaryData.analysis || `综合分析显示，关键词的推荐评分为${(comprehensiveScore.score || reportData.summary?.overall_score || 6.0).toFixed(2)}/10，整体置信度为${confidenceAnalysis.overall || 85}%，SEO难度为${seoDifficulty.level === 'easy' ? '简单' : seoDifficulty.level === 'medium' ? '中等' : '困难'}。${reportData.summary?.recommendation || '推荐'}进行SEO优化投入。`
        }
      }

      return convertedReport

    } catch (error) {
      // 如果转换失败，返回基础结构，避免硬编码值
      return {
        search_intent_analysis: {
          intent_type: '未知',
          confidence: 0,
          analysis: '报告数据解析失败，请重新生成分析报告。'
        },
        traffic_opportunity_assessment: {
          predicted_volume: 0,
          traffic_level: 'unknown',
          analysis: '流量数据解析失败，请重新生成分析报告。'
        },
        competition_landscape_analysis: {
          intensity: 'unknown',
          intensity_score: 0,
          analysis: '竞争数据解析失败，请重新生成分析报告。'
        },
        content_opportunity_mining: {
          gap_count: 0,
          opportunities: 'unknown',
          analysis: '内容分析数据解析失败，请重新生成分析报告。'
        },
        business_value_analysis: {
          commercial_score: 0,
          estimated_cpc: 0,
          analysis: '商业价值数据解析失败，请重新生成分析报告。'
        },
        comprehensive_score_analysis: {
          recommendation_score: 0,
          confidence_score: 0,
          seo_difficulty: 'unknown',
          analysis: '综合评分数据解析失败，请重新生成分析报告。'
        }
      }
    }
  }

  // 获取中文分类名
  const getCategoryName = (englishName: string) => {
    const category = categories.find(c => c.english_name === englishName)
    return category ? category.name : englishName
  }

  // 获取竞争难度显示信息 - 统一的分数区间标准
  const getCompetitionInfo = () => {
    if (!keyword?.competition_score) {
      return { text: '未分析', color: '#gray', bgColor: '#f3f4f6' }
    }

    // 统一的分数区间标准：简单(1-3)、中等(4-7)、困难(8-10)
    const score = keyword.competition_score
    if (score <= 3) {
      return { text: '简单', color: '#22c55e', bgColor: '#dcfce7' }
    } else if (score <= 7) {
      return { text: '中等', color: '#eab308', bgColor: '#fef3c7' }
    } else {
      return { text: '困难', color: '#ef4444', bgColor: '#fee2e2' }
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
          <FrontendHeader />
        <div className="flex items-center justify-center py-32">
          <div className="flex items-center space-x-3 bg-white/80 backdrop-blur-sm rounded-2xl px-8 py-6 shadow-lg">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <span className="text-lg font-medium text-gray-700">加载关键词详情中...</span>
          </div>
        </div>
        <FrontendFooter />
        <Toaster position="top-right" richColors closeButton duration={5000} />
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
          <FrontendHeader />
        <div className="flex items-center justify-center py-32">
          <Card className="w-full max-w-md idea-card-enhanced">
            <CardContent className="p-8 text-center">
              <AlertTriangle className="h-16 w-16 text-red-500 mx-auto mb-6" />
              <h2 className="text-xl font-bold mb-3 text-gray-900">加载失败</h2>
              <p className="text-gray-600 mb-6 leading-relaxed">{error}</p>
              <div className="flex gap-3 justify-center">
                <Button
                  onClick={() => params?.id && fetchKeywordDetail(params.id as string)}
                  className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-200"
                >
                  重试
                </Button>
                <Button
                  variant="outline"
                  onClick={() => router.back()}
                  className="border-gray-300 hover:bg-gray-50"
                >
                  返回
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
        <FrontendFooter />
        <Toaster position="top-right" richColors closeButton duration={5000} />
      </div>
    )
  }

  if (!keyword) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
          <FrontendHeader />
        <div className="flex items-center justify-center py-32">
          <Card className="w-full max-w-md idea-card-enhanced">
            <CardContent className="p-8 text-center">
              <Search className="h-16 w-16 text-gray-400 mx-auto mb-6" />
              <h2 className="text-xl font-bold mb-3 text-gray-900">关键词不存在</h2>
              <p className="text-gray-600 mb-6 leading-relaxed">请检查链接是否正确</p>
              <Button
                onClick={() => router.push('/')}
                className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-200"
              >
                返回首页
              </Button>
            </CardContent>
          </Card>
        </div>
        <FrontendFooter />
        <Toaster position="top-right" richColors closeButton duration={5000} />
      </div>
    )
  }

  const competitionInfo = getCompetitionInfo()

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
        {/* 公共头部 */}
        <FrontendHeader />

      {/* 关键词标题区域 */}
      <section className="py-8 px-4">
        <div className="container mx-auto">
          <Card className="idea-card-enhanced">
            <CardHeader className="pb-4">
              <div className="text-center">
                <CardTitle className="text-2xl md:text-3xl font-bold text-foreground mb-4 break-words">
                  {keyword.keyword}
                </CardTitle>
                <div className="flex items-center justify-center space-x-3 flex-wrap gap-2">
                  <Badge
                    className={keyword.is_analyzed ? "badge-analyzed" : "badge-pending"}
                  >
                    {keyword.is_analyzed ? '已分析' : '待分析'}
                  </Badge>
                  {keyword.category && (
                    <Badge variant="outline" className="border-primary/20 text-primary">
                      {getCategoryName(keyword.category)}
                    </Badge>
                  )}
                  {keyword.source && keyword.source !== 'google_trends' && (
                    <Badge className="badge-trend">
                      {keyword.source}
                    </Badge>
                  )}
                </div>
              </div>
            </CardHeader>
          </Card>
        </div>
      </section>

      {/* 主要内容 */}
      <section className="py-8 px-4">
        <div className="container mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* 左侧主要信息 */}
            <div className="lg:col-span-2 space-y-6">
              {/* 竞争难度 */}
              <Card className="idea-card-enhanced">
                <CardHeader>
                  <CardTitle className="flex items-center space-x-3">
                    <div className="p-2 bg-gradient-to-r from-red-500 to-orange-600 rounded-lg">
                      <Target className="h-5 w-5 text-white" />
                    </div>
                    <span className="text-xl font-bold text-gray-800">竞争分析</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center space-x-4 mb-4">
                    <Badge
                      className={`text-sm font-semibold ${
                        (keyword.competition_score || 0) <= 3 ? 'badge-competition-easy' :
                        (keyword.competition_score || 0) <= 7 ? 'badge-competition-medium' :
                        keyword.competition_score ? 'badge-competition-hard' :
                        'badge-pending'
                      }`}
                    >
                      {competitionInfo.text} ({keyword.competition_score || 0}/10)
                    </Badge>
                  </div>
                  {keyword.competition_description && (
                    <div className="space-y-2">
                      {(() => {
                        const competitionPoints = formatAnalysisField(keyword.competition_description)
                        return competitionPoints.length > 0 ? competitionPoints.map((point: string, index: number) => (
                          <div key={index} className="flex items-start space-x-2">
                            <div className="w-2 h-2 bg-red-500 rounded-full mt-2 flex-shrink-0"></div>
                            <span className="text-gray-700 text-sm">{point}</span>
                          </div>
                        )) : (
                          <p className="text-gray-700 leading-relaxed">{keyword.competition_description}</p>
                        )
                      })()}
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* 用户意图 */}
              {keyword.user_intent && (
                <Card className="idea-card-enhanced">
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-3">
                      <div className="p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg">
                        <User className="h-5 w-5 text-white" />
                      </div>
                      <span className="text-xl font-bold text-gray-800">用户意图</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      {(() => {
                        const intentPoints = formatAnalysisField(keyword.user_intent)
                        return intentPoints.length > 0 ? intentPoints.map((point: string, index: number) => (
                          <div key={index} className="flex items-start space-x-2">
                            <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                            <span className="text-gray-700 text-sm">{point}</span>
                          </div>
                        )) : (
                          <p className="text-gray-700 leading-relaxed text-base">{keyword.user_intent}</p>
                        )
                      })()}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* 用户痛点 */}
              {keyword.user_pain_point && (
                <Card className="idea-card-enhanced">
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-3">
                      <div className="p-2 bg-gradient-to-r from-orange-500 to-red-600 rounded-lg">
                        <AlertTriangle className="h-5 w-5 text-white" />
                      </div>
                      <span className="text-xl font-bold text-gray-800">用户痛点</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      {(() => {
                        const painPoints = formatAnalysisField(keyword.user_pain_point)
                        return painPoints.length > 0 ? painPoints.map((point: string, index: number) => (
                          <div key={index} className="flex items-start space-x-2">
                            <div className="w-2 h-2 bg-orange-500 rounded-full mt-2 flex-shrink-0"></div>
                            <span className="text-gray-700 text-sm">{point}</span>
                          </div>
                        )) : (
                          <p className="text-gray-700 leading-relaxed text-base">{keyword.user_pain_point}</p>
                        )
                      })()}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* SERP分析 */}
              {keyword.serp_analysis && (
                <Card className="idea-card-enhanced">
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-3">
                      <div className="p-2 bg-gradient-to-r from-green-500 to-teal-600 rounded-lg">
                        <BarChart3 className="h-5 w-5 text-white" />
                      </div>
                      <span className="text-xl font-bold text-gray-800">SERP分析</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      {(() => {
                        const serpPoints = formatAnalysisField(keyword.serp_analysis)
                        return serpPoints.length > 0 ? serpPoints.map((point: string, index: number) => (
                          <div key={index} className="flex items-start space-x-2">
                            <div className="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
                            <span className="text-gray-700 text-sm">{point}</span>
                          </div>
                        )) : (
                          <p className="text-gray-700 leading-relaxed text-base">{keyword.serp_analysis}</p>
                        )
                      })()}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* 详细分析报告 */}
              <Card className="idea-card-enhanced">
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="p-2 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-lg">
                        <BarChart3 className="h-5 w-5 text-white" />
                      </div>
                      <span className="text-xl font-bold text-gray-800">详细分析报告</span>
                      {/* 报告完整性指示器 */}
                      {completenessAnalysis && completenessAnalysis.completeness && (
                        <div className="flex items-center space-x-2 ml-4">
                          <div className="completeness-indicator flex items-center space-x-2 px-3 py-1 bg-gray-50 rounded-full border">
                            <span className="text-sm text-gray-600">完整性:</span>
                            <span className="text-sm font-medium text-gray-800">{completenessAnalysis.completeness}%</span>
                            <span className={`quality-badge px-2 py-1 rounded-full text-xs font-medium ${
                              completenessAnalysis.quality === 'high' ? 'bg-green-100 text-green-800' :
                              completenessAnalysis.quality === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                              'bg-red-100 text-red-800'
                            }`}>
                              {completenessAnalysis.quality === 'high' ? '优秀' :
                               completenessAnalysis.quality === 'medium' ? '良好' : '待完善'}
                            </span>
                          </div>
                        </div>
                      )}
                    </div>
                    {!report && keyword.is_analyzed && suggestions.length > 0 && (
                      <Button
                        onClick={generateReport}
                        disabled={generatingReport}
                        size="sm"
                        className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-200"
                      >
                        {generatingReport ? (
                          <>
                            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                            生成中...
                          </>
                        ) : (
                          '生成报告'
                        )}
                      </Button>
                    )}
                  </CardTitle>
                </CardHeader>
              <CardContent>
                {/* 完整性详情（仅在有数据时显示） */}
                {completenessAnalysis && completenessAnalysis.completeness && completenessAnalysis.missingSections && completenessAnalysis.missingSections.length > 0 && (
                  <div className="mb-4 p-3 bg-amber-50 border border-amber-200 rounded-lg">
                    <div className="flex items-center space-x-2 mb-2">
                      <span className="text-sm font-medium text-amber-800">报告完整性提示</span>
                    </div>
                    <div className="text-sm text-amber-700">
                      当前报告完成度 {completenessAnalysis.completeness}%，
                      已完成 {completenessAnalysis.completedSections}/{completenessAnalysis.totalSections} 个分析模块
                      {completenessAnalysis.missingSections.length > 0 && (
                        <span>，缺少: {completenessAnalysis.missingSections.join('、')}</span>
                      )}
                    </div>
                  </div>
                )}

                {generatingReport ? (
                  <div className="bg-blue-50 p-6 rounded-lg">
                    <div className="flex items-center space-x-3 mb-4">
                      <Loader2 className="h-6 w-6 animate-spin text-blue-600" />
                      <div>
                        <div className="font-medium text-blue-900">{currentStep}</div>
                        <div className="text-sm text-blue-700">正在使用AI模型进行深度分析...</div>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm text-blue-700">
                        <span>分析进度</span>
                        <span>{progress}%</span>
                      </div>
                      <div className="bg-blue-200 rounded-full h-3">
                        <div
                          className="bg-blue-600 h-3 rounded-full transition-all duration-500 ease-out"
                          style={{ width: `${progress}%` }}
                        />
                      </div>
                    </div>
                    <div className="mt-4 text-xs text-blue-600">
                      💡 提示：您可以离开此页面，分析将在后台继续进行
                    </div>
                  </div>
                ) : report ? (
                  <div className="space-y-6">
                    {/* 推荐评分 */}
                    <div className="bg-gradient-to-r from-blue-50 to-purple-50 p-4 rounded-lg border border-blue-200">
                      <h4 className="font-semibold text-blue-900 mb-2">📊 综合评分</h4>
                      <div className="flex items-center space-x-4">
                        <div className="text-3xl font-bold text-blue-600">
                          {report.recommendation_score}/10
                        </div>
                        <div className="text-sm text-blue-700">
                          <div>置信度: {Math.round((report.confidence_score || 0) * 100)}%</div>
                          <div>SEO难度: {report.seo_difficulty === 'easy' ? '简单' : report.seo_difficulty === 'medium' ? '中等' : '困难'}</div>
                        </div>
                      </div>
                    </div>

                    {/* 相似关键词分析 */}
                    {report.related_keywords && (
                      <div className="bg-gradient-to-r from-green-50 to-emerald-50 p-4 rounded-lg border border-green-200">
                        <h4 className="font-semibold text-green-900 mb-3">🔍 相似关键词分析</h4>
                        <div className="space-y-2">
                          {(() => {
                            try {
                              const relatedKeywords = typeof report.related_keywords === 'string'
                                ? JSON.parse(report.related_keywords)
                                : report.related_keywords;

                              // 处理相关关键词数据
                              const processedKeywords = relatedKeywords
                                .slice(0, 8)
                                .map((item: any, index: number) => ({
                                  keyword: item.suggestion || item.keyword || '未知关键词',
                                  similarity_score: item.similarity_score || (0.9 - index * 0.1) // 生成递减的相似度
                                }));

                              return processedKeywords.map((item: any, index: number) => (
                                <div key={index} className="flex items-center justify-between p-2 bg-white rounded border border-green-100">
                                  <span className="text-sm text-gray-700 flex-1">{item.keyword}</span>
                                  <span className="text-xs font-medium text-green-700">
                                    {(item.similarity_score * 100).toFixed(1)}%
                                  </span>
                                </div>
                              ));
                            } catch (error) {
                              return <div className="text-green-800 text-sm">相似关键词数据解析中...</div>
                            }
                          })()}
                        </div>
                      </div>
                    )}

                    {/* Google搜索数据 */}
                    {report.search_metrics && (
                      <div className="bg-gradient-to-r from-orange-50 to-amber-50 p-4 rounded-lg border border-orange-200">
                        <h4 className="font-semibold text-orange-900 mb-3">🔍 Google搜索数据</h4>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                          {(() => {
                            try {
                              const searchMetrics = typeof report.search_metrics === 'string'
                                ? JSON.parse(report.search_metrics)
                                : report.search_metrics;

                              return (
                                <>
                                  <div className="text-center p-3 bg-white rounded border border-orange-100">
                                    <div className="text-lg font-bold text-orange-600">
                                      {(searchMetrics.volume || 1000).toLocaleString()}
                                    </div>
                                    <div className="text-xs text-orange-700">月搜索量</div>
                                    <div className="text-xs text-orange-500 mt-1">
                                      基于Google搜索结果 {searchMetrics.totalResults ? `(${parseInt(searchMetrics.totalResults).toLocaleString()}条)` : ''} 算法预测
                                    </div>
                                  </div>
                                  <div className="text-center p-3 bg-white rounded border border-orange-100">
                                    <div className="text-lg font-bold text-orange-600">
                                      {searchMetrics.difficulty || 50}/100
                                    </div>
                                    <div className="text-xs text-orange-700">SEO难度</div>
                                  </div>
                                  <div className="text-center p-3 bg-white rounded border border-orange-100">
                                    <div className="text-lg font-bold text-orange-600">
                                      {searchMetrics.volume > 2000 ? '高' :
                                       searchMetrics.volume > 500 ? '中' : '低'}
                                    </div>
                                    <div className="text-xs text-orange-700">搜索热度</div>
                                  </div>
                                  <div className="text-center p-3 bg-white rounded border border-orange-100">
                                    <div className="text-lg font-bold text-orange-600">
                                      {report.competition_intensity === 'high' ? '激烈' :
                                       report.competition_intensity === 'medium' ? '中等' : '较低'}
                                    </div>
                                    <div className="text-xs text-orange-700">竞争强度</div>
                                  </div>
                                </>
                              );
                            } catch (error) {
                              return <div className="text-orange-800 text-sm col-span-4">搜索数据解析中...</div>
                            }
                          })()}
                        </div>
                      </div>
                    )}

                    {/* 前10名竞争对手分析 */}
                    {report.top_competitors_analysis && (
                      <div className="bg-gradient-to-r from-purple-50 to-pink-50 p-4 rounded-lg border border-purple-200">
                        <h4 className="font-semibold text-purple-900 mb-3">🏆 前10名竞争对手分析</h4>
                        <div className="space-y-3 w-full overflow-visible">
                          {(() => {
                            try {
                              const competitors = typeof report.top_competitors_analysis === 'string'
                                ? JSON.parse(report.top_competitors_analysis)
                                : report.top_competitors_analysis;

                              return competitors.slice(0, 10).map((comp: any, index: number) => (
                                <div key={index} className="p-3 bg-white rounded border border-purple-100 w-full">
                                  <div className="flex items-start justify-between mb-2">
                                    <div className="flex items-center space-x-2">
                                      <span className="text-xs font-bold text-purple-600 bg-purple-100 px-2 py-1 rounded">
                                        #{comp.rank || index + 1}
                                      </span>
                                      <div className="flex-1">
                                        <div className="text-sm font-medium text-gray-700 break-all">
                                          {comp.url}
                                        </div>
                                      </div>
                                    </div>
                                    <div className="flex items-center space-x-1">
                                      {(() => {
                                        // 优先显示最具体的URL类型信息
                                        const urlDescription = comp.urlDescription || comp.pathInfo

                                        if (urlDescription && urlDescription !== '根域名' && urlDescription !== '二级域名') {
                                          // 如果有具体的路径信息，显示路径信息
                                          return (
                                            <span className={`text-xs px-2 py-1 rounded ${
                                              comp.urlType === 'root_page' ? 'bg-red-100 text-red-700' :
                                              // 所有内页类型统一黄色
                                              comp.urlType === 'inner_page' || comp.urlType === 'deep_inner_page' || comp.urlType === 'i18n_inner_page' ? 'bg-yellow-100 text-yellow-700' :
                                              // 所有目录类型统一绿色
                                              comp.urlType === 'first_level_directory' || comp.urlType === 'deep_directory' || comp.urlType === 'i18n_directory' ? 'bg-green-100 text-green-700' :
                                              // API接口保持紫色
                                              comp.urlType === 'api_endpoint' || comp.urlType === 'deep_api_endpoint' ? 'bg-purple-100 text-purple-700' :
                                              // 国际化路径（非内页非目录）保持蓝色
                                              comp.urlType === 'i18n_path' ? 'bg-blue-100 text-blue-700' :
                                              'bg-gray-100 text-gray-700'
                                            }`}>
                                              {urlDescription}
                                            </span>
                                          )
                                        } else {
                                          // 如果没有具体路径信息，显示域名类型
                                          return comp.domainType === 'root_domain' ? (
                                            <span className="text-xs bg-red-100 text-red-700 px-2 py-1 rounded">根域名</span>
                                          ) : (
                                            <span className="text-xs bg-orange-100 text-orange-700 px-2 py-1 rounded">二级域名</span>
                                          )
                                        }
                                      })()}
                                      <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">
                                        {(() => {
                                          const contentType = comp.contentType || 'general'
                                          const typeMap: { [key: string]: string } = {
                                            'commercial': '商业',
                                            'informational': '信息',
                                            'list': '列表',
                                            'guide': '指南',
                                            'tutorial': '教程',
                                            'review': '评测',
                                            'news': '新闻',
                                            'blog': '博客',
                                            'discussion': '讨论',
                                            'general': '通用'
                                          }
                                          return typeMap[contentType] || contentType
                                        })()}
                                      </span>
                                    </div>
                                  </div>
                                  {/* 标题 - 完整显示不截取 */}
                                  <div className="text-sm text-gray-700 mb-1 leading-relaxed whitespace-normal break-words overflow-visible">
                                    {(() => {
                                      // 高亮关键词
                                      const keywordParts = keyword.keyword.toLowerCase().split(' ');
                                      let highlightedTitle = comp.title || '无标题';

                                      keywordParts.forEach((part: string) => {
                                        if (part.length > 2) {
                                          const regex = new RegExp(`(${part})`, 'gi');
                                          highlightedTitle = highlightedTitle.replace(regex, '<mark class="bg-red-200 text-red-800 font-semibold">$1</mark>');
                                        }
                                      });

                                      return <span dangerouslySetInnerHTML={{ __html: highlightedTitle }} />;
                                    })()}
                                  </div>

                                  {/* 描述 - 完整显示不截取 */}
                                  <div className="text-xs text-gray-500 mb-2 leading-relaxed whitespace-normal break-words overflow-visible">
                                    {(() => {
                                      // 高亮关键词
                                      const keywordParts = keyword.keyword.toLowerCase().split(' ');
                                      let highlightedSnippet = comp.snippet || '无描述';

                                      keywordParts.forEach((part: string) => {
                                        if (part.length > 2) {
                                          const regex = new RegExp(`(${part})`, 'gi');
                                          highlightedSnippet = highlightedSnippet.replace(regex, '<mark class="bg-red-200 text-red-800 font-semibold">$1</mark>');
                                        }
                                      });

                                      return <span dangerouslySetInnerHTML={{ __html: highlightedSnippet }} />;
                                    })()}
                                  </div>
                                  <div className="flex items-center justify-between mt-2 text-xs text-gray-500">
                                    <span>标题长度: {comp.titleLength}字符</span>
                                    <div className="flex space-x-2">
                                      {comp.hasNumbers && <span className="bg-yellow-100 text-yellow-700 px-1 rounded">含数字</span>}
                                      {comp.hasDate && <span className="bg-blue-100 text-blue-700 px-1 rounded">含日期</span>}
                                    </div>
                                  </div>
                                </div>
                              ));
                            } catch (error) {
                              return <div className="text-purple-800 text-sm">竞争对手数据解析中...</div>
                            }
                          })()}
                        </div>
                      </div>
                    )}

                    {/* 标题模式分析 */}
                    {report.title_patterns_analysis && (
                      <div className="bg-gradient-to-r from-indigo-50 to-blue-50 p-4 rounded-lg border border-indigo-200">
                        <h4 className="font-semibold text-indigo-900 mb-3">📝 标题模式分析</h4>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          {(() => {
                            try {
                              const titlePatterns = typeof report.title_patterns_analysis === 'string'
                                ? JSON.parse(report.title_patterns_analysis)
                                : report.title_patterns_analysis;

                              return (
                                <>
                                  <div className="space-y-3">
                                    <div className="p-3 bg-white rounded border border-indigo-100">
                                      <div className="text-sm font-medium text-indigo-900 mb-2">基础统计</div>
                                      <div className="space-y-1 text-sm text-gray-700">
                                        <div>平均标题长度: {titlePatterns.averageLength || 45}字符</div>
                                        <div>平均词汇数: {Math.round((titlePatterns.averageLength || 45) / 6)}个</div>
                                        <div>包含数字: {titlePatterns.hasNumberPattern || 3}/10</div>
                                        <div>包含日期: {titlePatterns.hasDatePattern || 1}/10</div>
                                        <div>包含问号: {titlePatterns.hasQuestionPattern || 2}/10</div>
                                      </div>
                                    </div>
                                  </div>
                                  <div className="space-y-3">
                                    <div className="p-3 bg-white rounded border border-indigo-100">
                                      <div className="text-sm font-medium text-indigo-900 mb-2">高频词汇</div>
                                      <div className="space-y-1">
                                        {(titlePatterns.commonWords || []).slice(0, 6).map((wordData: any, index: number) => (
                                          <div key={index} className="flex items-center justify-between text-sm">
                                            <span className="text-gray-700">{wordData.word || `词汇${index + 1}`}</span>
                                            <span className="text-indigo-600 font-medium">{wordData.count || 1}次</span>
                                          </div>
                                        ))}
                                        {(!titlePatterns.commonWords || titlePatterns.commonWords.length === 0) && (
                                          <div className="text-sm text-gray-500">暂无高频词汇数据</div>
                                        )}
                                      </div>
                                    </div>
                                  </div>
                                </>
                              );
                            } catch (error) {
                              return <div className="text-indigo-800 text-sm col-span-2">标题模式数据解析中...</div>
                            }
                          })()}
                        </div>
                      </div>
                    )}

                    {/* 市场机会与策略分析 */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      {/* 市场机会分析 */}
                      {report.market_opportunity && (
                        <div className="bg-gradient-to-r from-teal-50 to-cyan-50 p-4 rounded-lg border border-teal-200">
                          <h4 className="font-semibold text-teal-900 mb-3">🎯 市场机会分析</h4>
                          <div className="space-y-2">
                            {(() => {
                              const opportunities = formatAnalysisField(report.market_opportunity)
                              return opportunities.length > 0 ? opportunities.map((opportunity: string, index: number) => (
                                <div key={index} className="flex items-start space-x-2">
                                  <div className="w-2 h-2 bg-teal-500 rounded-full mt-2 flex-shrink-0"></div>
                                  <span className="text-teal-800 text-sm">{opportunity}</span>
                                </div>
                              )) : (
                                <div className="text-sm text-teal-800 leading-relaxed">
                                  {cleanMarkdown(report.market_opportunity)}
                                </div>
                              )
                            })()}
                          </div>
                        </div>
                      )}

                      {/* 内容空缺分析 */}
                      {report.content_gap_analysis && (
                        <div className="bg-gradient-to-r from-rose-50 to-pink-50 p-4 rounded-lg border border-rose-200">
                          <h4 className="font-semibold text-rose-900 mb-3">📋 内容空缺分析</h4>
                          {(() => {
                            try {
                              let contentGapData = report.content_gap_analysis

                              // 如果是字符串，尝试解析JSON
                              if (typeof contentGapData === 'string') {
                                try {
                                  contentGapData = JSON.parse(contentGapData)
                                } catch (e) {
                                  // 如果解析失败，作为文本处理
                                  return (
                                    <div className="text-sm text-rose-800 leading-relaxed whitespace-pre-line">
                                      {contentGapData || '正在分析内容空缺...'}
                                    </div>
                                  )
                                }
                              }

                              // 处理新的AI生成格式
                              if (contentGapData && typeof contentGapData === 'object') {
                                // 确保有gaps和recommendations数组
                                const gaps = contentGapData.gaps || []
                                const recommendations = contentGapData.recommendations || []
                                const opportunities = contentGapData.opportunities || 'medium'
                                const analysis = contentGapData.analysis || '基于AI分析的内容空缺评估'

                                contentGapData = {
                                  gaps,
                                  recommendations,
                                  opportunities,
                                  analysis
                                }
                              }

                              return (
                                <div className="space-y-3">
                                  {/* AI生成的内容空缺分析，一行一个显示 */}
                                  {contentGapData.gaps && contentGapData.gaps.length > 0 && (
                                    <div>
                                      <div className="text-xs font-medium text-rose-900 mb-2">发现的内容空缺：</div>
                                      <div className="space-y-2">
                                        {contentGapData.gaps.map((gap: any, index: number) => (
                                          <div key={index} className="flex items-start space-x-2">
                                            <div className="w-2 h-2 bg-rose-500 rounded-full mt-2 flex-shrink-0"></div>
                                            <span className="text-rose-800 text-sm">
                                              {typeof gap === 'string' ? gap : gap.reason || gap.type || '内容空缺'}
                                            </span>
                                          </div>
                                        ))}
                                      </div>
                                    </div>
                                  )}

                                  {contentGapData.recommendations && contentGapData.recommendations.length > 0 && (
                                    <div>
                                      <div className="text-xs font-medium text-rose-900 mb-2">优化建议：</div>
                                      <div className="space-y-2">
                                        {contentGapData.recommendations.map((rec: string, index: number) => (
                                          <div key={index} className="flex items-start space-x-2">
                                            <div className="w-2 h-2 bg-rose-500 rounded-full mt-2 flex-shrink-0"></div>
                                            <span className="text-rose-800 text-sm">{rec}</span>
                                          </div>
                                        ))}
                                      </div>
                                    </div>
                                  )}

                                  {/* 显示机会等级 */}
                                  {contentGapData.opportunities && (
                                    <div className="flex justify-end">
                                      <span className={`px-2 py-1 rounded text-white text-xs ${
                                        contentGapData.opportunities === 'high' ? 'bg-green-500' :
                                        contentGapData.opportunities === 'medium' ? 'bg-yellow-500' : 'bg-red-500'
                                      }`}>
                                        机会等级: {contentGapData.opportunities === 'high' ? '高' :
                                                  contentGapData.opportunities === 'medium' ? '中' : '低'}
                                      </span>
                                    </div>
                                  )}

                                  {/* 显示分析总结 */}
                                  {contentGapData.analysis && (
                                    <div className="text-xs text-rose-700 bg-rose-100 p-2 rounded">
                                      {contentGapData.analysis}
                                    </div>
                                  )}
                                </div>
                              )
                            } catch (e) {
                              return (
                                <div className="text-sm text-rose-800 leading-relaxed">
                                  <div className="flex items-center space-x-2 mb-2">
                                    <span className="text-rose-600">⚠️</span>
                                    <span className="font-medium">内容空缺分析</span>
                                  </div>
                                  <div>
                                    {typeof report.content_gap_analysis === 'string'
                                      ? cleanMarkdown(report.content_gap_analysis)
                                      : '正在重新分析内容空缺，请稍后刷新页面查看结果'}
                                  </div>
                                </div>
                              )
                            }
                          })()}
                        </div>
                      )}

                      {/* 内容策略建议 */}
                      {report.content_strategy && (
                        <div className="bg-gradient-to-r from-violet-50 to-purple-50 p-4 rounded-lg border border-violet-200">
                          <h4 className="font-semibold text-violet-900 mb-3">💡 内容策略建议</h4>
                          <div className="space-y-2">
                            {(() => {
                              const strategies = formatAnalysisField(report.content_strategy)
                              return strategies.length > 0 ? strategies.map((strategy: string, index: number) => (
                                <div key={index} className="flex items-start space-x-2">
                                  <div className="w-2 h-2 bg-violet-500 rounded-full mt-2 flex-shrink-0"></div>
                                  <span className="text-violet-800 text-sm">{strategy}</span>
                                </div>
                              )) : (
                                <div className="text-sm text-violet-800 leading-relaxed">
                                  {report.content_strategy}
                                </div>
                              )
                            })()}
                          </div>
                        </div>
                      )}

                      {/* 目标受众分析 */}
                      {report.target_audience && (
                        <div className="bg-gradient-to-r from-emerald-50 to-green-50 p-4 rounded-lg border border-emerald-200">
                          <h4 className="font-semibold text-emerald-900 mb-3">👥 目标受众分析</h4>
                          <div className="space-y-2">
                            {(() => {
                              const audiences = formatAnalysisField(report.target_audience)
                              return audiences.length > 0 ? audiences.map((audience: string, index: number) => (
                                <div key={index} className="flex items-start space-x-2">
                                  <div className="w-2 h-2 bg-emerald-500 rounded-full mt-2 flex-shrink-0"></div>
                                  <span className="text-emerald-800 text-sm">{audience}</span>
                                </div>
                              )) : (
                                <div className="text-sm text-emerald-800 leading-relaxed">
                                  {report.target_audience}
                                </div>
                              )
                            })()}
                          </div>
                        </div>
                      )}

                      {/* 竞争优势分析 */}
                      {(report.competitive_advantages || report.competitive_advantage_analysis) && (
                        <div className="bg-gradient-to-r from-amber-50 to-yellow-50 p-4 rounded-lg border border-amber-200">
                          <h4 className="font-semibold text-amber-900 mb-3">⚡ 竞争优势分析</h4>

                          {/* 智能分析结果 */}
                          {report.competitive_advantage_analysis && (
                            <div className="space-y-3">
                              {/* 根域名分析 */}
                              <div className="text-sm text-amber-800 leading-relaxed">
                                <strong>竞争格局：</strong>{report.competitive_advantage_analysis.rootDomainAnalysis}
                              </div>

                              {/* 推荐网站类型 */}
                              <div className="bg-amber-100 p-3 rounded-md">
                                <div className="text-sm font-medium text-amber-900 mb-1">
                                  🎯 推荐网站类型
                                </div>
                                <div className="text-sm text-amber-800">
                                  <span className="font-semibold">{report.competitive_advantage_analysis.websiteTypeRecommendation?.primary}</span>
                                  {report.competitive_advantage_analysis.websiteTypeRecommendation?.secondary && (
                                    <span> / {report.competitive_advantage_analysis.websiteTypeRecommendation.secondary}</span>
                                  )}
                                </div>
                                {report.competitive_advantage_analysis.websiteTypeRecommendation?.reason && (
                                  <div className="text-xs text-amber-700 mt-1">
                                    {report.competitive_advantage_analysis.websiteTypeRecommendation.reason}
                                  </div>
                                )}
                              </div>

                              {/* 战略优势 */}
                              {report.competitive_advantage_analysis.strategicAdvantages &&
                               report.competitive_advantage_analysis.strategicAdvantages.length > 0 && (
                                <div>
                                  <div className="text-sm font-medium text-amber-900 mb-2">
                                    🚀 核心优势
                                  </div>
                                  <ul className="text-sm text-amber-800 space-y-1">
                                    {report.competitive_advantage_analysis.strategicAdvantages.map((advantage: string, index: number) => (
                                      <li key={index} className="flex items-start">
                                        <span className="text-amber-600 mr-2">•</span>
                                        {advantage}
                                      </li>
                                    ))}
                                  </ul>
                                </div>
                              )}

                              {/* 行动建议 */}
                              {report.competitive_advantage_analysis.actionableSteps &&
                               report.competitive_advantage_analysis.actionableSteps.length > 0 && (
                                <div>
                                  <div className="text-sm font-medium text-amber-900 mb-2">
                                    📋 行动建议
                                  </div>
                                  <ul className="text-sm text-amber-800 space-y-1">
                                    {report.competitive_advantage_analysis.actionableSteps.map((step: string, index: number) => (
                                      <li key={index} className="flex items-start">
                                        <span className="text-amber-600 mr-2">{index + 1}.</span>
                                        {step}
                                      </li>
                                    ))}
                                  </ul>
                                </div>
                              )}
                            </div>
                          )}

                          {/* 传统分析结果（兜底） */}
                          {!report.competitive_advantage_analysis && report.competitive_advantages && (
                            <div className="space-y-2">
                              {(() => {
                                const advantages = formatAnalysisField(report.competitive_advantages)
                                return advantages.length > 0 ? advantages.map((advantage: string, index: number) => (
                                  <div key={index} className="flex items-start space-x-2">
                                    <div className="w-2 h-2 bg-amber-500 rounded-full mt-2 flex-shrink-0"></div>
                                    <span className="text-amber-800 text-sm">{advantage}</span>
                                  </div>
                                )) : (
                                  <div className="text-sm text-amber-800 leading-relaxed">
                                    {report.competitive_advantages}
                                  </div>
                                )
                              })()}
                            </div>
                          )}
                        </div>
                      )}

                      {/* 变现潜力分析 */}
                      {report.monetization_potential && (
                        <div className="bg-gradient-to-r from-lime-50 to-green-50 p-4 rounded-lg border border-lime-200">
                          <h4 className="font-semibold text-lime-900 mb-3">💰 变现潜力分析</h4>
                          <div className="space-y-2">
                            {(() => {
                              const potentials = formatAnalysisField(report.monetization_potential)
                              return potentials.length > 0 ? potentials.map((potential: string, index: number) => (
                                <div key={index} className="flex items-start space-x-2">
                                  <div className="w-2 h-2 bg-lime-500 rounded-full mt-2 flex-shrink-0"></div>
                                  <span className="text-lime-800 text-sm">{potential}</span>
                                </div>
                              )) : (
                                <div className="text-sm text-lime-800 leading-relaxed">
                                  {report.monetization_potential}
                                </div>
                              )
                            })()}
                          </div>
                        </div>
                      )}
                    </div>

                    {/* 趋势与季节性分析 */}
                    {(report.trend_analysis || report.seasonal_patterns) && (
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        {report.trend_analysis && (
                          <div className="bg-gradient-to-r from-sky-50 to-blue-50 p-4 rounded-lg border border-sky-200">
                            <h4 className="font-semibold text-sky-900 mb-3">📈 趋势分析</h4>
                            <div className="space-y-2">
                              {(() => {
                                const trends = formatAnalysisField(report.trend_analysis)
                                return trends.length > 0 ? trends.map((trend: string, index: number) => (
                                  <div key={index} className="flex items-start space-x-2">
                                    <div className="w-2 h-2 bg-sky-500 rounded-full mt-2 flex-shrink-0"></div>
                                    <span className="text-sky-800 text-sm">{trend}</span>
                                  </div>
                                )) : (
                                  <div className="text-sm text-sky-800 leading-relaxed">
                                    {report.trend_analysis}
                                  </div>
                                )
                              })()}
                            </div>
                          </div>
                        )}

                        {report.seasonal_patterns && (
                          <div className="bg-gradient-to-r from-slate-50 to-gray-50 p-4 rounded-lg border border-slate-200">
                            <h4 className="font-semibold text-slate-900 mb-3">🗓️ 季节性模式</h4>
                            <div className="space-y-2">
                              {(() => {
                                const patterns = formatAnalysisField(report.seasonal_patterns)
                                return patterns.length > 0 ? patterns.map((pattern: string, index: number) => (
                                  <div key={index} className="flex items-start space-x-2">
                                    <div className="w-2 h-2 bg-slate-500 rounded-full mt-2 flex-shrink-0"></div>
                                    <span className="text-slate-800 text-sm">{pattern}</span>
                                  </div>
                                )) : (
                                  <div className="text-sm text-slate-800 leading-relaxed">
                                    {report.seasonal_patterns}
                                  </div>
                                )
                              })()}
                            </div>
                          </div>
                        )}
                      </div>
                    )}

                    {/* 详细分析报告卡片 */}
                    {report.ai_analysis && (
                      <div className="space-y-4">
                        {parseReportSections(report.ai_analysis).map((section, index) => (
                          <div key={index} className={`border rounded-lg p-4 ${getSectionCardStyle(section.type)}`}>
                            <h4 className={`font-semibold mb-3 flex items-center space-x-2 ${getSectionTitleStyle(section.type)}`}>
                              <span>{section.emoji}</span>
                              <span>{section.title}</span>
                            </h4>
                            <div className={`space-y-2 ${getSectionContentStyle(section.type)}`}>
                              {section.content.map((item, itemIndex) => (
                                <div key={itemIndex} className="flex items-start space-x-2">
                                  {item.type === 'list' ? (
                                    <>
                                      <div className="w-2 h-2 bg-current rounded-full mt-2 flex-shrink-0 opacity-60"></div>
                                      <span className="text-sm">{item.text}</span>
                                    </>
                                  ) : item.type === 'bold' ? (
                                    <div className="font-medium text-sm">{item.text}</div>
                                  ) : (
                                    <span className="text-sm">{item.text}</span>
                                  )}
                                </div>
                              ))}
                            </div>
                          </div>
                        ))}
                      </div>
                    )}

                    {/* 主要竞争对手和快速行动建议 */}
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                      {/* 主要竞争对手根域名 */}
                      {report.top_competitors && (
                        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                          <h4 className="font-semibold text-blue-900 mb-3">🏢 当前主要同行</h4>
                          <div className="space-y-2">
                            {(() => {
                              try {
                                const competitors = typeof report.top_competitors === 'string'
                                  ? JSON.parse(report.top_competitors)
                                  : report.top_competitors
                                return Array.isArray(competitors) && competitors.length > 0 ? competitors.slice(0, 10).map((competitor: any, index: number) => (
                                  <div key={index} className="flex items-center space-x-3 p-2 bg-white rounded border border-blue-100">
                                    <div className="w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-xs font-bold flex-shrink-0">
                                      {competitor.rank || index + 1}
                                    </div>
                                    <div className="flex-1 min-w-0">
                                      <div className="text-blue-900 font-medium text-sm truncate">
                                        {competitor.domain || '未知域名'}
                                      </div>
                                    </div>
                                    {competitor.isRootDomain && (
                                      <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">根域名</span>
                                    )}
                                  </div>
                                )) : (
                                  <div className="text-blue-800 text-sm">暂无竞争对手数据</div>
                                )
                              } catch (error) {
                                return (
                                  <div className="text-blue-800 text-sm">竞争对手数据格式错误</div>
                                )
                              }
                            })()}
                          </div>
                        </div>
                      )}

                      {/* 快速行动建议 */}
                      {report.action_suggestions && (
                        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                          <h4 className="font-semibold text-green-900 mb-3">🎯 快速行动建议</h4>
                          <div className="space-y-2">
                            {(() => {
                              try {
                                const suggestions = typeof report.action_suggestions === 'string'
                                  ? JSON.parse(report.action_suggestions)
                                  : report.action_suggestions
                                return Array.isArray(suggestions) ? suggestions.map((suggestion: string, index: number) => (
                                  <div key={index} className="flex items-start space-x-2">
                                    <div className="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
                                    <span className="text-green-800 text-sm">{suggestion}</span>
                                  </div>
                                )) : (
                                  <div className="text-green-800">{suggestions}</div>
                                )
                              } catch (error) {
                                return (
                                  <div className="text-green-800">
                                    {typeof report.action_suggestions === 'string'
                                      ? report.action_suggestions
                                      : '行动建议数据格式错误'}
                                  </div>
                                )
                              }
                            })()}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    <BarChart3 className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    {!keyword.is_analyzed ? (
                      <div>
                        <p className="text-orange-600 font-medium">关键词尚未分析</p>
                        <p className="text-sm mt-2">请先在首页或管理后台对关键词进行AI分析，然后再生成详细报告</p>
                      </div>
                    ) : suggestions.length === 0 ? (
                      <div>
                        <p className="text-orange-600 font-medium">缺少相关词数据</p>
                        <p className="text-sm mt-2">请先获取关键词的相关搜索建议，然后再生成详细报告</p>
                      </div>
                    ) : (
                      <div>
                        <p>点击"生成报告"按钮获取详细的关键词分析报告</p>
                        <p className="text-sm mt-2">报告将包含市场机会、竞争分析、内容策略等深度洞察</p>
                      </div>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* 导出功能 */}
            {report && (
              <Card className="idea-card-enhanced mt-6">
                <CardHeader>
                  <CardTitle className="flex items-center space-x-3">
                    <div className="p-2 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg">
                      <Download className="h-5 w-5 text-white" />
                    </div>
                    <span className="text-lg font-bold text-gray-800">导出报告</span>
                  </CardTitle>
                  <CardDescription className="text-gray-600">
                    导出完整的关键词分析报告
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex gap-3">
                    <Button
                      onClick={() => exportReport('markdown')}
                      className="flex-1 bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white border-0 shadow-md hover:shadow-lg transition-all duration-200"
                    >
                      <Download className="h-4 w-4 mr-2" />
                      📄 Markdown
                    </Button>
                    <Button
                      onClick={() => exportReport('text')}
                      className="flex-1 bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white border-0 shadow-md hover:shadow-lg transition-all duration-200"
                    >
                      <Download className="h-4 w-4 mr-2" />
                      📝 文本
                    </Button>
                  </div>
                  <div className="mt-3 text-xs text-gray-500 text-center">
                    导出包含完整分析数据、推荐域名、相关词等信息
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

            {/* 右侧信息 */}
            <div className="space-y-6">
              {/* 推荐域名 */}
              {keyword.recommended_domains && keyword.recommended_domains.length > 0 && (
                <Card className="idea-card-enhanced">
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-3">
                      <div className="p-2 bg-gradient-to-r from-emerald-500 to-teal-600 rounded-lg">
                        <Globe className="h-5 w-5 text-white" />
                      </div>
                      <span className="text-lg font-bold text-gray-800">推荐域名</span>
                    </CardTitle>
                    <CardDescription className="text-gray-600">
                      点击查询域名可用性
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {keyword.recommended_domains.map((domain, index) => (
                        <div key={index} className="p-3 bg-gradient-to-r from-gray-50 to-gray-100 rounded-lg border border-gray-200 hover:shadow-md transition-all duration-200">
                          <div className="flex items-center justify-between mb-2">
                            <span className="font-mono text-sm font-medium text-gray-700">{domain.domain}</span>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => window.open(domain.check_url, '_blank')}
                              className="flex items-center space-x-1 hover:bg-blue-50 hover:border-blue-300 hover:text-blue-600 transition-all duration-200"
                            >
                              <ExternalLink className="h-3 w-3" />
                              <span>查询</span>
                            </Button>
                          </div>

                          {/* 只为第一个域名添加DNS查询功能 */}
                          {index === 0 && (
                            <div className="flex items-center justify-between pt-2 border-t border-gray-200">
                              <div className="flex items-center space-x-2">
                                {domainCheckStatus.result ? (
                                  <DomainStatusIndicator
                                    status={domainCheckStatus.result.status}
                                    message={domainCheckStatus.result.message}
                                  />
                                ) : (
                                  <span className="text-xs text-gray-500">DNS查询结果仅供参考</span>
                                )}
                              </div>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => checkDomainRegistration(domain.domain)}
                                disabled={domainCheckStatus.checking}
                                className="flex items-center space-x-1 hover:bg-green-50 hover:border-green-300 hover:text-green-600 transition-all duration-200"
                              >
                                {domainCheckStatus.checking ? (
                                  <>
                                    <Loader2 className="h-3 w-3 animate-spin" />
                                    <span>检查中</span>
                                  </>
                                ) : (
                                  <>
                                    <Search className="h-3 w-3" />
                                    <span>检查域名</span>
                                  </>
                                )}
                              </Button>
                            </div>
                          )}
                        </div>
                      ))}

                      {/* 智能域名推荐按钮 */}
                      <div className="border-t pt-3 mt-3">
                        <Button
                          onClick={() => setShowDomainCountDialog(true)}
                          size="sm"
                          variant="outline"
                          disabled={generatingDomains}
                          className="w-full flex items-center justify-center space-x-2 hover:bg-orange-50 hover:border-orange-300 hover:text-orange-600 transition-all duration-200"
                        >
                          {generatingDomains ? (
                            <>
                              <Loader2 className="h-3 w-3 animate-spin" />
                              <span>生成中...</span>
                            </>
                          ) : (
                            <>
                              <Star className="h-3 w-3" />
                              <span>这个被注册了？</span>
                            </>
                          )}
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* AI生成的域名推荐 */}
              {aiDomains.length > 0 && (
                <Card className="idea-card-enhanced">
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-3">
                      <div className="p-2 bg-gradient-to-r from-purple-500 to-pink-600 rounded-lg">
                        <Star className="h-5 w-5 text-white" />
                      </div>
                      <span className="text-lg font-bold text-gray-800">AI智能推荐</span>
                    </CardTitle>
                    <CardDescription className="text-gray-600">
                      基于关键词智能生成的域名推荐
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {aiDomains.map((domain, index) => (
                        <div key={index} className="p-3 bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg border border-purple-200 hover:shadow-md transition-all duration-200">
                          <div className="flex items-center justify-between mb-2">
                            <span className="font-mono text-sm font-medium text-gray-700">{domain.domain}</span>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => window.open(domain.check_url, '_blank')}
                              className="flex items-center space-x-1 hover:bg-purple-50 hover:border-purple-300 hover:text-purple-600 transition-all duration-200"
                            >
                              <ExternalLink className="h-3 w-3" />
                              <span>查询</span>
                            </Button>
                          </div>
                          <p className="text-xs text-gray-500">{domain.reason}</p>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* 相关词 */}
              {suggestions.length > 0 && (
                <Card className="idea-card-enhanced">
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-3">
                      <div className="p-2 bg-gradient-to-r from-purple-500 to-pink-600 rounded-lg">
                        <Search className="h-5 w-5 text-white" />
                      </div>
                      <span className="text-lg font-bold text-gray-800">相关词</span>
                      <Badge variant="outline" className="border-purple-200 text-purple-700 bg-purple-50">
                        {suggestions.length} 个
                      </Badge>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex flex-wrap gap-2">
                        {suggestions.map((suggestion, index) => (
                          <Badge
                            key={suggestion.id || index}
                            className="text-xs bg-purple-100 text-purple-700 border-purple-200 hover:bg-purple-200 transition-colors duration-200 cursor-pointer"
                            onClick={() => navigator.clipboard.writeText(suggestion.suggestion)}
                            title="点击复制"
                          >
                            {suggestion.suggestion}
                          </Badge>
                        ))}
                      </div>

                      {/* 批量域名检查功能 */}
                      <div className="border-t pt-4">
                        <div className="flex items-center justify-between mb-3">
                          <h4 className="text-sm font-medium text-gray-700">域名检查</h4>
                          <Button
                            onClick={batchCheckSuggestionDomains}
                            disabled={batchDomainCheck.checking}
                            size="sm"
                            variant="outline"
                            className="flex items-center space-x-1 hover:bg-blue-50 hover:border-blue-300 hover:text-blue-600"
                          >
                            {batchDomainCheck.checking ? (
                              <>
                                <Loader2 className="h-3 w-3 animate-spin" />
                                <span>检查中...</span>
                              </>
                            ) : (
                              <>
                                <Search className="h-3 w-3" />
                                <span>批量检查域名</span>
                              </>
                            )}
                          </Button>
                        </div>

                        {/* 检查结果统计 */}
                        {batchDomainCheck.stats && (
                          <div className="mb-3 p-2 bg-gray-50 rounded-lg">
                            <div className="flex items-center justify-between text-xs">
                              <span className="text-gray-600">
                                共检查 {batchDomainCheck.stats.total} 个域名
                              </span>
                              <div className="flex space-x-3">
                                <span className="text-green-600">
                                  可用: {batchDomainCheck.stats.available}
                                </span>
                                <span className="text-red-600">
                                  已注册: {batchDomainCheck.stats.registered}
                                </span>
                                <span className="text-gray-600">
                                  失败: {batchDomainCheck.stats.failed}
                                </span>
                              </div>
                            </div>
                          </div>
                        )}

                        {/* 检查结果列表 */}
                        {batchDomainCheck.results.length > 0 && (
                          <div className="space-y-2">
                            {/* 未注册域名 */}
                            {batchDomainCheck.results.filter(r => r.registered === false).length > 0 && (
                              <div>
                                <div className="flex items-center justify-between mb-2">
                                  <h5 className="text-xs font-medium text-green-700">未注册域名</h5>
                                  <Button
                                    onClick={copyAvailableDomains}
                                    size="sm"
                                    variant="outline"
                                    className="text-xs px-2 py-1 h-auto hover:bg-green-50 hover:border-green-300 hover:text-green-600"
                                  >
                                    复制全部
                                  </Button>
                                </div>
                                <div className="space-y-1">
                                  {batchDomainCheck.results
                                    .filter(result => result.registered === false)
                                    .map((result, index) => (
                                      <div key={index} className="flex items-center justify-between p-2 bg-green-50 rounded border border-green-200">
                                        <div className="flex-1">
                                          <div className="text-xs font-mono text-green-700">{result.domain}</div>
                                          <div className="text-xs text-green-600">来源: {result.suggestion}</div>
                                        </div>
                                        <Button
                                          onClick={() => window.open(`https://wanwang.aliyun.com/domain/searchresult/?keyword=${encodeURIComponent(result.domain.split('.')[0])}`, '_blank')}
                                          size="sm"
                                          variant="outline"
                                          className="text-xs px-2 py-1 h-auto hover:bg-blue-50 hover:border-blue-300 hover:text-blue-600"
                                        >
                                          查询
                                        </Button>
                                      </div>
                                    ))}
                                </div>
                              </div>
                            )}

                            {/* 已注册域名 */}
                            {batchDomainCheck.results.filter(r => r.registered === true).length > 0 && (
                              <div>
                                <h5 className="text-xs font-medium text-red-700 mb-2">已注册域名</h5>
                                <div className="space-y-1">
                                  {batchDomainCheck.results
                                    .filter(result => result.registered === true)
                                    .map((result, index) => (
                                      <div key={index} className="flex items-center justify-between p-2 bg-red-50 rounded border border-red-200">
                                        <div className="flex-1">
                                          <div className="text-xs font-mono text-red-700">{result.domain}</div>
                                          <div className="text-xs text-red-600">来源: {result.suggestion}</div>
                                        </div>
                                        <span className="text-xs text-red-600">❌ 已注册</span>
                                      </div>
                                    ))}
                                </div>
                              </div>
                            )}

                            {/* 查询失败域名 */}
                            {batchDomainCheck.results.filter(r => r.registered === null).length > 0 && (
                              <div>
                                <h5 className="text-xs font-medium text-gray-700 mb-2">查询失败</h5>
                                <div className="space-y-1">
                                  {batchDomainCheck.results
                                    .filter(result => result.registered === null)
                                    .map((result, index) => (
                                      <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded border border-gray-200">
                                        <div className="flex-1">
                                          <div className="text-xs font-mono text-gray-700">{result.domain}</div>
                                          <div className="text-xs text-gray-600">来源: {result.suggestion}</div>
                                        </div>
                                        <span className="text-xs text-gray-600">❓ {result.message}</span>
                                      </div>
                                    ))}
                                </div>
                              </div>
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Logo生成 */}
              <Card className="idea-card-enhanced">
                <CardHeader>
                  <CardTitle className="flex items-center space-x-3">
                    <div className="p-2 bg-gradient-to-r from-pink-500 to-purple-600 rounded-lg">
                      <Palette className="h-5 w-5 text-white" />
                    </div>
                    <span className="text-lg font-bold text-gray-800">生成Logo</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {!logoData ? (
                    <div className="text-center py-6">
                      <Button
                        onClick={() => setShowLogoCountDialog(true)}
                        disabled={generatingLogo}
                        className="bg-gradient-to-r from-pink-500 to-purple-600 hover:from-pink-600 hover:to-purple-700 text-white"
                      >
                        {generatingLogo ? (
                          <>
                            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                            {logoStep || '生成中...'}
                          </>
                        ) : (
                          <>
                            <Palette className="h-4 w-4 mr-2" />
                            生成Logo
                          </>
                        )}
                      </Button>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {/* Logo展示区域 */}
                      <div className={`grid gap-4 ${logoData.images.length === 1 ? 'grid-cols-1 justify-items-center' : logoData.images.length === 2 ? 'grid-cols-2' : 'grid-cols-2 md:grid-cols-3'}`}>
                        {logoData.images.map((imageUrl, index) => (
                          <div key={index} className="relative bg-white rounded-lg border-2 border-gray-200 p-4 shadow-sm group">
                            <img
                              src={imageUrl}
                              alt={`Generated Logo ${index + 1}`}
                              className="w-32 h-32 object-contain mx-auto"
                            />
                            {/* 单个图片下载按钮 */}
                            <Button
                              onClick={() => downloadLogo(imageUrl, index)}
                              size="sm"
                              variant="outline"
                              className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 p-1 h-auto"
                            >
                              <Download className="h-3 w-3" />
                            </Button>
                          </div>
                        ))}
                      </div>

                      {/* 操作按钮组 */}
                      <div className="flex flex-wrap gap-2 justify-center">
                        <Button
                          onClick={downloadAllLogos}
                          size="sm"
                          variant="outline"
                          className="flex items-center space-x-1 hover:bg-blue-50 hover:border-blue-300 hover:text-blue-600"
                        >
                          <Download className="h-3 w-3" />
                          <span>下载全部</span>
                        </Button>
                        <Button
                          onClick={copyLogoPrompt}
                          size="sm"
                          variant="outline"
                          className="flex items-center space-x-1 hover:bg-green-50 hover:border-green-300 hover:text-green-600"
                        >
                          <Copy className="h-3 w-3" />
                          <span>复制Prompt</span>
                        </Button>
                        <Button
                          onClick={() => setShowLogoCountDialog(true)}
                          size="sm"
                          variant="outline"
                          disabled={generatingLogo}
                          className="flex items-center space-x-1 hover:bg-purple-50 hover:border-purple-300 hover:text-purple-600"
                        >
                          <RotateCcw className="h-3 w-3" />
                          <span>再生成</span>
                        </Button>
                      </div>

                      {/* Prompt展示 */}
                      <div className="bg-gray-50 rounded-lg p-3">
                        <span className="text-xs text-gray-500 block mb-1">生成Prompt:</span>
                        <p className="text-sm text-gray-700 break-words">{logoData.prompt}</p>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* 基本信息 */}
              <Card className="idea-card-enhanced">
                <CardHeader>
                  <CardTitle className="flex items-center space-x-3">
                    <div className="p-2 bg-gradient-to-r from-gray-500 to-slate-600 rounded-lg">
                      <BarChart3 className="h-5 w-5 text-white" />
                    </div>
                    <span className="text-lg font-bold text-gray-800">基本信息</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="p-3 bg-gray-50 rounded-lg">
                    <span className="text-sm text-gray-500 block mb-1">创建时间</span>
                    <p className="font-medium text-gray-800">{keyword.created_at_formatted || '未知'}</p>
                  </div>
                  {keyword.analyzed_at_formatted && (
                    <div className="p-3 bg-gray-50 rounded-lg">
                      <span className="text-sm text-gray-500 block mb-1">分析时间</span>
                      <p className="font-medium text-gray-800">{keyword.analyzed_at_formatted}</p>
                    </div>
                  )}
                  <div className="p-3 bg-gray-50 rounded-lg">
                    <span className="text-sm text-gray-500 block mb-1">分析状态</span>
                    <p className="font-medium text-gray-800">
                      {keyword.is_analyzed ? '已完成分析' : '待分析'}
                    </p>
                  </div>


                </CardContent>
              </Card>

              {/* 相关导航 */}
              <Card className="idea-card-enhanced">
                <CardHeader>
                  <CardTitle className="flex items-center space-x-3">
                    <div className="p-2 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-lg">
                      <Navigation className="h-5 w-5 text-white" />
                    </div>
                    <span className="text-lg font-bold text-gray-800">词条导航</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  {prevKeyword ? (
                    <a
                      href={`/keyword/${prevKeyword.id}`}
                      className="block p-3 bg-gray-50 rounded-lg hover:bg-blue-50 hover:border-blue-200 border border-gray-200 transition-all duration-200 group no-underline"
                    >
                      <div className="flex items-center space-x-3">
                        <div className="p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg group-hover:scale-105 transition-transform duration-200">
                          <ArrowLeft className="h-4 w-4 text-white" />
                        </div>
                        <div className="flex-1 min-w-0">
                          <span className="font-medium text-gray-800 truncate">{prevKeyword.keyword}</span>
                        </div>
                      </div>
                    </a>
                  ) : (
                    <div className="p-3 bg-gray-50 rounded-lg border border-dashed border-gray-300">
                      <div className="flex items-center space-x-3">
                        <div className="p-2 bg-gray-300 rounded-lg">
                          <ArrowLeft className="h-4 w-4 text-gray-500" />
                        </div>
                        <div className="flex-1">
                          <span className="text-sm text-gray-400">暂无更早词条</span>
                        </div>
                      </div>
                    </div>
                  )}

                  {nextKeyword ? (
                    <a
                      href={`/keyword/${nextKeyword.id}`}
                      className="block p-3 bg-gray-50 rounded-lg hover:bg-green-50 hover:border-green-200 border border-gray-200 transition-all duration-200 group no-underline"
                    >
                      <div className="flex items-center space-x-3">
                        <div className="p-2 bg-gradient-to-r from-green-500 to-teal-600 rounded-lg group-hover:scale-105 transition-transform duration-200">
                          <ArrowLeft className="h-4 w-4 text-white rotate-180" />
                        </div>
                        <div className="flex-1 min-w-0">
                          <span className="font-medium text-gray-800 truncate">{nextKeyword.keyword}</span>
                        </div>
                      </div>
                    </a>
                  ) : (
                    <div className="p-3 bg-gray-50 rounded-lg border border-dashed border-gray-300">
                      <div className="flex items-center space-x-3">
                        <div className="p-2 bg-gray-300 rounded-lg">
                          <ArrowLeft className="h-4 w-4 text-gray-500 rotate-180" />
                        </div>
                        <div className="flex-1">
                          <span className="text-sm text-gray-400">暂无更新词条</span>
                        </div>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* 用户备注 */}
              {keyword.user_comment && (
                <Card className="idea-card-enhanced">
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-3">
                      <div className={`p-2 rounded-lg ${
                        keyword.user_comment === '有效'
                          ? 'bg-gradient-to-r from-blue-500 to-indigo-600'
                          : 'bg-gradient-to-r from-red-500 to-pink-600'
                      }`}>
                        <Star className="h-5 w-5 text-white" />
                      </div>
                      <span className="text-lg font-bold text-gray-800">用户评价</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className={`p-3 rounded-lg ${
                      keyword.user_comment === '有效'
                        ? 'bg-blue-50 border border-blue-200'
                        : 'bg-red-50 border border-red-200'
                    }`}>
                      <p className={`font-medium ${
                        keyword.user_comment === '有效'
                          ? 'text-blue-800'
                          : 'text-red-800 line-through'
                      }`}>
                        {keyword.user_comment}
                      </p>
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          </div>
        </div>
      </section>

      {/* 公共底部 */}
      <FrontendFooter />

      {/* 通知组件 */}
      <Toaster position="top-right" richColors closeButton duration={5000} />

      {/* 域名数量选择对话框 */}
      {showDomainCountDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold mb-4">选择生成数量</h3>
            <p className="text-gray-600 mb-4">请选择要生成的域名推荐数量：</p>

            <div className="grid grid-cols-3 gap-3 mb-6">
              {[5, 10, 20].map(count => (
                <button
                  key={count}
                  onClick={() => setSelectedDomainCount(count)}
                  className={`p-3 rounded-lg border-2 transition-all duration-200 ${
                    selectedDomainCount === count
                      ? 'border-blue-500 bg-blue-50 text-blue-700'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  {count} 个
                </button>
              ))}
            </div>

            <div className="flex space-x-3">
              <Button
                onClick={() => setShowDomainCountDialog(false)}
                variant="outline"
                className="flex-1"
              >
                取消
              </Button>
              <Button
                onClick={generateAIDomains}
                disabled={generatingDomains}
                className="flex-1 bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700"
              >
                {generatingDomains ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    生成中...
                  </>
                ) : (
                  '开始生成'
                )}
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Logo数量选择对话框 */}
      {showLogoCountDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold mb-4">选择生成数量</h3>
            <p className="text-gray-600 mb-4">请选择要生成的Logo数量：</p>

            <div className="grid grid-cols-4 gap-3 mb-6">
              {[1, 2, 3, 4].map(count => (
                <button
                  key={count}
                  onClick={() => setSelectedLogoCount(count)}
                  className={`p-3 rounded-lg border-2 transition-all duration-200 ${
                    selectedLogoCount === count
                      ? 'border-pink-500 bg-pink-50 text-pink-700'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  {count} 张
                </button>
              ))}
            </div>

            <div className="flex space-x-3">
              <Button
                onClick={() => setShowLogoCountDialog(false)}
                variant="outline"
                className="flex-1"
              >
                取消
              </Button>
              <Button
                onClick={generateLogo}
                disabled={generatingLogo}
                className="flex-1 bg-gradient-to-r from-pink-500 to-purple-600 hover:from-pink-600 hover:to-purple-700"
              >
                {generatingLogo ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    {logoStep || '生成中...'}
                  </>
                ) : (
                  '开始生成'
                )}
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
