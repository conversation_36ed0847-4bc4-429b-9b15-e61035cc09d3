import type { Metadata } from "next";

export const metadata: Metadata = {
  title: "CatchROI计算器 | CatchIdeas",
  description: "CatchROI优化难度回报率计算工具，使用公式CatchROI = Volume × CPC ÷ KD评估关键词价值。通过搜索量、点击单价和优化难度计算，支持单个和批量计算，自动排序，助力SEO优化。",
  authors: [{ name: "CatchIdeas团队" }],
  creator: "CatchIdeas",
  publisher: "CatchIdeas",
  robots: "index, follow",
  keywords: [
    "CatchROI计算器",
    "优化难度回报率",
    "关键词价值计算",
    "SEO工具",
    "关键词优化",
    "关键词筛选",
    "搜索量分析",
    "CPC分析",
    "KD分析",
    "关键词排序"
  ],
  openGraph: {
    type: "website",
    locale: "zh_CN",
    url: "https://catchideas.com/catchroi-calculator",
    title: "CatchROI计算器 | CatchIdeas",
    description: "CatchROI优化难度回报率计算工具，使用公式CatchROI = Volume × CPC ÷ KD评估关键词价值。通过搜索量、点击单价和优化难度计算，支持单个和批量计算，自动排序，助力SEO优化。",
    siteName: "CatchIdeas",
  },
  twitter: {
    card: "summary_large_image",
    title: "CatchROI计算器 | CatchIdeas",
    description: "CatchROI优化难度回报率计算工具，使用公式CatchROI = Volume × CPC ÷ KD评估关键词价值。通过搜索量、点击单价和优化难度计算，支持单个和批量计算，自动排序，助力SEO优化。",
  },
  alternates: {
    canonical: "https://catchideas.com/catchroi-calculator",
  },
};

export default function CatchROICalculatorLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}
