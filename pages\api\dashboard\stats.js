export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({
      success: false,
      message: 'Method not allowed'
    })
  }

  try {
    const apiBaseUrl = process.env.NEXT_PUBLIC_PHP_API_URL || 'https://api.xstty.com/api'

    // 只获取基础统计数据，这个是确定能工作的
    const statsResponse = await fetch(`${apiBaseUrl}/keywords.php?page=1&per_page=1`)
    const statsData = await statsResponse.json()

    const statistics = statsData.success && statsData.data.statistics ? statsData.data.statistics : {
      total: 0,
      analyzed: 0,
      unanalyzed: 0,
      category_count: 0
    }

    // 获取最近7天的导入记录
    const importLogsResponse = await fetch(`${apiBaseUrl}/import-logs.php?action=recent&days=7`)
    const importLogsData = await importLogsResponse.json()

    let recentActivity = []

    if (importLogsData.success && importLogsData.data) {
      recentActivity = importLogsData.data.map(importLog => ({
        id: importLog.id,
        type: importLog.type,
        description: importLog.description,
        timestamp: importLog.created_at || importLog.import_date,
        count: importLog.filtered_keywords,
        filename: importLog.filename,
        import_date: importLog.import_date
      }))
    }

    const dashboardData = {
      statistics,
      recentActivity,
      quickStats: {
        todayImports: 0, // 暂时设为0，因为无法准确计算
        todayAnalyzes: 0, // 暂时设为0，因为无法准确计算
        weeklyGrowth: 0   // 暂时设为0，因为无法准确计算
      }
    }

    res.status(200).json({
      success: true,
      data: dashboardData
    })

  } catch (error) {
    console.error('Dashboard stats error:', error)

    // 返回默认数据作为fallback
    res.status(200).json({
      success: true,
      data: {
        statistics: {
          total: 0,
          analyzed: 0,
          unanalyzed: 0,
          category_count: 0
        },
        recentActivity: [],
        quickStats: {
          todayImports: 0,
          todayAnalyzes: 0,
          weeklyGrowth: 0
        }
      }
    })
  }
}
