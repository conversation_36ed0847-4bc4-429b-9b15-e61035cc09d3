/**
 * 硅基流动API余额查询接口
 * 查询所有配置的硅基流动API密钥余额
 */

export default async function handler(req, res) {
  // 设置CORS头
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  if (req.method === 'GET') {
    return handleGetBalances(req, res);
  }

  return res.status(405).json({
    success: false,
    error: '不支持的请求方法，请使用GET'
  });
}

/**
 * 处理余额查询
 */
async function handleGetBalances(req, res) {
  try {
    const balances = [];

    // 查询10个硅基流动API密钥
    for (let i = 1; i <= 10; i++) {
      const apiKey = process.env[`SILICONFLOW_API_KEY_${i}`];
      
      if (!apiKey) {
        balances.push({
          keyIndex: i,
          keyName: `SILICONFLOW_API_KEY_${i}`,
          balance: '',
          totalBalance: '',
          chargeBalance: '',
          status: 'error',
          error: '密钥未配置'
        });
        continue;
      }

      try {
        // 创建AbortController用于超时控制
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 10000); // 10秒超时

        // 调用硅基流动用户信息API
        const response = await fetch('https://api.siliconflow.cn/v1/user/info', {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${apiKey}`,
            'Content-Type': 'application/json'
          },
          signal: controller.signal
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();

        if (data.code === 20000 && data.status === true) {
          balances.push({
            keyIndex: i,
            keyName: `SILICONFLOW_API_KEY_${i}`,
            balance: data.data.balance || '0.00',
            totalBalance: data.data.totalBalance || '0.00',
            chargeBalance: data.data.chargeBalance || '0.00',
            status: 'success'
          });
        } else {
          balances.push({
            keyIndex: i,
            keyName: `SILICONFLOW_API_KEY_${i}`,
            balance: '',
            totalBalance: '',
            chargeBalance: '',
            status: 'error',
            error: data.message || '查询失败'
          });
        }
      } catch (err) {
        balances.push({
          keyIndex: i,
          keyName: `SILICONFLOW_API_KEY_${i}`,
          balance: '',
          totalBalance: '',
          chargeBalance: '',
          status: 'error',
          error: err.message || '网络错误'
        });
      }

      // 添加延迟避免请求过快
      await new Promise(resolve => setTimeout(resolve, 200));
    }

    return res.status(200).json({
      success: true,
      balances: balances,
      summary: {
        total: balances.length,
        success: balances.filter(b => b.status === 'success').length,
        error: balances.filter(b => b.status === 'error').length,
        totalBalance: balances
          .filter(b => b.status === 'success')
          .reduce((sum, b) => sum + parseFloat(b.balance || '0'), 0)
          .toFixed(2)
      }
    });

  } catch {
    return res.status(500).json({
      success: false,
      error: '服务器内部错误'
    });
  }
}
