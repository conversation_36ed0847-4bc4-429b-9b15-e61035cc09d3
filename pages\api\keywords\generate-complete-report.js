import { getCurrentDateString, getCurrentYear } from '../../../lib/date-utils.js'
import googleUsageTracker from '../../../lib/google-usage-tracker.js'
import {
  enhancedComprehensiveScoring,
  enhancedConfidenceCalculation,
  enhancedVolumePrediction,
  enhancedSEODifficultyAnalysis,
  analyzeCommercialValue,
  analyzeSearchIntent,
  analyzeReportCompleteness,
  generateCompetitiveAdvantageAnalysis
} from '../../../lib/algorithm-utils.js'

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      message: 'Method not allowed'
    })
  }

  try {
    const { keyword_id, keyword: directKeyword } = req.body

    if (!keyword_id) {
      return res.status(400).json({
        success: false,
        message: '关键词ID不能为空'
      })
    }

    const apiBaseUrl = process.env.NEXT_PUBLIC_PHP_API_URL || 'https://api.xstty.com/api'

    // 开始生成完整报告

    // 优先使用前端传递的关键词，避免重复数据库查询
    let keyword = directKeyword

    if (!keyword) {

      // 1. 获取关键词信息（仅在前端未传递时）
      const keywordResponse = await fetch(`${apiBaseUrl}/keywords.php?id=${encodeURIComponent(keyword_id)}`)
      const keywordData = await keywordResponse.json()

      if (!keywordData.success) {
        return res.status(400).json({
          success: false,
          error: '关键词不存在'
        })
      }

      keyword = keywordData.data.keyword
    }

    // 2. 获取相关词
    const suggestionsResponse = await fetch(`${apiBaseUrl}/keyword_suggestions.php?keyword_id=${encodeURIComponent(keyword_id)}`)
    const suggestionsData = await suggestionsResponse.json()
    const relatedKeywords = suggestionsData.success ? suggestionsData.data.suggestions : []
    // 3. 跳过相似度分析，直接使用相关关键词
    const similarityAnalysis = {
      related_keywords: relatedKeywords.map((keyword, index) => ({
        keyword: keyword.suggestion || keyword.keyword || keyword,
        similarity_score: 0.8 - (index * 0.05), // 模拟相似度分数
        rank: index + 1
      })),
      analysis: '基于相关关键词的相似度分析'
    }

    // 4. Google搜索分析 - 必须成功获取数据
    let googleSearchData
    try {
      googleSearchData = await analyzeGoogleSearch(keyword)
    } catch (error) {
      return res.status(500).json({
        success: false,
        error: `Google搜索分析失败: ${error.message}`
      })
    }

    // 5. 生成智能竞争优势分析（使用googleSearchData中的数据）
    const competitiveAdvantageAnalysis = generateCompetitiveAdvantageAnalysis(
      {
        top_competitors: googleSearchData.top_competitors,
        competitor_summary: googleSearchData.competitor_summary,
        content_gap_analysis: googleSearchData.content_gap_analysis
      },
      googleSearchData.search_intent_analysis
    )

    // 6. 一次性生成完整报告
    const completeReport = await generateCompleteReport(keyword, relatedKeywords, googleSearchData, similarityAnalysis, competitiveAdvantageAnalysis, googleSearchData.search_intent_analysis)

    // 5. 保存报告到数据库
    const reportData = {
      keyword_id: keyword_id,
      related_keywords: JSON.stringify(similarityAnalysis.related_keywords || relatedKeywords),
      similarity_analysis: similarityAnalysis.analysis || '相似度分析完成',
      search_volume_estimate: completeReport.search_volume_estimate,
      competition_intensity: completeReport.competition_intensity,
      market_opportunity: completeReport.market_opportunity,
      google_search_results: JSON.stringify(googleSearchData),
      search_metrics: JSON.stringify({
        volume: googleSearchData.enhanced_volume_prediction?.volume || 1000,
        difficulty: googleSearchData.enhanced_seo_analysis?.score || 50,
        totalResults: googleSearchData.search_info?.totalResults || '0',
        searchTime: googleSearchData.search_info?.searchTime || '0.5',
        estimatedVolume: googleSearchData.enhanced_volume_prediction?.volume || 1000,
        source: 'google_custom_search'
      }),
      top_competitors_analysis: JSON.stringify(googleSearchData.top_competitors || []),
      title_patterns_analysis: JSON.stringify(googleSearchData.title_patterns || {}),
      content_gap_analysis: JSON.stringify(googleSearchData.content_gap_analysis || {}),
      ai_analysis: JSON.stringify(completeReport),
      recommendation_score: completeReport.recommendation_score,
      action_suggestions: JSON.stringify(completeReport.action_suggestions),
      top_competitors: JSON.stringify(extractTopCompetitors(googleSearchData.top_competitors || [])),
      competitive_advantages: completeReport.competitive_advantages,
      content_strategy: completeReport.content_strategy,
      target_audience: completeReport.target_audience,
      monetization_potential: completeReport.monetization_potential,
      seo_difficulty: completeReport.seo_difficulty,
      trend_analysis: completeReport.trend_analysis,
      seasonal_patterns: completeReport.seasonal_patterns,
      analysis_model: `${process.env.SILICONFLOW_REPORT_MODEL || 'moonshotai/Kimi-K2-Instruct'}`,
      analysis_version: '3.0',
      confidence_score: Math.min(100, completeReport.confidence_score || 85) / 100 // 转换为0-1范围
    }

    // 保存报告到数据库
    const saveResponse = await fetch(`${apiBaseUrl}/keyword_reports.php`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(reportData)
    })

    const saveResult = await saveResponse.json()

    if (!saveResult.success) {
      res.write(`data: ${JSON.stringify({ error: '保存报告失败: ' + saveResult.message })}\n\n`)
      res.end()
      return
    }

    // 更新关键词分析状态
    const updateKeywordData = {
      keyword_id: keyword_id,
      user_intent: completeReport.user_intent,
      user_pain_point: completeReport.user_pain_point,
      competition_level: completeReport.competition_level,
      competition_score: completeReport.competition_score,
      competition_color: getCompetitionColor(completeReport.competition_score),
      competition_description: completeReport.competition_description,
      category: completeReport.category,
      analyzed_at: new Date().toISOString()
    }

    const updateResponse = await fetch(`${apiBaseUrl}/keywords.php`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        action: 'update',
        id: keyword_id,
        ...updateKeywordData
      })
    })

    const updateResult = await updateResponse.json()
    // 关键词更新完成

    // 返回成功结果
    return res.status(200).json({
      success: true,
      message: '报告生成成功',
      report: completeReport
    })

  } catch (error) {

    return res.status(500).json({
      success: false,
      error: error.message || '服务器内部错误',
      errorName: error.name,
      errorStack: error.stack
    })
  }
}

// Google搜索分析函数
async function analyzeGoogleSearch(keyword) {


  // 动态生成20个Google搜索API配置
  const googleSearchConfigs = [];
  for (let i = 1; i <= 20; i++) {
    const apiKey = process.env[`GOOGLE_SEARCH_API_KEY_${i}`];
    const searchEngineId = process.env[`GOOGLE_SEARCH_CX_${i}`];

    if (apiKey && searchEngineId) {
      googleSearchConfigs.push({
        apiKey,
        searchEngineId,
        index: i
      });
    }
  }


  if (googleSearchConfigs.length === 0) {
    throw new Error('没有可用的Google搜索API配置')
  }



  // 获取使用次数最少的密钥
  let selectedConfig, keyIndex;
  try {
    keyIndex = await googleUsageTracker.getAvailableKey(100);
    selectedConfig = googleSearchConfigs.find(config =>
      config.apiKey === process.env[`GOOGLE_SEARCH_API_KEY_${keyIndex}`]
    );
    if (!selectedConfig) {
      // 如果指定密钥不存在，使用随机配置
      selectedConfig = googleSearchConfigs[Math.floor(Math.random() * googleSearchConfigs.length)];
      keyIndex = selectedConfig.index;
    }
  } catch (error) {
    // 如果获取失败，使用随机配置
    selectedConfig = googleSearchConfigs[Math.floor(Math.random() * googleSearchConfigs.length)];
    keyIndex = selectedConfig.index;
  }

  try {
    // 调用Google Custom Search API
    const searchUrl = `https://www.googleapis.com/customsearch/v1?key=${selectedConfig.apiKey}&cx=${selectedConfig.searchEngineId}&q=${encodeURIComponent(keyword)}&num=10`

    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), 10000)

    const response = await fetch(searchUrl, {
      signal: controller.signal
    })

    clearTimeout(timeoutId)

    if (!response.ok) {
      const errorText = await response.text()
      // 如果是403错误且包含suspended信息，标记密钥为超限
      if (response.status === 403 && errorText.includes('suspended')) {
        try {
          // 给失效密钥添加100次使用记录，使其超限
          await googleUsageTracker.recordUsage(keyIndex, 100);
        } catch (recordError) {
          // 静默处理记录错误
        }
      }
      throw new Error('Google搜索API请求失败')
    }

    const data = await response.json()

    // 检查API错误
    if (data.error) {
      // 如果是密钥被暂停的错误，自动标记为超限
      if (data.error.message && data.error.message.includes('suspended')) {
        try {
          // 给失效密钥添加100次使用记录，使其超限
          await googleUsageTracker.recordUsage(keyIndex, 100);
        } catch (recordError) {
          // 静默处理记录错误
        }
      }
      throw new Error('Google搜索API返回错误')
    }

    // 记录API使用成功
    try {
      await googleUsageTracker.recordUsage(keyIndex);
    } catch (error) {
      // 静默处理记录错误
    }

      // 检查是否有搜索结果
      const items = data.items || []
      const searchInfo = data.searchInformation || {}

      // 注意：空搜索结果也是有效的API响应，不应该视为失败
      // 继续处理，即使items为空也要返回分析结果

      // Google搜索API成功获取数据


      // 详细分析竞争对手
      const competitorAnalysis = analyzeCompetitors(items)
      const titleAnalysis = analyzeTitlePatterns(items)
      const contentAnalysis = analyzeContentTypes(items)

      // 增强版流量预测算法 - 使用真正的算法函数
      const enhancedVolumeData = enhancedVolumePrediction({
        results_count: searchInfo.totalResults,
        search_time: searchInfo.searchTime,
        top_competitors: competitorAnalysis.competitors,
        title_patterns: titleAnalysis,
        keyword: keyword
      })

      // 商业价值评估算法 - 使用真正的算法函数
      const commercialValueData = analyzeCommercialValue(keyword, {
        results_count: searchInfo.totalResults,
        top_competitors: competitorAnalysis.competitors,
        title_patterns: titleAnalysis,
        content_analysis: contentAnalysis
      })

      // SEO难度分析算法 - 使用真正的算法函数
      const seoAnalysisData = enhancedSEODifficultyAnalysis({
        top_competitors: competitorAnalysis.competitors,
        results_count: searchInfo.totalResults,
        competitor_summary: competitorAnalysis.summary,
        title_patterns: titleAnalysis
      })

      // 搜索意图分析算法 - 使用真正的算法函数
      const searchIntentData = analyzeSearchIntent(titleAnalysis)

      // 内容空缺分析 - 基于实际搜索数据生成
      const contentGapData = generateContentGapAnalysis(competitorAnalysis, contentAnalysis, titleAnalysis, searchInfo)

      // 综合评分算法 - 使用真正的算法函数
      const comprehensiveScore = enhancedComprehensiveScoring({
        enhanced_volume_prediction: enhancedVolumeData,
        enhanced_seo_analysis: seoAnalysisData,
        commercial_value_analysis: commercialValueData,
        content_gap_analysis: contentGapData,
        top_competitors: competitorAnalysis.competitors,
        results_count: searchInfo.totalResults
      }, competitorAnalysis, {})

      // 置信度分析算法 - 使用真正的算法函数
      const confidenceAnalysis = enhancedConfidenceCalculation({
        top_competitors: competitorAnalysis.competitors,
        title_patterns: titleAnalysis,
        results_count: searchInfo.totalResults,
        enhanced_volume_prediction: enhancedVolumeData,
        enhanced_seo_analysis: seoAnalysisData,
        commercial_value_analysis: commercialValueData,
        content_gap_analysis: contentGapData,
        search_time: searchInfo.searchTime
      }, competitorAnalysis, {})

      // 智能竞争优势分析已在前面生成

      // 报告完整性分析
      const completenessAnalysis = analyzeReportCompleteness({
        volumePrediction: enhancedVolumeData,
        seoDifficulty: seoAnalysisData,
        searchIntent: searchIntentData,
        commercialValue: commercialValueData,
        contentGap: { gaps: contentGapData.gaps?.length || 0, opportunities: contentGapData.opportunities },
        comprehensiveScore: comprehensiveScore,
        confidenceAnalysis: confidenceAnalysis
      })

      // 成功获取数据，返回分析结果
      return {
        enhanced_volume_prediction: enhancedVolumeData,
        enhanced_seo_analysis: seoAnalysisData,
        commercial_value_analysis: commercialValueData,
        search_intent_analysis: searchIntentData,
        title_patterns: titleAnalysis,
        top_competitors: competitorAnalysis.competitors,
        competitor_summary: competitorAnalysis.summary,
        enhanced_comprehensive_score: comprehensiveScore,
        enhanced_confidence_analysis: confidenceAnalysis,
        content_gap_analysis: contentGapData,
        report_completeness_analysis: completenessAnalysis,
        search_results: items.slice(0, 10),
        // 添加搜索统计信息
        search_info: {
          totalResults: searchInfo.totalResults || '0',
          searchTime: searchInfo.searchTime || '0.5',
          formattedTotalResults: searchInfo.formattedTotalResults || '0',
          formattedSearchTime: searchInfo.formattedSearchTime || '0.50'
        }
      }

  } catch (error) {
    throw new Error('Google搜索分析失败')
  }
}



// 生成完整报告函数
async function generateCompleteReport(keyword, relatedKeywords, googleSearchData, similarityAnalysis, competitiveAdvantageAnalysis, searchIntentData) {
  // 构建完整的分析prompt
  const prompt = buildCompleteAnalysisPrompt(keyword, relatedKeywords, googleSearchData, competitiveAdvantageAnalysis, searchIntentData)
  
  // 调用AI模型生成完整报告 (动态生成20个密钥)
  const availableKeys = [];
  for (let i = 1; i <= 20; i++) {
    const apiKey = process.env[`SILICONFLOW_API_KEY_${i}`];
    if (apiKey) {
      availableKeys.push(apiKey);
    }
  }

  for (let i = 0; i < availableKeys.length; i++) {
    try {
      const response = await fetch(process.env.SILICONFLOW_API_URL || 'https://api.siliconflow.cn/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${availableKeys[i]}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          model: process.env.SILICONFLOW_REPORT_MODEL || 'moonshotai/Kimi-K2-Instruct',
          messages: [{ role: 'user', content: prompt }],
          temperature: 0.6,
          max_tokens: 6000,
          top_p: 0.7,
          frequency_penalty: 0.0
        })
      })

      if (!response.ok) {
        throw new Error('AI API请求失败')
      }

      const data = await response.json()
      const content = data.choices[0].message.content

      // 解析AI返回的JSON
      try {
        const result = JSON.parse(content)
        // 检查是否包含未处理的模板变量
        if (JSON.stringify(result).includes('${')) {
          throw new Error('AI返回包含模板变量')
        }
        return result
      } catch (parseError) {
        // 尝试提取JSON
        const jsonMatch = content.match(/\{[\s\S]*\}/)
        if (jsonMatch) {
          try {
            const result = JSON.parse(jsonMatch[0])
            // 检查是否包含未处理的模板变量
            if (JSON.stringify(result).includes('${')) {
              throw new Error('提取的JSON包含模板变量')
            }
            return result
          } catch (e) {
            throw e
          }
        }
      }

    } catch (error) {
      if (i === availableKeys.length - 1) {
        // 所有API Key都失败，返回降级数据
        return generateFallbackReport(keyword, googleSearchData)
      }
    }
  }

  // 如果所有尝试都失败，返回降级数据
  return generateFallbackReport(keyword, googleSearchData)
}

// 降级报告生成
function generateFallbackReport(keyword, googleSearchData) {
  // 如果有Google搜索数据，尝试使用算法计算
  let searchVolume = 1000
  let seoScore = 50
  let commercialScore = 6
  let comprehensiveScore = 6
  let confidenceScore = 85

  if (googleSearchData) {
    // 使用算法计算的结果
    searchVolume = googleSearchData.enhanced_volume_prediction?.volume || 1000
    seoScore = googleSearchData.enhanced_seo_analysis?.score || 50
    commercialScore = googleSearchData.commercial_value_analysis?.score || 6
    comprehensiveScore = googleSearchData.enhanced_comprehensive_score?.score || commercialScore
    confidenceScore = googleSearchData.enhanced_confidence_analysis?.overall || 85
  }

  return {
    user_intent: 'informational',
    user_pain_point: `用户搜索"${keyword}"时主要面临信息获取困难、解决方案不明确等问题，需要提供专业、准确的内容来满足用户需求。`,
    competition_level: seoScore <= 30 ? 'low' : seoScore <= 70 ? 'medium' : 'high',
    competition_score: seoScore,
    competition_description: `基于算法分析，该关键词的SEO难度为${seoScore}/100，竞争强度为${seoScore <= 30 ? '较低' : seoScore <= 70 ? '中等' : '较高'}。`,
    category: 'General',
    similarity_analysis: { analysis: '相似度分析完成' },
    search_volume_estimate: searchVolume,
    competition_intensity: seoScore <= 30 ? 'low' : seoScore <= 70 ? 'medium' : 'high',
    market_opportunity: `基于算法分析发现的市场机会：预测月搜索量${searchVolume}次，具有稳定流量潜力。SEO难度${seoScore}/100，${seoScore <= 30 ? '竞争较低，适合快速进入' : seoScore <= 70 ? '竞争中等，需要优质内容策略' : '竞争激烈，需要差异化定位'}。商业价值${commercialScore}/10，${commercialScore >= 7 ? '变现潜力较高' : commercialScore >= 5 ? '具有一定变现价值' : '主要以流量获取为目标'}。建议采用${seoScore <= 50 ? '内容营销' : '技术SEO'}为主的策略，预期${Math.round(searchVolume * 0.1)}-${Math.round(searchVolume * 0.3)}次月访问量。`,
    search_metrics: { volume: searchVolume, difficulty: seoScore },
    top_competitors_analysis: { competitors: [] },
    title_patterns_analysis: { patterns: [] },
    content_gap_analysis: { gaps: 3, opportunities: 'medium' },
    recommendation_score: comprehensiveScore,
    action_suggestions: [
      `针对${searchVolume.toLocaleString()}次月搜索量，创建${seoScore <= 50 ? '长尾关键词' : '权威性'}内容获取流量`,
      `利用${seoScore}/100的SEO难度，采用${seoScore <= 30 ? '快速排名' : seoScore <= 70 ? '中期优化' : '长期建设'}策略`,
      `基于${commercialScore}/10商业价值，重点布局${commercialScore >= 7 ? '转化型' : commercialScore >= 5 ? '引流型' : '品牌型'}内容`,
      `预期获得${Math.round(searchVolume * 0.1).toLocaleString()}-${Math.round(searchVolume * 0.3).toLocaleString()}次月访问量，ROI评估为${commercialScore >= 6 ? '高' : commercialScore >= 4 ? '中' : '低'}收益`
    ],
    top_competitors: extractTopCompetitors(googleSearchData?.top_competitors || []),
    competitive_advantages: `基于算法分析的竞争优势构建：1) 利用${seoScore <= 50 ? '低竞争环境' : '技术优化'}获得排名优势；2) 针对${searchVolume}次月搜索需求提供差异化内容；3) 通过${commercialScore >= 6 ? '商业化布局' : '流量积累'}建立市场地位。`,
    content_strategy: `数据驱动的内容策略：1) 基于${searchVolume}次搜索量制定${searchVolume >= 2000 ? '高频发布' : searchVolume >= 500 ? '定期更新' : '精品路线'}计划；2) 针对${seoScore}/100难度采用${seoScore <= 30 ? '快速覆盖' : seoScore <= 70 ? '深度优化' : '权威建设'}方式；3) 结合${commercialScore}/10商业价值设计${commercialScore >= 6 ? '转化导向' : '价值导向'}内容。`,
    target_audience: `精准受众画像：基于${searchVolume}次月搜索行为分析，目标用户具有${commercialValue >= 6 ? '明确购买意图' : commercialValue >= 4 ? '信息收集需求' : '基础了解需求'}，建议采用${seoScore <= 50 ? '教育引导' : '专业权威'}的内容定位策略。`,
    monetization_potential: `变现潜力评估：${commercialScore}/10商业价值对应${commercialScore >= 7 ? '高变现潜力，建议直接商业化' : commercialScore >= 5 ? '中等变现潜力，可考虑联盟营销' : '低变现潜力，以流量积累为主'}。预期月收益：${Math.round(searchVolume * commercialScore * 0.01)}-${Math.round(searchVolume * commercialScore * 0.05)}元。`,
    seo_difficulty: seoScore <= 30 ? 'easy' : seoScore <= 70 ? 'medium' : 'hard',
    trend_analysis: `${getCurrentYear()}年市场趋势分析：基于${searchVolume}次月搜索量和${seoScore}/100竞争难度，该关键词处于${seoScore <= 30 ? '蓝海市场，增长潜力大' : seoScore <= 70 ? '成长期市场，竞争适中' : '成熟市场，需要差异化'}阶段。建议${searchVolume >= 1000 ? '加大投入抢占市场' : '稳步布局等待机会'}。`,
    seasonal_patterns: `季节性模式预测：根据${commercialScore}/10商业价值特征，该关键词${commercialScore >= 6 ? '可能存在购物季波动，建议Q4重点投入' : commercialScore >= 4 ? '搜索相对稳定，可均匀分布内容' : '主要受信息需求驱动，关注热点事件影响'}。优化时机：${seoScore <= 50 ? '全年适宜' : '避开竞争高峰期'}。`,
    confidence_score: confidenceScore
  }
}

// 构建完整分析prompt - 增强版，充分利用数据
function buildCompleteAnalysisPrompt(keyword, relatedKeywords, googleSearchData, competitiveAdvantageAnalysis, searchIntentData = {}) {
  const currentDate = getCurrentDateString()
  const currentYear = getCurrentYear()

  // 提取算法分析数据
  const volumePrediction = googleSearchData.enhanced_volume_prediction || {}
  const seoDifficulty = googleSearchData.enhanced_seo_analysis || {}
  const commercialValue = googleSearchData.commercial_value_analysis || {}
  const contentGap = googleSearchData.content_gap_analysis || {}
  const comprehensiveScore = googleSearchData.enhanced_comprehensive_score || {}
  const confidenceAnalysis = googleSearchData.enhanced_confidence_analysis || {}

  // 如果没有传入searchIntentData，在这里进行分析
  if (!searchIntentData.type && googleSearchData.title_patterns) {
    searchIntentData = analyzeSearchIntent(googleSearchData.title_patterns)
  }

  // 提取竞争对手数据
  const competitors = googleSearchData.top_competitors || []
  const topCompetitors = competitors.slice(0, 10)

  // 分析竞争对手类型分布
  const competitorTypes = {
    root_domains: topCompetitors.filter(c => c.isRootDomain).length,
    subdirectories: topCompetitors.filter(c => !c.isRootDomain).length,
    commercial: topCompetitors.filter(c => c.contentType === 'commercial').length,
    informational: topCompetitors.filter(c => c.contentType === 'informational').length
  }

  // 提取标题模式
  const titlePatterns = googleSearchData.title_patterns_analysis || {}
  const commonWords = titlePatterns.common_words || []

  // 构建丰富的上下文信息
  const contextInfo = `
=== 核心算法分析数据 ===
• 搜索量预测: ${volumePrediction.volume || 1000}次/月 (置信度: ${volumePrediction.confidence || 'medium'})
• SEO难度评分: ${seoDifficulty.score || 50}/100 (${seoDifficulty.level || 'medium'})
• 商业价值评分: ${commercialValue.score || 6}/10 (成熟度: ${commercialValue.maturity || 'medium'})
• 综合推荐评分: ${comprehensiveScore.score || 6}/10
• 分析置信度: ${confidenceAnalysis.overall || 85}%

=== 市场竞争环境 ===
• Google搜索结果总数: ${googleSearchData.search_info?.totalResults?.toLocaleString() || '未知'}条
• 前10名竞争对手分布:
  - 根域名: ${competitorTypes.root_domains}个 (${((competitorTypes.root_domains/topCompetitors.length)*100).toFixed(1)}%)
  - 子目录页面: ${competitorTypes.subdirectories}个 (${((competitorTypes.subdirectories/topCompetitors.length)*100).toFixed(1)}%)
  - 商业内容: ${competitorTypes.commercial}个 (${((competitorTypes.commercial/topCompetitors.length)*100).toFixed(1)}%)
  - 信息内容: ${competitorTypes.informational}个 (${((competitorTypes.informational/topCompetitors.length)*100).toFixed(1)}%)

=== 内容空缺机会 ===
• 发现内容空缺: ${contentGap.gaps?.length || 0}个机会点
• 机会等级: ${contentGap.opportunities || 'medium'}
• 主要空缺领域: ${contentGap.gaps?.slice(0, 3).join(', ') || '待分析'}

=== 相关关键词生态 ===
• 相关关键词总数: ${relatedKeywords.length}个
• 高频标题词汇: ${commonWords.slice(0, 5).join(', ') || '待分析'}
• 用户搜索模式: ${titlePatterns.pattern_analysis || '多样化搜索需求'}

=== 顶级竞争对手详情 ===
${topCompetitors.slice(0, 5).map((comp, i) =>
  `${i+1}. ${comp.domain || 'unknown'} - ${comp.contentType || 'unknown'} (${comp.isRootDomain ? '根域名' : '子页面'})`
).join('\n')}
`

  return `作为顶级SEO市场分析专家，请基于以下真实数据对关键词"${keyword}"进行深度市场机会分析。

当前时间：${currentDate}（${currentYear}年）

${contextInfo}

请充分发挥AI分析能力，基于以上丰富的数据进行深度洞察分析，避免模板化回答。重点关注：

🎯 **市场机会分析要求**：
1. 基于竞争对手类型分布分析市场进入机会
2. 结合AI分析的内容空缺识别具体的内容机会
3. 利用搜索量和商业价值数据评估ROI潜力
4. 分析用户搜索模式发现未满足的需求
5. 提供具体可执行的市场策略建议

请基于以上数据进行深度分析，以JSON格式返回：

{
  "user_intent": "基于搜索意图算法分析，关键词主要表现为${searchIntentData.type || 'informational'}意图，置信度${searchIntentData.confidence || 75}%。用户主要寻求${searchIntentData.type === 'transactional' ? '购买相关信息和交易机会' : searchIntentData.type === 'commercial' ? '产品比较和购买决策支持' : searchIntentData.type === 'navigational' ? '特定网站或品牌信息' : '相关知识和信息内容'}",
  "user_pain_point": "通过分析${topCompetitors.length}个竞争对手内容覆盖和${contentGap.gaps?.length || 0}个内容空缺，深度挖掘用户未被满足的具体痛点。请以自然段落形式描述，不使用数字编号（200字以内）",
  "competition_level": "low/medium/high",
  "competition_score": ${seoDifficulty.score || 50},
  "competition_description": "基于${googleSearchData.search_info?.totalResults?.toLocaleString() || '未知'}个搜索结果、${competitorTypes.root_domains}个根域名竞争对手的详细竞争环境分析。请以自然段落形式描述，不使用数字编号（150字以内）",
  "category": "从Business/Technology/Health/Finance/Education/Entertainment等选择最合适的分类",
  "similarity_analysis": {"analysis": "基于相关词生态的相似度分析"},
  "search_volume_estimate": ${volumePrediction.volume || 1000},
  "competition_intensity": "${seoDifficulty.level || 'medium'}",
  "market_opportunity": "基于${competitorTypes.commercial}个商业竞争对手、${contentGap.gaps?.length || 0}个内容空缺、${volumePrediction.volume || 1000}次月搜索量的深度市场机会分析。包括具体的市场进入策略、内容差异化机会、ROI预期评估、竞争优势构建方案。要求使用纯文本格式，避免使用Markdown符号如**、*、#等，内容300字以内，要有具体数据支撑和可执行建议",
  "search_metrics": {"volume": ${volumePrediction.volume || 1000}, "difficulty": ${seoDifficulty.score || 50}},
  "top_competitors_analysis": {"competitors": []},
  "title_patterns_analysis": {"patterns": []},
  "content_gap_analysis": {
    "gaps": [
      "基于${topCompetitors.length}个竞争对手分析，识别出的具体内容空缺1",
      "基于搜索意图和用户需求，发现的内容空缺2",
      "基于竞争对手内容类型分布，找到的机会空缺3"
    ],
    "recommendations": [
      "针对发现空缺的具体内容创作建议1",
      "基于竞争分析的差异化内容策略2",
      "结合用户搜索意图的内容优化建议3"
    ],
    "opportunities": "基于竞争环境评估的机会等级(high/medium/low)",
    "analysis": "综合内容空缺分析总结，包括市场机会评估和具体执行建议"
  },
  "similarity_analysis": {
    "related_keywords": [
      {"keyword": "最相关的关键词1", "similarity_score": 0.95, "rank": 1},
      {"keyword": "相关关键词2", "similarity_score": 0.89, "rank": 2},
      {"keyword": "相关关键词3", "similarity_score": 0.83, "rank": 3}
    ],
    "analysis": "基于${relatedKeywords.length}个相关关键词的相似度分析说明，包括关键词关联性和搜索意图相似性评估"
  },
  "recommendation_score": ${comprehensiveScore.score || 6},
  "action_suggestions": [
    "基于${competitorTypes.root_domains}个根域名竞争对手分析，提供4个具体可执行的SEO策略建议",
    "针对发现的${contentGap.gaps?.length || 0}个内容空缺，提供具体的内容创作方向和主题建议",
    "利用${volumePrediction.volume || 1000}次月搜索量，制定流量获取和转化的具体策略",
    "基于${commercialValue.score || 6}/10商业价值评分，规划变现路径和收益优化方案"
  ],
  "top_competitors": [],
  "competitive_advantages": "${competitiveAdvantageAnalysis.rootDomainAnalysis} 推荐网站类型：${competitiveAdvantageAnalysis.websiteTypeRecommendation.primary}（${competitiveAdvantageAnalysis.websiteTypeRecommendation.reason}）。核心优势：${competitiveAdvantageAnalysis.strategicAdvantages.join('、')}。",
  "content_strategy": "基于${competitorTypes.informational}个信息型内容 vs ${competitorTypes.commercial}个商业型内容的差异化内容策略（200字以内）",
  "target_audience": "基于用户搜索模式和竞争对手内容类型的精准受众画像分析（150字以内）",
  "monetization_potential": "基于${commercialValue.score || 6}/10商业价值评分和市场竞争环境的详细变现潜力评估（150字以内）",
  "seo_difficulty": "${seoDifficulty.level || 'medium'}",
  "trend_analysis": "基于${currentYear}年市场环境和${googleSearchData.search_info?.totalResults?.toLocaleString() || '未知'}个搜索结果的趋势分析，包括市场成熟度、增长潜力和发展方向（150字以内）",
  "seasonal_patterns": "基于搜索行为模式和竞争对手内容发布规律的季节性分析，识别最佳内容发布时机（150字以内）",
  "confidence_score": ${confidenceAnalysis.overall || 85}
}

🎯 **关键分析要求**：
1. **数据驱动分析**：必须深度利用所有提供的真实数据，每个分析都要有具体数据支撑
2. **避免模板化**：禁止使用通用模板回答，要针对具体数据进行个性化分析
3. **市场机会重点**：market_opportunity字段是核心，必须提供具体可执行的市场策略
4. **竞争洞察**：充分利用竞争对手类型分布数据，提供差异化竞争策略
5. **内容空缺分析**：基于${topCompetitors.length}个竞争对手的内容类型、标题模式、搜索意图，深度分析市场内容空缺，在content_gap_analysis中提供3个具体的内容空缺和对应的创作建议
6. **相似度分析**：基于${relatedKeywords.length}个相关关键词，在similarity_analysis中按相似度排序并评分，提供关键词关联性分析
7. **ROI导向**：所有建议都要考虑${volumePrediction.volume || 1000}次搜索量的流量价值
8. **行动建议具体化**：action_suggestions数组必须包含4个具体可执行的建议，每个建议要包含具体数据和可操作的步骤，不要使用模板变量
9. **格式要求**：所有文本字段使用纯文本格式，严禁使用Markdown符号（**、*、#、-等）和数字编号（1. 2. 3.等）
10. **自然段落**：所有分析内容请使用自然段落形式表达，避免列表和编号格式
11. **完整JSON结构**：必须包含content_gap_analysis和similarity_analysis的完整结构，不能省略任何字段
12. **直接返回JSON**：不要任何解释文字，直接返回完整JSON格式`
}

// 提取主要竞争对手根域名
function extractTopCompetitors(topCompetitorsData) {
  if (!Array.isArray(topCompetitorsData)) {
    return []
  }

  // 提取前10个竞争对手的根域名信息
  return topCompetitorsData.slice(0, 10).map((competitor, index) => {
    // 提取根域名
    let domain = competitor.domain || competitor.displayLink || ''

    // 如果有URL，从URL中提取域名
    if (!domain && competitor.url) {
      try {
        const url = new URL(competitor.url)
        domain = url.hostname
      } catch (e) {
        domain = competitor.url.replace(/^https?:\/\//, '').split('/')[0]
      }
    }

    // 移除www前缀
    domain = domain.replace(/^www\./, '')

    return {
      rank: index + 1,
      domain: domain,
      isRootDomain: competitor.isRootDomain || false
    }
  }).filter(comp => comp.domain) // 过滤掉没有域名的条目
}

// 竞争颜色映射
function getCompetitionColor(score) {
  if (score <= 30) return 'green'
  if (score <= 70) return 'orange'
  return 'red'
}

// 详细分析竞争对手
function analyzeCompetitors(items) {
  const competitors = items.slice(0, 10).map((item, index) => {
    const url = new URL(item.link)

    // 正确的域名和路径分析
    const hostname = url.hostname
    const pathname = url.pathname

    // 移除www前缀进行域名分析
    const cleanHostname = hostname.replace(/^www\./, '')
    const hostParts = cleanHostname.split('.')

    // 判断域名类型
    let domainType = 'root_domain'
    let domainInfo = '根域名'
    let isRootDomain = true
    let isSubdirectory = false

    if (hostParts.length > 2) {
      domainType = 'subdomain'
      domainInfo = '二级域名'
      isRootDomain = false
    }

    // 分析路径结构 - 全面的URL类型识别
    const pathSegments = pathname.split('/').filter(segment => segment.length > 0)
    const queryParams = url.search
    const fragment = url.hash
    let pathInfo = ''
    let urlType = 'root_page'
    let urlFeatures = []

    // 检查特殊URL特征
    if (queryParams) urlFeatures.push('带参数')
    if (fragment) urlFeatures.push('带锚点')

    if (pathSegments.length === 0) {
      // 根域名
      pathInfo = '根域名'
      urlType = 'root_page'
      isSubdirectory = false
    } else {
      // 检查是否是API路径
      if (pathSegments[0] === 'api') {
        pathInfo = 'API接口'
        urlType = 'api_endpoint'
        isSubdirectory = true
      }
      // 检查是否是国际化路径
      else if (/^(en|zh|ja|ko|fr|de|es|it|pt|ru)$/i.test(pathSegments[0])) {
        pathInfo = `国际化路径 (${pathSegments[0].toUpperCase()})`
        urlType = 'i18n_path'
        isSubdirectory = true
      }
      // 单级路径分析
      else if (pathSegments.length === 1) {
        const lastSegment = pathSegments[0]
        const hasFileExtension = /\.(html|htm|php|asp|aspx|jsp|pdf|doc|docx|xml|json)$/i.test(lastSegment)

        if (hasFileExtension) {
          pathInfo = '内页'
          urlType = 'inner_page'
          isSubdirectory = false
        } else {
          pathInfo = '1级目录'
          urlType = 'first_level_directory'
          isSubdirectory = true
        }
      }
      // 多级路径分析
      else {
        const lastSegment = pathSegments[pathSegments.length - 1]
        const hasFileExtension = /\.(html|htm|php|asp|aspx|jsp|pdf|doc|docx|xml|json)$/i.test(lastSegment)

        // 检查是否是API的深层路径
        if (pathSegments[0] === 'api') {
          pathInfo = `API接口 (${pathSegments.length}级)`
          urlType = 'deep_api_endpoint'
        }
        // 检查是否是国际化的深层路径
        else if (/^(en|zh|ja|ko|fr|de|es|it|pt|ru)$/i.test(pathSegments[0])) {
          if (hasFileExtension) {
            pathInfo = `国际化内页 (${pathSegments[0].toUpperCase()})`
            urlType = 'i18n_inner_page'
          } else {
            pathInfo = `国际化目录 (${pathSegments[0].toUpperCase()}, ${pathSegments.length}级)`
            urlType = 'i18n_directory'
          }
        }
        // 普通多级路径
        else if (hasFileExtension) {
          pathInfo = `${pathSegments.length - 1}级目录的内页`
          urlType = 'deep_inner_page'
        } else {
          pathInfo = `${pathSegments.length}级目录`
          urlType = 'deep_directory'
        }
        isSubdirectory = true
      }
    }

    // 添加URL特征到描述中
    if (urlFeatures.length > 0) {
      pathInfo += ` (${urlFeatures.join(', ')})`
    }

    // 最终显示的结构信息
    let structureDisplay = domainInfo
    if (pathInfo) {
      structureDisplay += ` + ${pathInfo}`
    }

    return {
      rank: index + 1,
      domain: hostname, // 保持原始域名显示
      title: item.title,
      url: item.link, // 完整URL
      snippet: item.snippet,

      // URL结构分析
      domainType,
      domainInfo,
      pathInfo,
      structureDisplay,
      urlType, // 更准确的URL类型
      urlFeatures, // 新增：URL特征（参数、锚点等）
      pathSegments: pathSegments,
      pathDepth: pathSegments.length,
      isRootDomain,
      isSubdirectory,
      hasQueryParams: !!queryParams,
      hasFragment: !!fragment,

      // 标题分析
      titleLength: item.title.length,
      hasNumbers: /\d/.test(item.title),
      hasDate: /20\d{2}|january|february|march|april|may|june|july|august|september|october|november|december/i.test(item.title),
      hasBrand: item.displayLink ? item.displayLink.split('.')[0].toLowerCase().includes(item.title.toLowerCase().split(' ')[0]) : false,

      // 内容类型推断
      contentType: inferContentType(item.title, item.snippet, item.link)
    }
  })

  // 竞争对手汇总分析
  const summary = {
    totalCompetitors: competitors.length,
    rootDomainCount: competitors.filter(c => c.isRootDomain).length,
    subdirectoryCount: competitors.filter(c => c.isSubdirectory).length,
    averageTitleLength: Math.round(competitors.reduce((sum, c) => sum + c.titleLength, 0) / competitors.length),
    dateOptimizedCount: competitors.filter(c => c.hasDate).length,
    brandedCount: competitors.filter(c => c.hasBrand).length,
    contentTypeDistribution: getContentTypeDistribution(competitors)
  }

  return { competitors, summary }
}

// 增强的标题模式分析
function analyzeTitlePatterns(items) {
  const titles = items.map(item => item.title)

  if (titles.length === 0) {
    return {
      commonWords: [],
      averageLength: 0,
      hasDatePattern: 0,
      hasNumberPattern: 0,
      hasQuestionPattern: 0,
      averageWordCount: 0
    }
  }

  // 提取所有单词（3个字符以上）
  const allWords = titles.join(' ').toLowerCase().match(/\b\w{3,}\b/g) || []
  const wordCount = {}

  allWords.forEach(word => {
    // 过滤常见停用词
    if (!['the', 'and', 'for', 'are', 'but', 'not', 'you', 'all', 'can', 'had', 'her', 'was', 'one', 'our', 'out', 'day', 'get', 'has', 'him', 'his', 'how', 'its', 'may', 'new', 'now', 'old', 'see', 'two', 'who', 'boy', 'did', 'she', 'use', 'way', 'what', 'with'].includes(word)) {
      wordCount[word] = (wordCount[word] || 0) + 1
    }
  })

  return {
    averageLength: Math.round(titles.reduce((sum, title) => sum + title.length, 0) / titles.length),
    commonWords: Object.entries(wordCount)
      .filter(([word, count]) => count > 1)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10)
      .map(([word, count]) => ({ word, count })),
    hasDatePattern: titles.filter(title => /20\d{2}/.test(title)).length,
    hasNumberPattern: titles.filter(title => /\d/.test(title)).length,
    hasQuestionPattern: titles.filter(title => /\?/.test(title)).length,
    averageWordCount: Math.round(titles.reduce((sum, title) => sum + title.split(' ').length, 0) / titles.length),
    searchIntent: { type: 'informational', confidence: 75 }
  }
}

// 分析内容类型
function analyzeContentTypes(items) {
  const contentTypes = items.map(item => inferContentType(item.title, item.snippet, item.link))
  const distribution = getContentTypeDistribution(items.map((item, index) => ({ contentType: contentTypes[index] })))

  return {
    distribution,
    dominantType: Object.entries(distribution).sort(([,a], [,b]) => b - a)[0]?.[0] || 'informational',
    diversity: Object.keys(distribution).length
  }
}

// 推断内容类型
function inferContentType(title, snippet, url = '') {
  const text = `${title} ${snippet} ${url}`.toLowerCase()

  // 优先级顺序：从最具体到最一般
  if (text.includes('buy') || text.includes('purchase') || text.includes('price') || text.includes('cost')) {
    return 'commercial'
  } else if (text.includes('news') || text.includes('report') || text.includes('update')) {
    return 'news'
  } else if (text.includes('guide') || text.includes('how to') || text.includes('tutorial')) {
    return 'guide'
  } else if (text.includes('review') || text.includes('comparison') || text.includes('vs')) {
    return 'review'
  } else if (text.includes('list') || text.includes('best') || text.includes('top')) {
    return 'list'
  } else if (text.includes('blog') || text.includes('article')) {
    return 'blog'
  } else if (text.includes('forum') || text.includes('discussion') || text.includes('reddit')) {
    return 'discussion'
  } else {
    return 'informational'
  }
}

// 获取内容类型分布
function getContentTypeDistribution(competitors) {
  const distribution = {}
  competitors.forEach(comp => {
    distribution[comp.contentType] = (distribution[comp.contentType] || 0) + 1
  })
  return distribution
}

// 基于实际数据生成内容空缺分析
function generateContentGapAnalysis(competitorAnalysis, contentAnalysis, titleAnalysis, searchInfo) {
  const competitors = competitorAnalysis.competitors || []
  const contentTypes = contentAnalysis.distribution || {}
  const titlePatterns = titleAnalysis.commonWords || []
  const totalResults = parseInt(searchInfo.totalResults) || 0

  // 分析内容类型空缺
  const gaps = []
  const recommendations = []

  // 基于内容类型分布分析空缺
  const typeCount = Object.keys(contentTypes).length
  if (!contentTypes.guide && !contentTypes.tutorial) {
    gaps.push('缺少教程指导类内容')
    recommendations.push('创建详细的操作指南和教程内容')
  }

  if (!contentTypes.review && !contentTypes.comparison) {
    gaps.push('缺少评测对比类内容')
    recommendations.push('制作产品评测和对比分析文章')
  }

  if (!contentTypes.list && titlePatterns.filter(w => w.word.includes('best') || w.word.includes('top')).length === 0) {
    gaps.push('缺少榜单推荐类内容')
    recommendations.push('创建"最佳"、"推荐"类榜单内容')
  }

  // 基于竞争对手分析空缺
  const rootDomainRatio = competitors.filter(c => c.isRootDomain).length / competitors.length
  if (rootDomainRatio > 0.7) {
    gaps.push('权威域名占主导，缺少细分专业内容')
    recommendations.push('专注细分领域深度内容，建立专业权威性')
  }

  // 基于搜索结果数量评估机会
  let opportunities = 'medium'
  if (totalResults < 1000000) {
    opportunities = 'high'
    gaps.push('市场竞争相对较少')
    recommendations.push('抓住低竞争机会，快速建立市场地位')
  } else if (totalResults > 100000000) {
    opportunities = 'low'
    gaps.push('市场高度饱和，需要差异化定位')
    recommendations.push('寻找长尾关键词和细分市场机会')
  }

  // 确保至少有3个空缺和建议
  while (gaps.length < 3) {
    gaps.push(`基于${competitors.length}个竞争对手分析发现的内容机会`)
  }
  while (recommendations.length < 3) {
    recommendations.push('基于竞争分析的内容策略建议')
  }

  return {
    gaps: gaps.slice(0, 3),
    recommendations: recommendations.slice(0, 3),
    opportunities,
    analysis: `基于${competitors.length}个竞争对手、${typeCount}种内容类型、${totalResults.toLocaleString()}个搜索结果的综合分析`
  }
}