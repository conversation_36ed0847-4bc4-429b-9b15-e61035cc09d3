'use client'

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { signIn, useSession } from "next-auth/react";
import { Globe, AlertCircle, Loader2 } from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect } from "react";

export default function SignIn() {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const { data: session, status } = useSession()
  const router = useRouter()

  // 如果已经登录，重定向到控制台
  useEffect(() => {
    if (session) {
      router.push('/console')
    }
  }, [session, router])

  const handleGoogleSignIn = async () => {
    try {
      setIsLoading(true)
      setError(null)

      const result = await signIn('google', {
        callbackUrl: '/console',
        redirect: false
      })

      if (result?.error) {
        setError('登录失败，请重试')
      }
    } catch {
      setError('登录过程中出现错误')
    } finally {
      setIsLoading(false)
    }
  }

  // 如果正在检查session状态，显示加载
  if (status === 'loading') {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-2" />
          <p className="text-muted-foreground">检查登录状态...</p>
        </div>
      </div>
    )
  }

  // 如果已经登录，显示重定向信息
  if (session) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-2" />
          <p className="text-muted-foreground">正在跳转到控制台...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-background">
      <Card className="w-full max-w-md data-card-enhanced">
        <CardHeader className="text-center">
          <CardTitle className="text-2xl">登录到 CatchIdeas</CardTitle>
          <CardDescription>
            使用Google账号登录访问控制台
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {error && (
            <Alert className="border-destructive/50 text-destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <Button
            onClick={handleGoogleSignIn}
            disabled={isLoading}
            className="w-full btn-primary-enhanced"
            size="lg"
          >
            {isLoading ? (
              <>
                <Loader2 className="h-5 w-5 mr-2 animate-spin" />
                登录中...
              </>
            ) : (
              <>
                <Globe className="h-5 w-5 mr-2" />
                使用 Google 登录
              </>
            )}
          </Button>
        </CardContent>
      </Card>
    </div>
  );
}
