/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,

  // 输出模式 - 用于服务器部署
  output: 'standalone',

  // 实验性功能
  experimental: {
    // 启用服务器组件
    serverComponentsExternalPackages: ['cheerio']
  },

  // 启用SWC压缩
  swcMinify: true,

  // Webpack 配置
  webpack: (config) => {
    config.resolve.fallback = {
      ...config.resolve.fallback,
      fs: false,
      net: false,
      tls: false,
    };
    return config;
  },

  // 页面扩展名
  pageExtensions: ['js', 'jsx', 'ts', 'tsx'],
}

module.exports = nextConfig
