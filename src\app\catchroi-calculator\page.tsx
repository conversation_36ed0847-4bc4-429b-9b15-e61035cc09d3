'use client'

import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card'
import { Badge } from '../../components/ui/badge'
import { Button } from '../../components/ui/button'
import { Input } from '../../components/ui/input'
import { Label } from '../../components/ui/label'
import { Textarea } from '../../components/ui/textarea'
import { Calculator, TrendingUp, Target, AlertCircle, CheckCircle, XCircle, Download, Plus, Trash2, FileText } from 'lucide-react'
import FrontendHeader from '../../components/frontend-header'
import FrontendFooter from '../../components/frontend-footer'

interface CatchROIItem {
  keyword: string
  volume: number
  kd: number
  cpc: number
  catchroi: number
  level: 'excellent' | 'good' | 'average' | 'poor'
  description: string
}

export default function CatchROICalculatorPage() {
  // 单位换算函数
  const parseNumberWithUnit = (input: string): { value: number; explanation: string; hasUnit: boolean } => {
    if (!input.trim()) return { value: 0, explanation: '', hasUnit: false }

    const cleanInput = input.trim().toLowerCase()
    const numberMatch = cleanInput.match(/^(\d*\.?\d+)([kmb]?)$/)

    if (!numberMatch) {
      const plainNumber = parseFloat(cleanInput.replace(/,/g, ''))
      if (!isNaN(plainNumber)) {
        return {
          value: plainNumber,
          explanation: '',
          hasUnit: false
        }
      }
      return { value: 0, explanation: '格式错误', hasUnit: false }
    }

    const [, numberStr, unit] = numberMatch
    const baseNumber = parseFloat(numberStr)

    if (isNaN(baseNumber)) return { value: 0, explanation: '数字格式错误', hasUnit: false }

    let multiplier = 1
    let unitName = ''
    let explanation = ''

    switch (unit) {
      case 'k':
        multiplier = 1000
        unitName = '千'
        explanation = `${numberStr}K = ${(baseNumber * multiplier).toLocaleString()}`
        break
      case 'm':
        multiplier = 1000000
        unitName = '百万'
        explanation = `${numberStr}M = ${(baseNumber * multiplier).toLocaleString()}`
        break
      case 'b':
        multiplier = 1000000000
        unitName = '十亿'
        explanation = `${numberStr}B = ${(baseNumber * multiplier).toLocaleString()}`
        break
      default:
        explanation = ''
    }

    return {
      value: baseNumber * multiplier,
      explanation,
      hasUnit: unit !== ''
    }
  }

  // 单个计算状态
  const [keyword, setKeyword] = useState<string>('')
  const [volume, setVolume] = useState<string>('')
  const [kd, setKd] = useState<string>('')
  const [cpc, setCpc] = useState<string>('')
  const [catchroiResult, setCatchroiResult] = useState<CatchROIItem | null>(null)

  // 解析后的数值状态
  const [volumeParsed, setVolumeParsed] = useState<{ value: number; explanation: string; hasUnit: boolean }>({ value: 0, explanation: '', hasUnit: false })
  const [kdParsed, setKdParsed] = useState<{ value: number; explanation: string; hasUnit: boolean }>({ value: 0, explanation: '', hasUnit: false })
  const [cpcParsed, setCpcParsed] = useState<{ value: number; explanation: string; hasUnit: boolean }>({ value: 0, explanation: '', hasUnit: false })

  // 批量计算状态
  const [batchMode, setBatchMode] = useState<boolean>(false)
  const [batchInput, setBatchInput] = useState<string>('')
  const [batchResults, setBatchResults] = useState<CatchROIItem[]>([])

  // 实时解析输入
  const handleVolumeChange = (value: string) => {
    setVolume(value)
    setVolumeParsed(parseNumberWithUnit(value))
  }

  const handleKdChange = (value: string) => {
    setKd(value)
    setKdParsed(parseNumberWithUnit(value))
  }

  const handleCpcChange = (value: string) => {
    setCpc(value)
    setCpcParsed(parseNumberWithUnit(value))
  }

  // 计算CatchROI等级
  const getCatchROILevel = (catchroi: number): { level: CatchROIItem['level']; description: string } => {
    if (catchroi >= 2000) {
      return { level: 'excellent', description: '极优：非常值得优化的关键词' }
    } else if (catchroi >= 1000) {
      return { level: 'good', description: '良好：值得考虑的关键词' }
    } else if (catchroi >= 500) {
      return { level: 'average', description: '一般：可以考虑的关键词' }
    } else {
      return { level: 'poor', description: '较差：不建议优化的关键词' }
    }
  }

  // 计算单个CatchROI
  const calculateCatchROI = () => {
    const volumeValue = volumeParsed.value
    const kdValue = kdParsed.value
    const cpcValue = cpcParsed.value

    if (volumeValue <= 0 || kdValue <= 0 || cpcValue <= 0) {
      alert('请输入有效的数值（所有值必须大于0）')
      return
    }

    if (kdValue > 100) {
      alert('KD值通常在0-100之间，请检查输入')
      return
    }

    const catchroi = (volumeValue * cpcValue) / kdValue
    const { level, description } = getCatchROILevel(catchroi)

    const result: CatchROIItem = {
      keyword: keyword.trim() || '未命名关键词',
      volume: volumeValue,
      kd: kdValue,
      cpc: cpcValue,
      catchroi: catchroi,
      level: level,
      description: description
    }

    setCatchroiResult(result)
  }

  // 批量计算CatchROI
  const calculateBatchCatchROI = () => {
    if (!batchInput.trim()) {
      alert('请输入批量数据')
      return
    }

    const lines = batchInput.trim().split('\n')
    const results: CatchROIItem[] = []

    lines.forEach((line, index) => {
      const parts = line.split(',').map(part => part.trim())
      
      if (parts.length !== 4) {
        console.warn(`第${index + 1}行格式错误，跳过: ${line}`)
        return
      }

      const [keywordStr, volumeStr, kdStr, cpcStr] = parts
      const volumeValue = parseNumberWithUnit(volumeStr).value
      const kdValue = parseNumberWithUnit(kdStr).value
      const cpcValue = parseNumberWithUnit(cpcStr).value

      if (volumeValue <= 0 || kdValue <= 0 || cpcValue <= 0) {
        console.warn(`第${index + 1}行数据无效，跳过: ${line}`)
        return
      }

      const catchroi = (volumeValue * cpcValue) / kdValue
      const { level, description } = getCatchROILevel(catchroi)

      results.push({
        keyword: keywordStr || `关键词${index + 1}`,
        volume: volumeValue,
        kd: kdValue,
        cpc: cpcValue,
        catchroi: catchroi,
        level: level,
        description: description
      })
    })

    if (results.length === 0) {
      alert('没有有效的数据行，请检查输入格式')
      return
    }

    // 按CatchROI从高到低排序
    results.sort((a, b) => b.catchroi - a.catchroi)
    setBatchResults(results)
  }

  // 导出CSV
  const exportToCSV = () => {
    const dataToExport = batchMode ? batchResults : (catchroiResult ? [catchroiResult] : [])
    
    if (dataToExport.length === 0) {
      alert('没有数据可导出')
      return
    }

    const csvData = [
      ['关键词', '搜索量', 'KD难度', 'CPC单价', 'CatchROI', '等级', '说明'],
      ...dataToExport.map(item => [
        item.keyword,
        item.volume.toString(),
        item.kd.toString(),
        item.cpc.toString(),
        item.catchroi.toFixed(2),
        item.level,
        item.description
      ]),
      [''],
      ['计算公式: CatchROI = Volume × CPC ÷ KD'],
      ['计算时间: ' + new Date().toLocaleString()],
      ['数据来源: CatchIdeas CatchROI计算器']
    ]

    const csvContent = csvData.map(row => row.map(cell => `"${cell}"`).join(',')).join('\n')
    const blob = new Blob(['\uFEFF' + csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', `CatchROI计算结果_${new Date().toISOString().split('T')[0]}.csv`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  // 切换模式
  const toggleMode = (isBatch: boolean) => {
    setBatchMode(isBatch)
    if (!isBatch) {
      setBatchInput('')
      setBatchResults([])
    } else {
      setCatchroiResult(null)
    }
  }

  // 重置计算器
  const resetCalculator = () => {
    setKeyword('')
    setVolume('')
    setKd('')
    setCpc('')
    setVolumeParsed({ value: 0, explanation: '', hasUnit: false })
    setKdParsed({ value: 0, explanation: '', hasUnit: false })
    setCpcParsed({ value: 0, explanation: '', hasUnit: false })
    setCatchroiResult(null)
    setBatchInput('')
    setBatchResults([])
  }

  return (
    <div className="min-h-screen bg-background">
      <FrontendHeader />
      
      {/* 页面标题 */}
      <section className="py-8 px-4 bg-gradient-to-r from-blue-50 to-purple-50 border-b">
        <div className="container mx-auto text-center">
          <div className="flex items-center justify-center mb-4">
            <div className="p-3 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl mr-4">
              <Target className="h-8 w-8 text-white" />
            </div>
            <h1 className="text-4xl font-bold text-gray-900">CatchROI计算器</h1>
          </div>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            优化难度回报率计算工具，通过Volume×CPC÷KD公式评估关键词价值，找到最值得优化的关键词
          </p>
          <div className="mt-4 p-4 bg-white/80 rounded-lg border border-blue-200 max-w-2xl mx-auto">
            <p className="text-lg font-semibold text-blue-800 mb-2">CatchROI公式</p>
            <p className="text-xl font-bold text-purple-700">CatchROI = Volume × CPC ÷ KD</p>
            <p className="text-sm text-gray-600 mt-2">Volume: 搜索量 | CPC: 点击单价 | KD: 优化难度</p>
          </div>
        </div>
      </section>

      {/* 模式切换 */}
      <section className="py-6 px-4 bg-background border-b">
        <div className="container mx-auto">
          <div className="max-w-4xl mx-auto">
            <div className="flex justify-center gap-2">
              <Button
                variant={!batchMode ? "default" : "outline"}
                onClick={() => toggleMode(false)}
                className={!batchMode
                  ? "bg-gradient-to-r from-blue-500 to-purple-600 text-white hover:from-blue-600 hover:to-purple-700"
                  : "bg-white/80 backdrop-blur-sm border-gray-200 text-gray-700 hover:bg-blue-50 hover:text-blue-600 hover:border-blue-300 transition-all duration-200"
                }
              >
                <Calculator className="h-4 w-4 mr-2" />
                单个计算
              </Button>
              <Button
                variant={batchMode ? "default" : "outline"}
                onClick={() => toggleMode(true)}
                className={batchMode
                  ? "bg-gradient-to-r from-green-500 to-teal-600 text-white hover:from-green-600 hover:to-teal-700"
                  : "bg-white/80 backdrop-blur-sm border-gray-200 text-gray-700 hover:bg-green-50 hover:text-green-600 hover:border-green-300 transition-all duration-200"
                }
              >
                <FileText className="h-4 w-4 mr-2" />
                批量计算
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* 主要内容区域 */}
      <section className="py-8 px-4">
        <div className="container mx-auto">
          <div className="max-w-6xl mx-auto">
            {!batchMode ? (
              /* 单个计算模式 */
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {/* 输入区域 */}
                <div className="space-y-6">
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center">
                        <Target className="h-5 w-5 mr-2 text-blue-600" />
                        关键词信息
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div>
                        <Label htmlFor="keyword" className="text-sm font-semibold text-gray-700 mb-2 block">
                          目标关键词
                        </Label>
                        <Input
                          id="keyword"
                          type="text"
                          placeholder="例如：ai writing tool"
                          value={keyword}
                          onChange={(e) => setKeyword(e.target.value)}
                          className="h-12 text-lg border-2 border-purple-200 focus:border-purple-500"
                        />
                      </div>

                      <div>
                        <Label htmlFor="volume" className="text-sm font-semibold text-gray-700 mb-2 block">
                          搜索量 (Volume)
                        </Label>
                        <Input
                          id="volume"
                          type="text"
                          placeholder="例如：2400 或 2.4K"
                          value={volume}
                          onChange={(e) => handleVolumeChange(e.target.value)}
                          className="h-12 text-lg border-2 border-purple-200 focus:border-purple-500"
                        />
                        {volumeParsed.hasUnit && volumeParsed.explanation && (
                          <div className="mt-2 p-2 bg-blue-50 rounded-md">
                            <p className="text-sm text-blue-700">
                              <span className="font-semibold">换算结果：</span>
                              {volumeParsed.explanation}
                            </p>
                          </div>
                        )}
                      </div>

                      <div>
                        <Label htmlFor="kd" className="text-sm font-semibold text-gray-700 mb-2 block">
                          优化难度 (KD)
                        </Label>
                        <Input
                          id="kd"
                          type="text"
                          placeholder="例如：12 (0-100之间)"
                          value={kd}
                          onChange={(e) => handleKdChange(e.target.value)}
                          className="h-12 text-lg border-2 border-purple-200 focus:border-purple-500"
                        />
                        {kdParsed.hasUnit && kdParsed.explanation && (
                          <div className="mt-2 p-2 bg-blue-50 rounded-md">
                            <p className="text-sm text-blue-700">
                              <span className="font-semibold">换算结果：</span>
                              {kdParsed.explanation}
                            </p>
                          </div>
                        )}
                      </div>

                      <div>
                        <Label htmlFor="cpc" className="text-sm font-semibold text-gray-700 mb-2 block">
                          点击单价 (CPC)
                        </Label>
                        <Input
                          id="cpc"
                          type="text"
                          placeholder="例如：8.5"
                          value={cpc}
                          onChange={(e) => handleCpcChange(e.target.value)}
                          className="h-12 text-lg border-2 border-purple-200 focus:border-purple-500"
                        />
                        {cpcParsed.hasUnit && cpcParsed.explanation && (
                          <div className="mt-2 p-2 bg-blue-50 rounded-md">
                            <p className="text-sm text-blue-700">
                              <span className="font-semibold">换算结果：</span>
                              {cpcParsed.explanation}
                            </p>
                          </div>
                        )}
                      </div>

                      {/* 操作按钮 */}
                      <div className="flex gap-3 pt-4">
                        <Button
                          onClick={calculateCatchROI}
                          className="flex-1 h-12 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white font-semibold"
                        >
                          <Calculator className="h-4 w-4 mr-2" />
                          计算 CatchROI
                        </Button>
                        <Button
                          onClick={resetCalculator}
                          variant="outline"
                          className="h-12 border-2 border-gray-300 hover:border-gray-500"
                        >
                          重置
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {/* 结果显示区域 */}
                <div className="space-y-6">
                  {catchroiResult ? (
                    <>
                      {/* CatchROI结果卡片 */}
                      <Card className="border-2 border-blue-200">
                        <CardHeader>
                          <CardTitle className="flex items-center justify-between">
                            <span className="text-blue-700">CatchROI 计算结果</span>
                            <Badge className={
                              catchroiResult.level === 'excellent' ? 'bg-green-500' :
                              catchroiResult.level === 'good' ? 'bg-blue-500' :
                              catchroiResult.level === 'average' ? 'bg-yellow-500' :
                              'bg-red-500'
                            }>
                              {catchroiResult.level === 'excellent' ? '极优' :
                               catchroiResult.level === 'good' ? '良好' :
                               catchroiResult.level === 'average' ? '一般' : '较差'}
                            </Badge>
                          </CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="text-center mb-4">
                            <div className="text-4xl font-bold text-purple-600 mb-2">
                              {catchroiResult.catchroi.toFixed(2)}
                            </div>
                            <p className="text-gray-600">{catchroiResult.description}</p>
                          </div>

                          <div className="grid grid-cols-2 gap-4 text-sm">
                            <div className="bg-gray-50 p-3 rounded-lg">
                              <div className="font-semibold text-gray-700">关键词</div>
                              <div className="text-gray-900">{catchroiResult.keyword}</div>
                            </div>
                            <div className="bg-gray-50 p-3 rounded-lg">
                              <div className="font-semibold text-gray-700">搜索量</div>
                              <div className="text-gray-900">{catchroiResult.volume.toLocaleString()}</div>
                            </div>
                            <div className="bg-gray-50 p-3 rounded-lg">
                              <div className="font-semibold text-gray-700">优化难度</div>
                              <div className="text-gray-900">{catchroiResult.kd}</div>
                            </div>
                            <div className="bg-gray-50 p-3 rounded-lg">
                              <div className="font-semibold text-gray-700">点击单价</div>
                              <div className="text-gray-900">${catchroiResult.cpc}</div>
                            </div>
                          </div>

                          <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                            <p className="text-sm text-blue-800">
                              <span className="font-semibold">计算过程：</span>
                              {catchroiResult.volume.toLocaleString()} × ${catchroiResult.cpc} ÷ {catchroiResult.kd} = {catchroiResult.catchroi.toFixed(2)}
                            </p>
                          </div>
                        </CardContent>
                      </Card>

                      {/* 导出按钮 */}
                      <Card>
                        <CardContent className="pt-6">
                          <Button
                            onClick={exportToCSV}
                            className="w-full h-12 bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white font-semibold"
                          >
                            <Download className="h-4 w-4 mr-2" />
                            导出结果为 CSV
                          </Button>
                        </CardContent>
                      </Card>
                    </>
                  ) : (
                    <Card>
                      <CardContent className="text-center py-12">
                        <Target className="h-16 w-16 mx-auto text-gray-400 mb-4" />
                        <h3 className="text-xl font-semibold text-gray-600 mb-2">等待计算</h3>
                        <p className="text-gray-500">
                          请填写关键词信息并点击"计算 CatchROI"按钮
                        </p>
                      </CardContent>
                    </Card>
                  )}
                </div>
              </div>
            ) : (
              /* 批量计算模式 */
              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <FileText className="h-5 w-5 mr-2 text-green-600" />
                      批量CatchROI计算
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div>
                        <Label htmlFor="batchInput" className="text-sm font-semibold text-green-800 mb-2 block">
                          输入格式：关键词,搜索量,优化难度,点击单价（每行一个）
                        </Label>
                        <Textarea
                          id="batchInput"
                          placeholder={`ai writing tool,2.4K,12,8.5\npassword generator,3.2K,8,6.2\nqr code generator,5.1K,15,4.8\ncolor picker tool,1.8K,6,7.3\nimage compressor,4.5K,18,5.9\nurl shortener,2.9K,10,6.8\nmarkdown editor,1.5K,9,9.2\nbase64 encoder,1.2K,5,11.4`}
                          value={batchInput}
                          onChange={(e) => setBatchInput(e.target.value)}
                          className="min-h-[200px] text-sm border-2 border-green-200 focus:border-green-500"
                        />
                      </div>

                      <div className="bg-green-50 p-4 rounded-lg">
                        <h4 className="font-semibold text-green-800 mb-2">输入说明</h4>
                        <ul className="text-sm text-green-700 space-y-1">
                          <li>• 每行一个关键词，用逗号分隔四个字段</li>
                          <li>• 支持单位简写：K(千)、M(百万)、B(十亿)</li>
                          <li>• 示例：ai writing tool,2.4K,12,8.5</li>
                          <li>• 结果将按CatchROI从高到低自动排序</li>
                        </ul>
                      </div>

                      <div className="flex gap-3">
                        <Button
                          onClick={calculateBatchCatchROI}
                          className="flex-1 h-12 bg-gradient-to-r from-green-500 to-teal-600 hover:from-green-600 hover:to-teal-700 text-white font-semibold"
                        >
                          <Calculator className="h-4 w-4 mr-2" />
                          批量计算 CatchROI
                        </Button>
                        <Button
                          onClick={exportToCSV}
                          disabled={batchResults.length === 0}
                          className="h-12 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white font-semibold disabled:opacity-50"
                        >
                          <Download className="h-4 w-4 mr-2" />
                          导出 CSV
                        </Button>
                        <Button
                          onClick={resetCalculator}
                          variant="outline"
                          className="h-12 border-2 border-gray-300 hover:border-gray-500"
                        >
                          重置
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* 批量计算结果 */}
                {batchResults.length > 0 && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center justify-between">
                        <span>批量计算结果</span>
                        <Badge className="bg-green-100 text-green-700">
                          {batchResults.length} 个关键词
                        </Badge>
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="overflow-x-auto">
                        <table className="w-full text-sm">
                          <thead>
                            <tr className="border-b border-gray-200">
                              <th className="text-left py-3 px-2 font-semibold text-gray-700">排名</th>
                              <th className="text-left py-3 px-2 font-semibold text-gray-700">关键词</th>
                              <th className="text-right py-3 px-2 font-semibold text-gray-700">搜索量</th>
                              <th className="text-right py-3 px-2 font-semibold text-gray-700">KD</th>
                              <th className="text-right py-3 px-2 font-semibold text-gray-700">CPC</th>
                              <th className="text-right py-3 px-2 font-semibold text-gray-700">CatchROI</th>
                              <th className="text-center py-3 px-2 font-semibold text-gray-700">等级</th>
                            </tr>
                          </thead>
                          <tbody>
                            {batchResults.map((item, index) => (
                              <tr key={index} className="border-b border-gray-100 hover:bg-gray-50">
                                <td className="py-3 px-2">
                                  <Badge variant="outline" className="text-xs">
                                    #{index + 1}
                                  </Badge>
                                </td>
                                <td className="py-3 px-2 font-medium text-gray-900">
                                  {item.keyword}
                                </td>
                                <td className="py-3 px-2 text-right text-gray-700">
                                  {item.volume.toLocaleString()}
                                </td>
                                <td className="py-3 px-2 text-right text-gray-700">
                                  {item.kd}
                                </td>
                                <td className="py-3 px-2 text-right text-gray-700">
                                  ${item.cpc}
                                </td>
                                <td className="py-3 px-2 text-right font-bold text-purple-600">
                                  {item.catchroi.toFixed(2)}
                                </td>
                                <td className="py-3 px-2 text-center">
                                  <Badge className={
                                    item.level === 'excellent' ? 'bg-green-500' :
                                    item.level === 'good' ? 'bg-blue-500' :
                                    item.level === 'average' ? 'bg-yellow-500' :
                                    'bg-red-500'
                                  }>
                                    {item.level === 'excellent' ? '极优' :
                                     item.level === 'good' ? '良好' :
                                     item.level === 'average' ? '一般' : '较差'}
                                  </Badge>
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>

                      <div className="mt-4 p-4 bg-blue-50 rounded-lg">
                        <h4 className="font-semibold text-blue-800 mb-2">结果说明</h4>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                          <div>
                            <span className="font-semibold text-green-600">极优 (≥2000):</span>
                            <span className="text-gray-700"> 非常值得优化</span>
                          </div>
                          <div>
                            <span className="font-semibold text-blue-600">良好 (≥1000):</span>
                            <span className="text-gray-700"> 值得考虑</span>
                          </div>
                          <div>
                            <span className="font-semibold text-yellow-600">一般 (≥500):</span>
                            <span className="text-gray-700"> 可以考虑</span>
                          </div>
                          <div>
                            <span className="font-semibold text-red-600">较差 (&lt;500):</span>
                            <span className="text-gray-700"> 不建议优化</span>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )}
              </div>
            )}
          </div>
        </div>
      </section>

      <FrontendFooter />
    </div>
  )
}
