# PHP API 配置
NEXT_PUBLIC_PHP_API_URL=

# SiliconFlow API配置
SILICONFLOW_API_URL=https://api.siliconflow.cn/v1/chat/completions
SILICONFLOW_IMAGE_API_URL=https://api.siliconflow.cn/v1/images/generations
SILICONFLOW_API_KEY_1=
SILICONFLOW_API_KEY_2=
SILICONFLOW_API_KEY_3=
SILICONFLOW_API_KEY_4=
SILICONFLOW_API_KEY_5=
SILICONFLOW_API_KEY_6=
SILICONFLOW_API_KEY_7=
SILICONFLOW_API_KEY_8=
SILICONFLOW_API_KEY_9=
SILICONFLOW_API_KEY_10=

# SiliconFlow AI模型配置

# 方案A（高质量快速模型）- 2025年7月最新优化
SILICONFLOW_SIMILARITY_MODEL=Qwen/Qwen3-Embedding-8B          # 相似度分析：关键词语义相似度计算和聚类分析
SILICONFLOW_COMPETITION_MODEL=moonshotai/Kimi-K2-Instruct         # 竞争分析：SERP竞争对手分析和难度评估
SILICONFLOW_REPORT_MODEL=moonshotai/Kimi-K2-Instruct              # 报告生成：完整关键词分析报告和策略建议
SILICONFLOW_DOMAIN_MODEL=Qwen/Qwen2.5-32B-Instruct           # 域名推荐：SEO友好域名生成和品牌建议
SILICONFLOW_LOGO_TEXT_MODEL=Qwen/Qwen2.5-14B-Instruct        # Logo文案：Logo设计提示词生成和创意描述
SILICONFLOW_LOGO_IMAGE_MODEL=Kwai-Kolors/Kolors              # Logo图像：AI Logo图像生成和视觉设计

# 关键词过滤专用模型（使用最高质量模型）
SILICONFLOW_FILTER_MODEL=moonshotai/Kimi-K2-Instruct              # 智能过滤：CSV导入时过滤低质量和违规关键词

# 方案B（免费模型 - 备用配置）
# SILICONFLOW_SIMILARITY_MODEL=BAAI/bge-m3                     # 免费嵌入模型：基础语义相似度分析
# SILICONFLOW_COMPETITION_MODEL=deepseek-ai/DeepSeek-R1-0528-Qwen3-8B  # 免费推理模型：基础竞争分析
# SILICONFLOW_REPORT_MODEL=deepseek-ai/DeepSeek-R1-0528-Qwen3-8B       # 免费推理模型：基础报告生成
# SILICONFLOW_DOMAIN_MODEL=Qwen/Qwen2.5-7B-Instruct          # 免费指令模型：基础域名推荐
# SILICONFLOW_LOGO_TEXT_MODEL=Qwen/Qwen2.5-7B-Instruct       # 免费指令模型：基础Logo文案
# SILICONFLOW_LOGO_IMAGE_MODEL=Kwai-Kolors/Kolors            # 图像生成模型：Logo图像设计
# SILICONFLOW_FILTER_MODEL=Qwen/Qwen2.5-7B-Instruct          # 免费过滤模型：基础关键词过滤

# 智谱AI配置
# 用于关键词快速分析和批量分析的高速模型
ZHIPU_AI_API_KEY=                                             # 智谱AI密钥：用于快速关键词分析
ZHIPU_AI_BASE_URL=https://open.bigmodel.cn/api/paas/v4       # 智谱AI接口地址
ZHIPU_AI_MODEL=glm-4-flash-250414                            # 高速模型：批量关键词快速分析和分类

# Google API配置
GOOGLE_SUGGESTIONS_API_1=https://suggestqueries.google.com/complete/search
GOOGLE_SUGGESTIONS_API_2=https://clients1.google.com/complete/search

# NextAuth.js Google OAuth配置
GOOGLE_CLIENT_ID=
GOOGLE_CLIENT_SECRET=
NEXTAUTH_URL=
NEXTAUTH_SECRET=

# Google Custom Search API 1
GOOGLE_SEARCH_API_KEY_1=
GOOGLE_SEARCH_CX_1=

# Google Custom Search API 2
GOOGLE_SEARCH_API_KEY_2=
GOOGLE_SEARCH_CX_2=

# Google Custom Search API 3
GOOGLE_SEARCH_API_KEY_3=
GOOGLE_SEARCH_CX_3=

# Google Custom Search API 4
GOOGLE_SEARCH_API_KEY_4=
GOOGLE_SEARCH_CX_4=

# Google Custom Search API 5
GOOGLE_SEARCH_API_KEY_5=
GOOGLE_SEARCH_CX_5=

# Google Custom Search API 6
GOOGLE_SEARCH_API_KEY_6=
GOOGLE_SEARCH_CX_6=

# Google Custom Search API 7
GOOGLE_SEARCH_API_KEY_7=
GOOGLE_SEARCH_CX_7=

# Google Custom Search API 8
GOOGLE_SEARCH_API_KEY_8=
GOOGLE_SEARCH_CX_8=

# Google Custom Search API 9
GOOGLE_SEARCH_API_KEY_9=
GOOGLE_SEARCH_CX_9=

# Google Custom Search API 10
GOOGLE_SEARCH_API_KEY_10=
GOOGLE_SEARCH_CX_10=

# Google Custom Search API 11
GOOGLE_SEARCH_API_KEY_11=
GOOGLE_SEARCH_CX_11=

# Google Custom Search API 12
GOOGLE_SEARCH_API_KEY_12=
GOOGLE_SEARCH_CX_12=

# Google Custom Search API 13
GOOGLE_SEARCH_API_KEY_13=
GOOGLE_SEARCH_CX_13=

# Google Custom Search API 14
GOOGLE_SEARCH_API_KEY_14=
GOOGLE_SEARCH_CX_14=

# Google Custom Search API 15
GOOGLE_SEARCH_API_KEY_15=
GOOGLE_SEARCH_CX_15=

# Google Custom Search API 16
GOOGLE_SEARCH_API_KEY_16=
GOOGLE_SEARCH_CX_16=

# Google Custom Search API 17
GOOGLE_SEARCH_API_KEY_17=
GOOGLE_SEARCH_CX_17=

# Google Custom Search API 18
GOOGLE_SEARCH_API_KEY_18=
GOOGLE_SEARCH_CX_18=

# Google Custom Search API 19
GOOGLE_SEARCH_API_KEY_19=
GOOGLE_SEARCH_CX_19=

# Google Custom Search API 20
GOOGLE_SEARCH_API_KEY_20=
GOOGLE_SEARCH_CX_20=

