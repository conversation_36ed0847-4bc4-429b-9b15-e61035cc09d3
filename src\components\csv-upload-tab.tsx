'use client'

import { useState, useRef } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card'
import { Button } from './ui/button'

import { Progress } from './ui/progress'
import { Alert, AlertDescription } from './ui/alert'
import { Badge } from './ui/badge'
import {
  Upload,
  FileText,
  CheckCircle,
  XCircle,
  Loader2,
  Database,
  Filter
} from 'lucide-react'

interface ImportResult {
  success: boolean
  data?: {
    parse: {
      totalLines: number
      totalKeywords: number
    }
    import: {
      filename: string
      totalLines: number
      rawKeywords: number
      importedAt: string
    }
    aiFilter: {
      total: number
      accepted: number
      rejected: number
      passRate: string
      rejectionReasons: {
        brands: number
        genericWords: number
        personNames: number
        nonEnglish: number
        meaningless: number
        tooCompetitive: number
        noSeoValue: number
        serviceError: number
        other: number
      }
      rejectedKeywords: {
        brands: string[]
        genericWords: string[]
        personNames: string[]
        nonEnglish: string[]
        meaningless: string[]
        tooCompetitive: string[]
        noSeoValue: string[]
        serviceError: string[]
        other: string[]
      }
    }
    api: {
      totalImported: number
      duplicates: number
      apiErrors: number
      batchResults?: Array<{
        batchIndex: number
        startIndex: number
        endIndex: number
        success: boolean
        imported: number
        skipped: number
        duplicates: Array<{
          keyword: string
          reason: string
        }>
        errors: Array<{
          error: string
          keywords?: string[]
        }>
      }>
      failedBatches?: Array<{
        batchIndex: number
        startIndex: number
        endIndex: number
        keywords: string[]
        error: string
        timestamp: string
      }>
    }
  }
  message?: string
  error?: string
}

export default function CSVUploadTab() {
  const [importResult, setImportResult] = useState<ImportResult | null>(null)
  const [isProcessing, setIsProcessing] = useState(false)
  const [dragActive, setDragActive] = useState(false)
  const [processingStep, setProcessingStep] = useState('')
  const [processingProgress, setProcessingProgress] = useState(0)
  const fileInputRef = useRef<HTMLInputElement>(null)

  // AI处理进度模拟函数
  const startAIProcessingProgress = () => {
    const steps = [
      { progress: 45, message: '正在分析关键词结构...', delay: 800 },
      { progress: 55, message: '正在识别品牌词和通用词...', delay: 1200 },
      { progress: 65, message: '正在检查语言和格式...', delay: 1000 },
      { progress: 75, message: '正在评估SEO价值...', delay: 1500 },
      { progress: 85, message: '正在完成最终筛选...', delay: 800 }
    ]

    let stepIndex = 0

    const interval = setInterval(() => {
      if (stepIndex < steps.length) {
        const step = steps[stepIndex]
        setProcessingProgress(step.progress)
        setProcessingStep(step.message)
        stepIndex++
      }
    }, 1000)

    return interval
  }

  const handleFileUpload = async (file: File) => {
    if (!file) return

    // 验证文件类型
    if (!file.name.toLowerCase().endsWith('.csv')) {
      setImportResult({
        success: false,
        error: '请选择CSV格式文件'
      })
      return
    }

    setIsProcessing(true)
    setImportResult(null)
    setProcessingProgress(0)
    setProcessingStep('开始处理文件...')

    try {
      // 步骤1: 准备上传
      setProcessingStep('正在准备文件...')
      setProcessingProgress(10)

      const formData = new FormData()
      formData.append('csvFile', file)

      // 步骤2: 开始上传
      setProcessingStep('正在上传文件...')
      setProcessingProgress(20)

      // 步骤3: 解析CSV
      setProcessingStep('正在解析CSV文件...')
      setProcessingProgress(30)

      // 模拟解析进度
      await new Promise(resolve => setTimeout(resolve, 500))
      setProcessingStep('正在智能过滤关键词...')
      setProcessingProgress(40)

      // 启动AI处理进度模拟
      const progressInterval = startAIProcessingProgress()

      const response = await fetch('/api/trends/import', {
        method: 'POST',
        body: formData
      })

      // 清除进度模拟
      clearInterval(progressInterval)

      // 检查响应状态
      if (!response.ok) {
        // 尝试读取错误信息
        let errorMessage = '服务器错误'
        try {
          const contentType = response.headers.get('content-type')
          if (contentType && contentType.includes('application/json')) {
            const errorData = await response.json()
            errorMessage = errorData.error || errorData.message || '服务器错误'
          } else {
            // 如果是HTML错误页面，提供友好的错误信息
            errorMessage = '服务暂时不可用，请稍后重试'
          }
        } catch (e) {
          // 解析错误响应失败，使用默认错误信息
        }
        throw new Error(errorMessage)
      }

      // 检查响应类型
      const contentType = response.headers.get('content-type')
      if (!contentType || !contentType.includes('application/json')) {
        throw new Error('服务器返回格式错误')
      }

      const result = await response.json()

      // 验证响应格式
      if (!result || typeof result !== 'object') {
        throw new Error('响应格式错误')
      }

      // 最终步骤: 完成
      setProcessingStep('正在保存结果...')
      setProcessingProgress(90)

      // 短暂延迟显示保存过程
      await new Promise(resolve => setTimeout(resolve, 500))

      setProcessingStep(result.success ? '导入完成！' : '处理失败')
      setProcessingProgress(100)

      if (!result.success) {
        throw new Error(result.error || '处理失败')
      }

      setImportResult(result)

    } catch (error) {
      setProcessingStep('处理失败')
      setImportResult({
        success: false,
        error: (error as Error).message || '上传失败，请稍后重试'
      })
    } finally {
      setTimeout(() => {
        setIsProcessing(false)
        setProcessingStep('')
        setProcessingProgress(0)
      }, 1000)
    }
  }

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      handleFileUpload(file)
    }
  }

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true)
    } else if (e.type === 'dragleave') {
      setDragActive(false)
    }
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFileUpload(e.dataTransfer.files[0])
    }
  }

  const triggerFileInput = () => {
    fileInputRef.current?.click()
  }

  return (
    <div className="space-y-8">
      {/* 上传区域 - 统一卡片设计 */}
      <Card className="idea-card-enhanced">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Upload className="h-6 w-6 text-primary" />
            <span>智能文件上传</span>
          </CardTitle>
          <CardDescription>
            支持CSV格式文件，智能解析和处理您的关键词数据
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div
            className={`border-2 border-dashed rounded-xl p-12 text-center transition-all duration-300 cursor-pointer bg-card ${
              dragActive
                ? 'border-primary bg-primary/10 scale-105 shadow-lg'
                : 'border-border/50 hover:border-primary/50 hover:bg-muted/20 hover:shadow-md'
            } ${isProcessing ? 'pointer-events-none opacity-50' : ''}`}
            onDragEnter={handleDrag}
            onDragLeave={handleDrag}
            onDragOver={handleDrag}
            onDrop={handleDrop}
            onClick={triggerFileInput}
          >
            <div className={`transition-all duration-300 ${dragActive ? 'scale-110' : ''}`}>
              <div className={`w-20 h-20 mx-auto mb-6 rounded-full flex items-center justify-center transition-all duration-300 ${
                dragActive ? 'bg-primary/20 shadow-lg' : 'bg-muted shadow-sm'
              }`}>
                <FileText className={`h-10 w-10 ${
                  dragActive ? 'text-primary' : 'text-muted-foreground'
                }`} />
              </div>
              <div className="space-y-3">
                <h3 className="text-xl font-bold text-foreground">
                  {dragActive ? '释放文件开始处理' : '拖拽CSV文件到此处'}
                </h3>
                <p className="text-muted-foreground">
                  或点击下方按钮选择文件上传
                </p>
                <div className="flex items-center justify-center space-x-4 text-sm text-muted-foreground">
                  <span>• 支持CSV格式</span>
                  <span>• 最大10MB</span>
                  <span>• 自动去重</span>
                </div>
              </div>
            </div>
            <input
              ref={fileInputRef}
              type="file"
              accept=".csv"
              onChange={handleInputChange}
              className="hidden"
              disabled={isProcessing}
            />
            <Button
              className="mt-6 btn-primary-enhanced px-8 py-3"
              disabled={isProcessing}
              onClick={(e) => {
                e.stopPropagation()
                triggerFileInput()
              }}
              size="lg"
            >
              {isProcessing ? (
                <>
                  <Loader2 className="h-5 w-5 mr-2 animate-spin" />
                  处理中...
                </>
              ) : (
                <>
                  <Upload className="h-5 w-5 mr-2" />
                  选择文件
                </>
              )}
            </Button>
          </div>

          {/* 处理进度 - 统一卡片设计 */}
          {isProcessing && (
            <div className="mt-8 p-6 bg-card rounded-xl border border-primary/20 shadow-sm">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-primary/10 rounded-lg">
                    <Loader2 className="h-5 w-5 animate-spin text-primary" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-foreground">{processingStep}</h4>
                    <p className="text-sm text-muted-foreground">正在智能处理您的数据...</p>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-2xl font-bold text-primary">{processingProgress}%</div>
                  <div className="text-xs text-muted-foreground">已完成</div>
                </div>
              </div>
              <Progress value={processingProgress} className="h-3" />
              <div className="mt-3 text-xs text-muted-foreground">
                解析文件内容 → 过滤无效关键词 → 去重处理 → 导入数据库
              </div>
            </div>
          )}

          {/* 处理结果 - 现代化设计 */}
          {importResult && (
            <div className="mt-8 space-y-4">
              {/* 成功/失败状态 */}
              <Alert variant={importResult.success ? "default" : "destructive"}>
                <div className="flex items-center space-x-2">
              {importResult.success ? (
                    <CheckCircle className="h-5 w-5 text-green-500" />
                  ) : (
                    <XCircle className="h-5 w-5 text-red-500" />
                  )}
                  <AlertDescription>
                    {importResult.success ? importResult.message : importResult.error}
                  </AlertDescription>
                </div>
                </Alert>

              {/* 导入统计 */}
              {importResult.data && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <FileText className="h-5 w-5" />
                      <span>导入统计</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid gap-4 md:grid-cols-3">
                      {/* 文件信息 */}
                      <div className="space-y-2">
                        <h4 className="font-medium">文件信息</h4>
                        <div className="text-sm text-muted-foreground">
                          <p>文件名: {importResult.data.import.filename}</p>
                          <p>总行数: {importResult.data.import.totalLines}</p>
                          <p>原始关键词: {importResult.data.import.rawKeywords}</p>
                        </div>
                      </div>

                      {/* 最终导入结果 */}
                      <div className="space-y-2">
                        <h4 className="font-medium">最终结果</h4>
                        <div className="text-sm text-muted-foreground">
                          <p>成功导入: {importResult.data.api.totalImported}</p>
                          <p>数据库重复: {importResult.data.api.duplicates}</p>
                          <p>最终通过率: {importResult.data.parse.totalKeywords > 0
                            ? ((importResult.data.api.totalImported / importResult.data.parse.totalKeywords) * 100).toFixed(2) + '%'
                            : '0%'}</p>
                        </div>
                      </div>

                      {/* 处理状态 */}
                      <div className="space-y-2">
                        <h4 className="font-medium">处理状态</h4>
                        <div className="text-sm text-muted-foreground">
                          <p>✅ 文件解析完成</p>
                          <p>✅ AI智能筛选完成</p>
                          <p>✅ 数据库导入完成</p>
                        </div>
                      </div>
                    </div>

                    {/* 批次处理详情 */}
                    {importResult.data.api.batchResults && importResult.data.api.batchResults.length > 0 && (
                      <div className="mt-6">
                        <h4 className="font-medium mb-4">批次处理详情</h4>
                        <div className="space-y-4">
                          {importResult.data.api.batchResults.map((batch, index) => (
                            <div key={index} className="border rounded-lg p-4">
                              <div className="flex items-center justify-between mb-2">
                                <h5 className="font-medium">
                                  批次 {batch.batchIndex + 1} ({batch.startIndex + 1}-{batch.endIndex})
                                </h5>
                                <Badge variant={batch.success ? "default" : "destructive"}>
                                  {batch.success ? "成功" : "失败"}
                                </Badge>
                              </div>
                              <div className="text-sm text-muted-foreground">
                                <p>导入: {batch.imported}</p>
                                <p>跳过: {batch.skipped}</p>
                                {batch.duplicates.length > 0 && (
                                  <details className="mt-2">
                                    <summary className="cursor-pointer">
                                      重复关键词 ({batch.duplicates.length})
                                    </summary>
                                    <ul className="mt-1 ml-4 list-disc">
                                      {batch.duplicates.map((dup, idx) => (
                                        <li key={idx}>
                                          {dup.keyword} - {dup.reason}
                                        </li>
                                      ))}
                                    </ul>
                                  </details>
                                )}
                                {batch.errors.length > 0 && (
                                  <details className="mt-2">
                                    <summary className="cursor-pointer text-red-500">
                                      错误信息 ({batch.errors.length})
                                    </summary>
                                    <ul className="mt-1 ml-4 list-disc">
                                      {batch.errors.map((err, idx) => (
                                        <li key={idx}>
                                          {err.error}
                                          {err.keywords && (
                                            <ul className="ml-4 mt-1 list-disc">
                                              {err.keywords.map((kw, kwIdx) => (
                                                <li key={kwIdx}>{kw}</li>
                                              ))}
                                            </ul>
                                          )}
                                        </li>
                                      ))}
                                    </ul>
                                  </details>
                                )}
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* 失败批次详情 */}
                    {importResult.data.api.failedBatches && importResult.data.api.failedBatches.length > 0 && (
                      <div className="mt-6">
                        <h4 className="font-medium text-red-500 mb-4">失败批次</h4>
                        <div className="space-y-4">
                          {importResult.data.api.failedBatches.map((batch, index) => (
                            <div key={index} className="border border-red-200 rounded-lg p-4 bg-red-50">
                              <div className="flex items-center justify-between mb-2">
                                <h5 className="font-medium">
                                  批次 {batch.batchIndex + 1} ({batch.startIndex + 1}-{batch.endIndex})
                                </h5>
                                <span className="text-sm text-gray-500">
                                  {new Date(batch.timestamp).toLocaleString()}
                                </span>
                              </div>
                              <p className="text-red-600 mb-2">{batch.error}</p>
                              <details>
                                <summary className="cursor-pointer">
                                  受影响的关键词 ({batch.keywords.length})
                                </summary>
                                <ul className="mt-2 ml-4 list-disc">
                                  {batch.keywords.map((keyword, idx) => (
                                    <li key={idx}>{keyword}</li>
                                  ))}
                                </ul>
                              </details>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* 导入详情 - 现代化设计 */}
      {importResult?.success && importResult.data && (
        <>
          <Card className="data-card-enhanced">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Database className="h-6 w-6 text-primary" />
                <span>处理流程详情</span>
              </CardTitle>
              <CardDescription>
                完整的关键词处理流程统计和结果分析
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {/* 文件统计 */}
                <div className="space-y-4">
                  <h4 className="font-semibold text-foreground flex items-center space-x-2">
                    <FileText className="h-4 w-4 text-secondary" />
                    <span>文件统计</span>
                  </h4>
                  <div className="space-y-3">
                    <div className="flex justify-between items-center p-3 bg-muted rounded-lg">
                      <span className="text-sm text-muted-foreground">总行数</span>
                      <Badge className="badge-trend">{importResult.data.import.totalLines}</Badge>
                    </div>
                    <div className="flex justify-between items-center p-3 bg-muted rounded-lg">
                      <span className="text-sm text-muted-foreground">原始关键词</span>
                      <Badge className="badge-trend">{importResult.data.import.rawKeywords}</Badge>
                    </div>
                  </div>
                </div>

                {/* AI过滤统计 */}
                <div className="space-y-4">
                  <h4 className="font-semibold text-foreground flex items-center space-x-2">
                    <Filter className="h-4 w-4 text-warning" />
                    <span>AI过滤统计</span>
                  </h4>
                  <div className="space-y-3">
                    <div className="flex justify-between items-center p-3 bg-card border border-success/20 rounded-lg">
                      <span className="text-sm text-muted-foreground">AI接受</span>
                      <Badge className="badge-analyzed">{importResult.data.aiFilter.accepted}</Badge>
                    </div>
                    <div className="flex justify-between items-center p-3 bg-card border border-destructive/20 rounded-lg">
                      <span className="text-sm text-muted-foreground">AI拒绝</span>
                      <Badge className="bg-destructive/10 text-destructive border-destructive/20">{importResult.data.aiFilter.rejected}</Badge>
                    </div>
                    <div className="flex justify-between items-center p-3 bg-card border border-primary/20 rounded-lg">
                      <span className="text-sm text-muted-foreground">AI通过率</span>
                      <Badge className="bg-primary/10 text-primary border-primary/20">{importResult.data.aiFilter.passRate}</Badge>
                    </div>
                  </div>
                </div>

                {/* 导入结果 */}
                <div className="space-y-4">
                  <h4 className="font-semibold text-foreground flex items-center space-x-2">
                    <CheckCircle className="h-4 w-4 text-success" />
                    <span>导入结果</span>
                  </h4>
                  <div className="space-y-3">
                    <div className="flex justify-between items-center p-3 bg-card border border-primary/20 rounded-lg">
                      <span className="text-sm text-muted-foreground">最终通过率</span>
                      <Badge className="bg-orange-600 text-white border-orange-600 font-semibold">
                        {importResult.data.parse.totalKeywords > 0
                          ? ((importResult.data.api.totalImported / importResult.data.parse.totalKeywords) * 100).toFixed(2) + '%'
                          : '0%'
                        }
                      </Badge>
                    </div>
                    <div className="flex justify-between items-center p-3 bg-card border border-success/20 rounded-lg">
                      <span className="text-sm text-muted-foreground">成功导入</span>
                      <Badge className="badge-analyzed">{importResult.data.api.totalImported}</Badge>
                    </div>
                    <div className="flex justify-between items-center p-3 bg-card border border-warning/20 rounded-lg">
                      <span className="text-sm text-muted-foreground">数据库重复</span>
                      <Badge className="bg-warning/10 text-warning border-warning/20">{importResult.data.api.duplicates}</Badge>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* AI过滤关键词详情 - 现代化设计 */}
          <Card className="data-card-enhanced">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Filter className="h-6 w-6 text-warning" />
                <span>AI过滤关键词详情</span>
              </CardTitle>
              <CardDescription>
                查看被AI过滤的具体关键词和原因，帮助优化数据质量
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {/* 品牌词 */}
                {importResult.data.aiFilter.rejectionReasons.brands > 0 && (
                  <div className="space-y-3">
                    <div className="flex items-center space-x-2">
                      <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                      <h5 className="font-medium text-foreground">品牌词 ({importResult.data.aiFilter.rejectionReasons.brands})</h5>
                    </div>
                    <div className="flex flex-wrap gap-2">
                      {importResult.data.aiFilter.rejectedKeywords.brands.map((keyword, index) => (
                        <Badge key={index} variant="outline" className="bg-red-50 text-red-700 border-red-200">
                          {keyword}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

                {/* 通用词汇 */}
                {importResult.data.aiFilter.rejectionReasons.genericWords > 0 && (
                  <div className="space-y-3">
                    <div className="flex items-center space-x-2">
                      <div className="w-3 h-3 bg-orange-500 rounded-full"></div>
                      <h5 className="font-medium text-foreground">通用词汇 ({importResult.data.aiFilter.rejectionReasons.genericWords})</h5>
                    </div>
                    <div className="flex flex-wrap gap-2">
                      {importResult.data.aiFilter.rejectedKeywords.genericWords.map((keyword, index) => (
                        <Badge key={index} variant="outline" className="bg-orange-50 text-orange-700 border-orange-200">
                          {keyword}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

                {/* 人名 */}
                {importResult.data.aiFilter.rejectionReasons.personNames > 0 && (
                  <div className="space-y-3">
                    <div className="flex items-center space-x-2">
                      <div className="w-3 h-3 bg-purple-500 rounded-full"></div>
                      <h5 className="font-medium text-foreground">人名 ({importResult.data.aiFilter.rejectionReasons.personNames})</h5>
                    </div>
                    <div className="flex flex-wrap gap-2">
                      {importResult.data.aiFilter.rejectedKeywords.personNames.map((keyword, index) => (
                        <Badge key={index} variant="outline" className="bg-purple-50 text-purple-700 border-purple-200">
                          {keyword}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

                {/* 非英文 */}
                {importResult.data.aiFilter.rejectionReasons.nonEnglish > 0 && (
                  <div className="space-y-3">
                    <div className="flex items-center space-x-2">
                      <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                      <h5 className="font-medium text-foreground">非英文 ({importResult.data.aiFilter.rejectionReasons.nonEnglish})</h5>
                    </div>
                    <div className="flex flex-wrap gap-2">
                      {importResult.data.aiFilter.rejectedKeywords.nonEnglish.map((keyword, index) => (
                        <Badge key={index} variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                          {keyword}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

                {/* 无意义内容 */}
                {importResult.data.aiFilter.rejectionReasons.meaningless > 0 && (
                  <div className="space-y-3">
                    <div className="flex items-center space-x-2">
                      <div className="w-3 h-3 bg-gray-500 rounded-full"></div>
                      <h5 className="font-medium text-foreground">无意义内容 ({importResult.data.aiFilter.rejectionReasons.meaningless})</h5>
                    </div>
                    <div className="flex flex-wrap gap-2">
                      {importResult.data.aiFilter.rejectedKeywords.meaningless.map((keyword, index) => (
                        <Badge key={index} variant="outline" className="bg-gray-50 text-gray-700 border-gray-200">
                          {keyword}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

                {/* 竞争激烈 */}
                {importResult.data.aiFilter.rejectionReasons.tooCompetitive > 0 && (
                  <div className="space-y-3">
                    <div className="flex items-center space-x-2">
                      <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                      <h5 className="font-medium text-foreground">竞争激烈 ({importResult.data.aiFilter.rejectionReasons.tooCompetitive})</h5>
                    </div>
                    <div className="flex flex-wrap gap-2">
                      {importResult.data.aiFilter.rejectedKeywords.tooCompetitive.map((keyword, index) => (
                        <Badge key={index} variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">
                          {keyword}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

                {/* 无SEO价值 */}
                {importResult.data.aiFilter.rejectionReasons.noSeoValue > 0 && (
                  <div className="space-y-3">
                    <div className="flex items-center space-x-2">
                      <div className="w-3 h-3 bg-pink-500 rounded-full"></div>
                      <h5 className="font-medium text-foreground">无SEO价值 ({importResult.data.aiFilter.rejectionReasons.noSeoValue})</h5>
                    </div>
                    <div className="flex flex-wrap gap-2">
                      {importResult.data.aiFilter.rejectedKeywords.noSeoValue.map((keyword, index) => (
                        <Badge key={index} variant="outline" className="bg-pink-50 text-pink-700 border-pink-200">
                          {keyword}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

                {/* 服务异常 */}
                {importResult.data.aiFilter.rejectionReasons.serviceError > 0 && (
                  <div className="space-y-3">
                    <div className="flex items-center space-x-2">
                      <div className="w-3 h-3 bg-red-600 rounded-full"></div>
                      <h5 className="font-medium text-foreground">服务异常 ({importResult.data.aiFilter.rejectionReasons.serviceError})</h5>
                    </div>
                    <div className="flex flex-wrap gap-2">
                      {importResult.data.aiFilter.rejectedKeywords.serviceError.map((keyword, index) => (
                        <Badge key={index} variant="outline" className="bg-red-50 text-red-700 border-red-200">
                          {keyword}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

                {/* 其他原因 */}
                {importResult.data.aiFilter.rejectionReasons.other > 0 && (
                  <div className="space-y-3">
                    <div className="flex items-center space-x-2">
                      <div className="w-3 h-3 bg-gray-400 rounded-full"></div>
                      <h5 className="font-medium text-foreground">其他原因 ({importResult.data.aiFilter.rejectionReasons.other})</h5>
                    </div>
                    <div className="flex flex-wrap gap-2">
                      {importResult.data.aiFilter.rejectedKeywords.other.map((keyword, index) => (
                        <Badge key={index} variant="outline" className="bg-gray-50 text-gray-700 border-gray-200">
                          {keyword}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

                {/* 如果没有被拒绝的关键词 */}
                {importResult.data.aiFilter.rejected === 0 && (
                  <div className="text-center py-8 text-muted-foreground">
                    🎉 所有关键词都通过了AI筛选！
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </>
      )}
    </div>
  )
}
