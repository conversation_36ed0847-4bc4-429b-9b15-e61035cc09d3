import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { ShieldX, ArrowLeft } from "lucide-react";
import Link from "next/link";

export default function Unauthorized() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-background">
      <Card className="w-full max-w-md data-card-enhanced">
        <CardHeader className="text-center">
          <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-warning/10 flex items-center justify-center">
            <ShieldX className="h-8 w-8 text-warning" />
          </div>
          <CardTitle className="text-xl">权限不足</CardTitle>
          <CardDescription>
            您当前是普通用户，无法访问管理员专用页面。
          </CardDescription>
        </CardHeader>
        <CardContent className="text-center">
          <div className="space-y-4">
            <div className="p-4 bg-muted/50 rounded-lg">
              <p className="text-sm font-medium text-foreground mb-2">
                您可以使用以下功能：
              </p>
              <div className="space-y-2">
                <Link href="/console/upload">
                  <Button variant="outline" className="w-full">
                    📁 数据上传
                  </Button>
                </Link>
                <Link href="/console/analyze">
                  <Button variant="outline" className="w-full">
                    🤖 AI分析
                  </Button>
                </Link>
              </div>
            </div>
            <div className="text-xs text-muted-foreground">
              如需管理员权限，请联系：<EMAIL>
            </div>
            <Link href="/">
              <Button variant="ghost" className="w-full">
                <ArrowLeft className="h-4 w-4 mr-2" />
                返回首页
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
