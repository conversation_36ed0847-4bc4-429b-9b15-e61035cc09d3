'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, use<PERSON>ara<PERSON> } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../../../../components/ui/card'
import { But<PERSON> } from '../../../../../components/ui/button'
import { Input } from '../../../../../components/ui/input'
import { Label } from '../../../../../components/ui/label'
import { Textarea } from '../../../../../components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../../../../components/ui/select'
import { Badge } from '../../../../../components/ui/badge'
import { ArrowLeft, Save, Loader2 } from 'lucide-react'
import ConsoleLayout from '../../../../../components/console-layout'
import Link from 'next/link'
import { Toaster, toast } from 'sonner'

interface Keyword {
  id: string
  keyword: string
  user_intent?: string
  user_pain_point?: string
  competition_level?: 'easy' | 'medium' | 'hard'
  competition_score?: number
  serp_analysis?: string
  recommended_domains?: string
  category?: string
  source?: string
  notes?: string
  created_at?: string
}

interface Category {
  id: string
  name: string
  english_name: string
}

export default function EditKeywordPage() {
  const router = useRouter()
  const params = useParams()
  const keywordId = params?.id as string

  const [keyword, setKeyword] = useState<Keyword | null>(null)
  const [categories, setCategories] = useState<Category[]>([])
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [formData, setFormData] = useState({
    keyword: '',
    user_intent: '',
    user_pain_point: '',
    competition_level: '',
    competition_score: '',
    serp_analysis: '',
    recommended_domains: '',
    category: '',
    notes: ''
  })

  // 设置页面标题
  useEffect(() => {
    document.title = '编辑关键词 - CatchIdeas'
  }, [])

  // 获取关键词数据
  useEffect(() => {
    if (keywordId) {
      fetchKeyword()
      fetchCategories()
    }
  }, [keywordId])

  const fetchKeyword = async () => {
    try {
      // 添加时间戳防止缓存
      const timestamp = Date.now()
      const response = await fetch(`/api/keywords/detail?id=${keywordId}&t=${timestamp}`, {
        cache: 'no-store',
        headers: {
          'Cache-Control': 'no-cache'
        }
      })
      const data = await response.json()

      if (data.success && data.data) {
        const keywordData = data.data
        setKeyword(keywordData)
        setFormData({
          keyword: keywordData.keyword || '',
          user_intent: keywordData.user_intent || '',
          user_pain_point: keywordData.user_pain_point || '',
          competition_level: keywordData.competition_level || '',
          competition_score: keywordData.competition_score?.toString() || '',
          serp_analysis: keywordData.serp_analysis || '',
          recommended_domains: keywordData.recommended_domains || '',
          category: keywordData.category || '',
          notes: keywordData.notes || ''
        })
      } else {
        toast.error('获取关键词详情失败')
      }
    } catch (error) {
      toast.error('获取关键词详情失败')
    } finally {
      setLoading(false)
    }
  }

  const fetchCategories = async () => {
    try {
      // 添加时间戳防止缓存
      const timestamp = Date.now()
      const response = await fetch(`/api/categories/list?t=${timestamp}`, {
        cache: 'no-store',
        headers: {
          'Cache-Control': 'no-cache'
        }
      })
      const data = await response.json()

      if (data.success && data.data.categories) {
        setCategories(data.data.categories)
      }
    } catch (error) {
      console.error('获取分类失败:', error)
    }
  }

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleSave = async () => {
    setSaving(true)
    try {
      const response = await fetch('/api/keywords/update', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: keywordId,
          ...formData,
          competition_score: formData.competition_score ? parseFloat(formData.competition_score) : null
        })
      })

      const data = await response.json()

      if (data.success) {
        toast.success('关键词保存成功')
        router.push('/console/keywords')
      } else {
        toast.error('保存失败: ' + data.message)
      }
    } catch (error) {
      console.error('保存失败:', error)
      toast.error('保存失败，请重试')
    } finally {
      setSaving(false)
    }
  }

  // 根据分数获取竞争难度颜色 - 统一的分数区间标准
  const getCompetitionColor = (score: number) => {
    if (score <= 3) return 'bg-green-100 text-green-800'
    if (score <= 7) return 'bg-yellow-100 text-yellow-800'
    return 'bg-red-100 text-red-800'
  }

  // 根据分数获取竞争难度文本
  const getCompetitionText = (score: number) => {
    if (score <= 3) return '简单'
    if (score <= 7) return '中等'
    return '困难'
  }

  if (loading) {
    return (
      <ConsoleLayout title="编辑关键词">
        <div className="flex items-center justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin" />
          <span className="ml-2">加载中...</span>
        </div>
        <Toaster position="top-right" richColors closeButton duration={5000} />
      </ConsoleLayout>
    )
  }

  if (!keyword) {
    return (
      <ConsoleLayout title="编辑关键词">
        <div className="text-center py-8">
          <p className="text-muted-foreground">关键词不存在</p>
          <Link href="/console/keywords">
            <Button className="mt-4">
              <ArrowLeft className="h-4 w-4 mr-2" />
              返回关键词列表
            </Button>
          </Link>
        </div>
        <Toaster position="top-right" richColors closeButton duration={5000} />
      </ConsoleLayout>
    )
  }

  return (
    <ConsoleLayout
      title="编辑关键词"
      actions={
        <div className="flex items-center space-x-2">
          <Link href="/console/keywords">
            <Button variant="outline">
              <ArrowLeft className="h-4 w-4 mr-2" />
              返回
            </Button>
          </Link>
          <Button onClick={handleSave} disabled={saving}>
            {saving ? (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <Save className="h-4 w-4 mr-2" />
            )}
            {saving ? '保存中...' : '保存'}
          </Button>
        </div>
      }
    >
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>基本信息</CardTitle>
            <CardDescription>编辑关键词的基本信息</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="keyword">关键词</Label>
                <Input
                  id="keyword"
                  value={formData.keyword}
                  onChange={(e) => handleInputChange('keyword', e.target.value)}
                  placeholder="输入关键词"
                />
              </div>
              <div>
                <Label htmlFor="category">分类</Label>
                <Select value={formData.category} onValueChange={(value) => handleInputChange('category', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="选择分类" />
                  </SelectTrigger>
                  <SelectContent>
                    {categories.map((category) => (
                      <SelectItem key={category.id} value={category.english_name}>
                        {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>AI分析结果</CardTitle>
            <CardDescription>编辑AI分析的结果</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="user_intent">用户意图</Label>
              <Textarea
                id="user_intent"
                value={formData.user_intent}
                onChange={(e) => handleInputChange('user_intent', e.target.value)}
                placeholder="描述用户搜索此关键词的意图"
                rows={3}
              />
            </div>
            <div>
              <Label htmlFor="user_pain_point">用户痛点</Label>
              <Textarea
                id="user_pain_point"
                value={formData.user_pain_point}
                onChange={(e) => handleInputChange('user_pain_point', e.target.value)}
                placeholder="描述用户的痛点和需求"
                rows={3}
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="competition_level">竞争难度</Label>
                <Select value={formData.competition_level} onValueChange={(value) => handleInputChange('competition_level', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="选择竞争难度" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="easy">简单</SelectItem>
                    <SelectItem value="medium">中等</SelectItem>
                    <SelectItem value="hard">困难</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="competition_score">竞争分数 (1-10)</Label>
                <Input
                  id="competition_score"
                  type="number"
                  min="1"
                  max="10"
                  step="0.1"
                  value={formData.competition_score}
                  onChange={(e) => handleInputChange('competition_score', e.target.value)}
                  placeholder="1-10分"
                />
              </div>
            </div>
            <div>
              <Label htmlFor="serp_analysis">SERP分析</Label>
              <Textarea
                id="serp_analysis"
                value={formData.serp_analysis}
                onChange={(e) => handleInputChange('serp_analysis', e.target.value)}
                placeholder="搜索结果页面分析"
                rows={4}
              />
            </div>
            <div>
              <Label htmlFor="recommended_domains">推荐域名</Label>
              <Textarea
                id="recommended_domains"
                value={formData.recommended_domains}
                onChange={(e) => handleInputChange('recommended_domains', e.target.value)}
                placeholder="推荐的域名列表"
                rows={3}
              />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>备注</CardTitle>
            <CardDescription>添加额外的备注信息</CardDescription>
          </CardHeader>
          <CardContent>
            <div>
              <Label htmlFor="notes">备注</Label>
              <Textarea
                id="notes"
                value={formData.notes}
                onChange={(e) => handleInputChange('notes', e.target.value)}
                placeholder="添加备注信息"
                rows={3}
              />
            </div>
          </CardContent>
        </Card>

        {/* 显示当前竞争难度 */}
        {formData.competition_score && (
          <Card>
            <CardHeader>
              <CardTitle>当前状态</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center space-x-2">
                <span>竞争难度:</span>
                <Badge className={getCompetitionColor(parseFloat(formData.competition_score) || 0)}>
                  {getCompetitionText(parseFloat(formData.competition_score) || 0)} ({formData.competition_score}/10)
                </Badge>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
      <Toaster position="top-right" richColors closeButton duration={5000} />
    </ConsoleLayout>
  )
}