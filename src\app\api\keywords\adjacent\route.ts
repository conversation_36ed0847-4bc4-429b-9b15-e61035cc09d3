import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const keywordId = searchParams.get('keyword_id')

    if (!keywordId) {
      return NextResponse.json({
        success: false,
        error: '缺少关键词ID参数'
      }, { status: 400 })
    }

    // 构建API请求URL
    const apiUrl = `https://api.xstty.com/api/adjacent.php?keyword_id=${encodeURIComponent(keywordId)}`

    const response = await fetch(apiUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache'
      },
      cache: 'no-store'
    })

    if (!response.ok) {
      return NextResponse.json({
        success: false,
        error: `API请求失败: ${response.status}`
      }, { status: response.status })
    }

    const responseText = await response.text()

    let data
    try {
      data = JSON.parse(responseText)
    } catch (parseError) {
      return NextResponse.json({
        success: false,
        error: 'API响应格式错误'
      }, { status: 500 })
    }

    return NextResponse.json(data)

  } catch (error) {
    return NextResponse.json({
      success: false,
      error: '服务器内部错误'
    }, { status: 500 })
  }
}
