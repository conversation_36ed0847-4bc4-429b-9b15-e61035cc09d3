'use client'

import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card'
import { Badge } from '../../components/ui/badge'
import { Button } from '../../components/ui/button'
import { Input } from '../../components/ui/input'
import { ExternalLink, Search, TrendingUp, Star, ChevronDown, ChevronUp, ExternalLink as LinkIcon } from 'lucide-react'
import FrontendHeader from '../../components/frontend-header'
import FrontendFooter from '../../components/frontend-footer'

interface RootKeyword {
  keyword: string
  isStarred?: boolean
}

interface RootCategory {
  name: string
  nameEn: string
  description?: string
  keywords: RootKeyword[]
}

export default function RootsPage() {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [collapsedCategories, setCollapsedCategories] = useState<Record<string, boolean>>({})

  // AI工具站和小游戏词根数据
  const rootCategories: RootCategory[] = [
    // === AI 工具站词根 ===
    {
      name: '现象级旗舰模型',
      nameEn: 'Flagship AI Models',
      description: '顶级AI模型品牌词根，流量巨大',
      keywords: [
        { keyword: 'openai', isStarred: true },
        { keyword: 'gpt', isStarred: true },
        { keyword: 'claude', isStarred: true },
        { keyword: 'gemini', isStarred: true },
        { keyword: 'llama', isStarred: true },
        { keyword: 'mistral', isStarred: true },
        { keyword: 'grok', isStarred: true },
        { keyword: 'anthropic' },
        { keyword: 'cohere' },
        { keyword: 'huggingface' }
      ]
    },
    {
      name: 'AI生成设计',
      nameEn: 'AI Generation & Design',
      description: '创意生成类头部产品词根',
      keywords: [
        { keyword: 'midjourney', isStarred: true },
        { keyword: 'dalle', isStarred: true },
        { keyword: 'flux', isStarred: true },
        { keyword: 'runway', isStarred: true },
        { keyword: 'luma', isStarred: true },
        { keyword: 'imagen', isStarred: true },
        { keyword: 'stable', isStarred: true },
        { keyword: 'comfyui', isStarred: true },
        { keyword: 'haiper' },
        { keyword: 'pika' },
        { keyword: 'kling' },
        { keyword: 'ideogram' },
        { keyword: 'leonardo' },
        { keyword: 'firefly' },
        { keyword: 'framer' }
      ]
    },
    {
      name: '开发者工具',
      nameEn: 'Developer Tools',
      description: '开发者工具与低代码平台',
      keywords: [
        { keyword: 'cursor', isStarred: true },
        { keyword: 'windsurf', isStarred: true },
        { keyword: 'bolt', isStarred: true },
        { keyword: 'lovable', isStarred: true },
        { keyword: 'replit', isStarred: true },
        { keyword: 'copilot', isStarred: true },
        { keyword: 'devin', isStarred: true },
        { keyword: 'v0', isStarred: true },
        { keyword: 'augment', isStarred: true },
        { keyword: 'aider', isStarred: true },
        { keyword: 'continue' },
        { keyword: 'supermaven' },
        { keyword: 'tabnine' },
        { keyword: 'crewai' },
        { keyword: 'autogen' },
        { keyword: 'langflow' },
        { keyword: 'flowise' },
        { keyword: 'n8n' },
        { keyword: 'mcp' },
        { keyword: 'codeium' },
        { keyword: 'cody' },
        { keyword: 'sourcegraph' }
      ]
    },
    {
      name: '办公效率',
      nameEn: 'Office Productivity',
      description: '办公协作与效率提升工具',
      keywords: [
        { keyword: 'notion', isStarred: true },
        { keyword: 'gamma', isStarred: true },
        { keyword: 'felo', isStarred: true },
        { keyword: 'figma', isStarred: true },
        { keyword: 'canva', isStarred: true },
        { keyword: 'miro', isStarred: true },
        { keyword: 'grammarly', isStarred: true },
        { keyword: 'perplexity', isStarred: true },
        { keyword: 'consensus' },
        { keyword: 'quillbot' },
        { keyword: 'tldraw' },
        { keyword: 'scite' },
        { keyword: 'paperpal' },
        { keyword: 'scholarcy' },
        { keyword: 'obsidian' },
        { keyword: 'airtable' },
        { keyword: 'monday' },
        { keyword: 'asana' },
        { keyword: 'trello' },
        { keyword: 'slack' },
        { keyword: 'discord' },
        { keyword: 'zoom' },
        { keyword: 'loom' },
        { keyword: 'calendly' },
        { keyword: 'typeform' },
        { keyword: 'ifttt' }
      ]
    },
    {
      name: '搜索对话智能体',
      nameEn: 'Search & Chat Agents',
      description: '智能搜索与对话助手平台',
      keywords: [
        { keyword: 'exa', isStarred: true },
        { keyword: 'phind', isStarred: true },
        { keyword: 'metaso', isStarred: true },
        { keyword: 'devv', isStarred: true },
        { keyword: 'searchgpt', isStarred: true },
        { keyword: 'you', isStarred: true },
        { keyword: 'andi', isStarred: true },
        { keyword: 'kagi', isStarred: true },
        { keyword: 'scispace' },
        { keyword: 'chatpdf' },
        { keyword: 'chatdoc' },
        { keyword: 'humata' },
        { keyword: 'elicit' },
        { keyword: 'semantic' },
        { keyword: 'tavily' },
        { keyword: 'brave' },
        { keyword: 'neeva' },
        { keyword: 'green' }
      ]
    },
    {
      name: '部署托管API',
      nameEn: 'Deployment & Hosting APIs',
      description: 'AI模型部署与API服务平台',
      keywords: [
        { keyword: 'modal', isStarred: true },
        { keyword: 'runpod', isStarred: true },
        { keyword: 'together', isStarred: true },
        { keyword: 'replicate', isStarred: true },
        { keyword: 'fal', isStarred: true },
        { keyword: 'fireworks', isStarred: true },
        { keyword: 'anyscale', isStarred: true },
        { keyword: 'railway' },
        { keyword: 'supabase' },
        { keyword: 'render' },
        { keyword: 'netlify' },
        { keyword: 'vercel' },
        { keyword: 'octoai' },
        { keyword: 'baseten' },
        { keyword: 'deepinfra' },
        { keyword: 'groq' },
        { keyword: 'cerebras' },
        { keyword: 'lepton' },
        { keyword: 'banana' },
        { keyword: 'beam' },
        { keyword: 'vast' },
        { keyword: 'lambda' },
        { keyword: 'paperspace' },
        { keyword: 'gradient' }
      ]
    },
    {
      name: '音频语音',
      nameEn: 'Audio & Voice',
      description: '语音生成类新兴热点',
      keywords: [
        { keyword: 'elevenlabs', isStarred: true },
        { keyword: 'suno', isStarred: true },
        { keyword: 'mubert', isStarred: true },
        { keyword: 'udio', isStarred: true },
        { keyword: 'speechify', isStarred: true },
        { keyword: 'resemble', isStarred: true },
        { keyword: 'descript' },
        { keyword: 'whisper' },
        { keyword: 'voicemod' },
        { keyword: 'lovo' },
        { keyword: 'synthesia' },
        { keyword: 'replica' },
        { keyword: 'wellsaid' },
        { keyword: 'play' },
        { keyword: 'riffusion' },
        { keyword: 'boomy' },
        { keyword: 'soundraw' },
        { keyword: 'beatoven' }
      ]
    },
    {
      name: '检测安全',
      nameEn: 'Detection & Security',
      description: '合规与风控需求持续涨',
      keywords: [
        { keyword: 'detector', isStarred: true },
        { keyword: 'deepfake', isStarred: true },
        { keyword: 'originality', isStarred: true },
        { keyword: 'turnitin', isStarred: true },
        { keyword: 'copyleaks', isStarred: true },
        { keyword: 'winston', isStarred: true },
        { keyword: 'prompt' },
        { keyword: 'gptzero' },
        { keyword: 'sapling' },
        { keyword: 'crossplag' },
        { keyword: 'contentatscale' },
        { keyword: 'writer' },
        { keyword: 'undetectable' },
        { keyword: 'humbot' },
        { keyword: 'bypass' }
      ]
    },
    {
      name: '长尾前缀',
      nameEn: 'Long-tail Prefixes',
      description: '和以上词根任意组合',
      keywords: [
        { keyword: 'free', isStarred: true },
        { keyword: 'online', isStarred: true },
        { keyword: 'best', isStarred: true },
        { keyword: 'top', isStarred: true },
        { keyword: 'unlimited', isStarred: true },
        { keyword: 'premium', isStarred: true },
        { keyword: 'no sign up' },
        { keyword: 'no login' },
        { keyword: 'quick' },
        { keyword: 'easy' },
        { keyword: 'simple' },
        { keyword: 'powerful' },
        { keyword: 'advanced' },
        { keyword: 'professional' },
        { keyword: 'api' },
        { keyword: 'github' },
        { keyword: 'open source' },
        { keyword: 'alternative' },
        { keyword: 'clone' },
        { keyword: 'generator' },
        { keyword: 'maker' },
        { keyword: 'creator' },
        { keyword: 'builder' },
        { keyword: 'tool' },
        { keyword: 'app' },
        { keyword: 'platform' },
        { keyword: 'software' }
      ]
    },
    // === 小游戏词根 ===
    {
      name: '游戏玩法',
      nameEn: 'Game Mechanics',
      description: '玩法关键词自带细分流量',
      keywords: [
        { keyword: 'incremental', isStarred: true },
        { keyword: 'idle', isStarred: true },
        { keyword: 'clicker', isStarred: true },
        { keyword: 'puzzle', isStarred: true },
        { keyword: 'wordle', isStarred: true },
        { keyword: 'tetris', isStarred: true },
        { keyword: 'match3', isStarred: true },
        { keyword: 'runner', isStarred: true },
        { keyword: '2048' },
        { keyword: 'flappy' },
        { keyword: 'merge' },
        { keyword: 'tower' },
        { keyword: 'roguelike' },
        { keyword: 'platformer' },
        { keyword: 'shooter' },
        { keyword: 'racing' },
        { keyword: 'strategy' },
        { keyword: 'rpg' },
        { keyword: 'adventure' },
        { keyword: 'arcade' },
        { keyword: 'action' },
        { keyword: 'simulation' },
        { keyword: 'survival' },
        { keyword: 'sandbox' }
      ]
    },
    {
      name: '游戏平台',
      nameEn: 'Game Platforms',
      description: '游戏发布和分发平台',
      keywords: [
        { keyword: 'poki', isStarred: true },
        { keyword: 'crazygames', isStarred: true },
        { keyword: 'itch', isStarred: true },
        { keyword: 'newgrounds', isStarred: true },
        { keyword: 'friv', isStarred: true },
        { keyword: 'kizi', isStarred: true },
        { keyword: 'y8', isStarred: true },
        { keyword: 'miniclip', isStarred: true },
        { keyword: 'coolmath' },
        { keyword: 'kongregate' },
        { keyword: 'armor' },
        { keyword: 'steam' },
        { keyword: 'agame' },
        { keyword: 'lagged' },
        { keyword: 'gamepix' }
      ]
    },
    {
      name: '游戏技术',
      nameEn: 'Game Technology',
      description: '技术栈词根易抓到开发者搜索',
      keywords: [
        { keyword: 'webgl', isStarred: true },
        { keyword: 'html5', isStarred: true },
        { keyword: 'godot', isStarred: true },
        { keyword: 'unity', isStarred: true },
        { keyword: 'unreal', isStarred: true },
        { keyword: 'threejs', isStarred: true },
        { keyword: 'phaser', isStarred: true },
        { keyword: 'wasm' },
        { keyword: 'defold' },
        { keyword: 'pixijs' },
        { keyword: 'kaboom' },
        { keyword: 'construct' },
        { keyword: 'gamemaker' },
        { keyword: 'canvas' },
        { keyword: 'webxr' },
        { keyword: 'babylonjs' },
        { keyword: 'aframe' },
        { keyword: 'playcanvas' },
        { keyword: 'cocos' },
        { keyword: 'flutter' }
      ]
    },
    {
      name: '游戏变现',
      nameEn: 'Game Monetization',
      description: '搜索意图强、易变现',
      keywords: [
        { keyword: 'unblocked', isStarred: true },
        { keyword: 'instant', isStarred: true },
        { keyword: 'multiplayer', isStarred: true },
        { keyword: 'addictive', isStarred: true },
        { keyword: 'trending', isStarred: true },
        { keyword: 'popular', isStarred: true },
        { keyword: 'chromebook' },
        { keyword: 'school' },
        { keyword: 'mini' },
        { keyword: 'pwa' },
        { keyword: 'offline' },
        { keyword: 'mobile' },
        { keyword: 'tablet' },
        { keyword: 'fullscreen' },
        { keyword: 'leaderboard' },
        { keyword: 'achievement' },
        { keyword: 'competitive' },
        { keyword: 'social' },
        { keyword: 'viral' },
        { keyword: 'endless' }
      ]
    },
    {
      name: '视觉风格',
      nameEn: 'Visual Styles',
      description: '热门视觉风格与美术风格词根',
      keywords: [
        { keyword: 'pixel', isStarred: true },
        { keyword: 'retro', isStarred: true },
        { keyword: 'neon', isStarred: true },
        { keyword: 'cyberpunk', isStarred: true },
        { keyword: 'minimalist', isStarred: true },
        { keyword: 'cartoon', isStarred: true },
        { keyword: 'anime' },
        { keyword: 'ghibli' },
        { keyword: 'vaporwave' },
        { keyword: 'synthwave' },
        { keyword: 'steampunk' },
        { keyword: 'gothic' },
        { keyword: 'medieval' },
        { keyword: 'futuristic' },
        { keyword: 'abstract' },
        { keyword: 'geometric' },
        { keyword: 'hand-drawn' },
        { keyword: 'watercolor' }
      ]
    }
  ]


  // 获取所有分类名称
  const categories = ['all', ...rootCategories.map(cat => cat.name)]

  const filteredRoots = rootCategories.filter(category => {
    if (selectedCategory !== 'all' && category.name !== selectedCategory) {
      return false
    }

    if (searchTerm) {
      return category.keywords.some(item =>
        item.keyword.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    return true
  }).map(category => ({
    ...category,
    keywords: category.keywords.filter(item =>
      !searchTerm ||
      item.keyword.toLowerCase().includes(searchTerm.toLowerCase())
    )
  }))

  // 生成Google Trends链接
  const generateTrendsUrl = (keyword: string) => {
    return `https://trends.google.com/trends/explore?date=now%207-d&geo=US&q=${encodeURIComponent(keyword)},gpts&hl=en-US`
  }

  // 切换分类折叠状态
  const toggleCategoryCollapse = (categoryName: string) => {
    setCollapsedCategories(prev => ({
      ...prev,
      [categoryName]: !prev[categoryName]
    }))
  }

  // 批量打开分类下所有关键词的Google Trends链接
  const openAllTrendsInCategory = (category: RootCategory) => {
    category.keywords.forEach((item, index) => {
      // 随机间隔500ms-1500ms，避免触发谷歌频率限制
      const randomDelay = Math.random() * 1000 + 500 + (index * 200) // 基础随机500-1500ms + 递增间隔
      setTimeout(() => {
        window.open(generateTrendsUrl(item.keyword), '_blank', 'noopener,noreferrer')
      }, randomDelay)
    })
  }

  return (
    <div className="min-h-screen bg-background">
      {/* 使用统一前台顶部组件 */}
      <FrontendHeader />

      {/* 搜索和筛选区域 */}
      <section className="py-6 px-4 bg-background border-b">
        <div className="container mx-auto">
          <div className="max-w-4xl mx-auto">
            <div className="bg-card rounded-xl shadow-sm border p-6">
              {/* 页面标题 */}
              <div className="text-center mb-6">
                <h1 className="text-3xl font-bold text-foreground mb-2">词根导航</h1>
                <p className="text-muted-foreground">AI工具站 & 小游戏关键词挖掘，已验证Google Trends热度，涵盖14大分类300+精选词根</p>
              </div>

              {/* 搜索框 - 居中显示 */}
              <div className="flex justify-center mb-6">
                <div className="relative w-full max-w-md">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-muted-foreground" />
                  <Input
                    type="text"
                    placeholder="搜索词根..."
                    className="pl-10 h-12 text-lg border-2 border-purple-200 focus:border-purple-500 focus:ring-purple-500 hover:border-purple-300 transition-colors"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
              </div>

              {/* 分类筛选 */}
              <div className="flex flex-wrap justify-center gap-2">
                {categories.map((category) => (
                  <Button
                    key={category}
                    variant={selectedCategory === category ? "default" : "outline"}
                    size="sm"
                    onClick={() => setSelectedCategory(category)}
                    className={selectedCategory === category
                      ? "bg-purple-600 hover:bg-purple-700 border-purple-600 text-white"
                      : "border-2 border-purple-200 hover:border-purple-500 hover:bg-purple-50 transition-colors"
                    }
                  >
                    {category === 'all' ? '全部' : category}
                  </Button>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 词根内容区域 */}
      <section className="py-8 px-4">
        <div className="container mx-auto">
          {filteredRoots.length === 0 ? (
            <Card className="text-center py-12">
              <CardContent>
                <Search className="h-16 w-16 mx-auto text-gray-400 mb-4" />
                <h3 className="text-xl font-semibold text-foreground mb-2">未找到相关词根</h3>
                <p className="text-muted-foreground">
                  试试调整搜索关键词或选择其他分类
                </p>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-8">
              {filteredRoots.map((category) => (
                <div key={category.name}>
                  <div className="flex items-center mb-6">
                    <div className="p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg mr-3">
                      <TrendingUp className="h-5 w-5 text-white" />
                    </div>
                    <div className="flex-1">
                      <h2 className="text-2xl font-bold text-foreground">{category.name}</h2>
                      <p className="text-sm text-muted-foreground">{category.nameEn}</p>
                      {category.description && (
                        <p className="text-sm text-blue-600 mt-1 font-medium">{category.description}</p>
                      )}
                    </div>
                    <div className="flex items-center gap-2 ml-auto">
                      <Badge variant="secondary">
                        {category.keywords.length} 个词根
                      </Badge>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => openAllTrendsInCategory(category)}
                        className="text-xs border-2 border-purple-200 hover:border-purple-500 hover:bg-purple-50 transition-colors flex items-center gap-1"
                      >
                        <LinkIcon className="h-3 w-3" />
                        新窗口
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => toggleCategoryCollapse(category.name)}
                        className="text-xs"
                      >
                        {collapsedCategories[category.name] ? (
                          <>
                            <ChevronDown className="h-4 w-4 mr-1" />
                            展开
                          </>
                        ) : (
                          <>
                            <ChevronUp className="h-4 w-4 mr-1" />
                            收起
                          </>
                        )}
                      </Button>
                    </div>
                  </div>

                  {!collapsedCategories[category.name] && (
                    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-3">
                      {/* 先显示标星的词根 */}
                      {category.keywords
                        .filter(item => item.isStarred)
                        .map((item, index) => (
                        <Card key={`starred-${index}`} className="group hover:shadow-lg transition-all duration-200 hover:scale-105 border-border hover:border-primary/30 bg-gradient-to-br from-yellow-50 to-orange-50 border-yellow-200">
                          <CardContent className="p-4">
                            <div className="flex items-center justify-between mb-2">
                              <span className="font-semibold text-foreground group-hover:text-primary transition-colors">
                                {item.keyword}
                              </span>
                              <Star className="h-4 w-4 text-yellow-500 fill-current" />
                            </div>
                            <Button
                              asChild
                              size="sm"
                              className="w-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white border-0 transition-all duration-200"
                            >
                              <a
                                href={generateTrendsUrl(item.keyword)}
                                target="_blank"
                                rel="nofollow noopener noreferrer"
                                className="flex items-center justify-center"
                              >
                                <TrendingUp className="h-3 w-3 mr-1" />
                                查看趋势
                              </a>
                            </Button>
                          </CardContent>
                        </Card>
                      ))}

                    {/* 然后显示普通词根 */}
                    {category.keywords
                      .filter(item => !item.isStarred)
                      .map((item, index) => (
                        <Card key={`normal-${index}`} className="group hover:shadow-lg transition-all duration-200 hover:scale-105 border-border hover:border-primary/30">
                          <CardContent className="p-4">
                            <div className="flex items-center justify-between mb-2">
                              <span className="font-semibold text-foreground group-hover:text-primary transition-colors">
                                {item.keyword}
                              </span>
                            </div>
                            <Button
                              asChild
                              size="sm"
                              className="w-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white border-0 transition-all duration-200"
                            >
                              <a
                                href={generateTrendsUrl(item.keyword)}
                                target="_blank"
                                rel="nofollow noopener noreferrer"
                                className="flex items-center justify-center"
                              >
                                <TrendingUp className="h-3 w-3 mr-1" />
                                查看趋势
                              </a>
                            </Button>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      </section>

      {/* 使用统一前台底部组件 */}
      <FrontendFooter />
    </div>
  )
}
