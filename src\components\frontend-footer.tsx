'use client'

import Script from 'next/script'
import { Sparkles, TrendingUp, Target, BarChart3, Search, Lightbulb } from 'lucide-react'

interface FrontendFooterProps {
  className?: string
}

export default function FrontendFooter({ className = '' }: FrontendFooterProps) {
  return (
    <footer className={`border-t bg-muted/30 py-12 mt-16 ${className}`}>
      <div className="container mx-auto px-4">
        {/* 主要内容区 */}
        <div className="text-center space-y-6">
          {/* 品牌标识 */}
          <div className="flex items-center justify-center space-x-3">
            <Sparkles className="h-8 w-8 text-primary sparkles-smooth sparkles-delay-2" />
            <span className="font-bold text-2xl">CatchIdeas</span>
          </div>

          {/* 简洁描述 */}
          <p className="text-muted-foreground max-w-2xl mx-auto">
            一站式关键词分析工具，助力产品经理和运营人员做出精准决策
          </p>

          {/* 核心功能标签 */}
          <div className="flex flex-wrap justify-center gap-3 max-w-3xl mx-auto">
            <span className="inline-flex items-center space-x-1 bg-primary/10 text-primary px-3 py-1.5 rounded-full text-sm">
              <Search className="h-3 w-3" />
              <span>词汇解析</span>
            </span>
            <span className="inline-flex items-center space-x-1 bg-primary/10 text-primary px-3 py-1.5 rounded-full text-sm">
              <Target className="h-3 w-3" />
              <span>行为分析</span>
            </span>
            <span className="inline-flex items-center space-x-1 bg-primary/10 text-primary px-3 py-1.5 rounded-full text-sm">
              <BarChart3 className="h-3 w-3" />
              <span>对手研究</span>
            </span>
            <span className="inline-flex items-center space-x-1 bg-primary/10 text-primary px-3 py-1.5 rounded-full text-sm">
              <TrendingUp className="h-3 w-3" />
              <span>数据监控</span>
            </span>
            <span className="inline-flex items-center space-x-1 bg-primary/10 text-primary px-3 py-1.5 rounded-full text-sm">
              <Lightbulb className="h-3 w-3" />
              <span>灵感启发</span>
            </span>
          </div>
        </div>

        {/* 版权信息 */}
        <div className="border-t mt-8 pt-6 text-center">
          <p className="text-sm text-muted-foreground">
            © 2025 CatchIdeas. 专业关键词分析工具平台
          </p>
        </div>
      </div>

      {/* Google Analytics */}
      <Script
        src="https://www.googletagmanager.com/gtag/js?id=G-XVHRW6P7QD"
        strategy="afterInteractive"
      />
      <Script id="google-analytics" strategy="afterInteractive">
        {`
          window.dataLayer = window.dataLayer || [];
          function gtag(){dataLayer.push(arguments);}
          gtag('js', new Date());
          gtag('config', 'G-XVHRW6P7QD');
        `}
      </Script>

      {/* 广告代码 */}
      <Script
        async
        data-cfasync="false"
        src="//pl27211408.profitableratecpm.com/dab3873d7045ab55c32b055616093330/invoke.js"
        strategy="afterInteractive"
      />
      <div id="container-dab3873d7045ab55c32b055616093330"></div>
    </footer>
  )
}
