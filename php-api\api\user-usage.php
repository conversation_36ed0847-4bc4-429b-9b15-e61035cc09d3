<?php
/**
 * 用户使用统计API
 * 记录和查询用户每日使用次数
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once '../config/database.php';

try {
    $database = new Database();
    $conn = $database->getConnection();
    
    $method = $_SERVER['REQUEST_METHOD'];
    
    switch ($method) {
        case 'GET':
            handleGetUsage($conn);
            break;
        case 'POST':
            handleRecordUsage($conn);
            break;
        default:
            http_response_code(405);
            echo json_encode([
                'success' => false,
                'error' => '不支持的请求方法'
            ]);
            break;
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => '服务器内部错误'
    ]);
}

/**
 * 获取用户使用统计
 */
function handleGetUsage($conn) {
    try {
        $email = $_GET['email'] ?? null;
        $date = $_GET['date'] ?? date('Y-m-d');
        
        if (!$email) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'error' => '缺少用户邮箱参数'
            ]);
            return;
        }

        // 查询用户当日使用统计
        $query = "SELECT * FROM user_usage_stats 
                 WHERE user_email = :email AND usage_date = :date";
        $stmt = $conn->prepare($query);
        $stmt->bindParam(':email', $email);
        $stmt->bindParam(':date', $date);
        $stmt->execute();

        $result = $stmt->fetch();

        if (!$result) {
            // 如果没有记录，返回默认值
            $result = [
                'user_email' => $email,
                'usage_date' => $date,
                'usage_count' => 0,
                'created_at' => null,
                'updated_at' => null
            ];
        }

        echo json_encode([
            'success' => true,
            'data' => $result
        ]);
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'error' => '查询失败'
        ]);
    }
}

/**
 * 记录用户使用次数
 */
function handleRecordUsage($conn) {
    try {
        $input = json_decode(file_get_contents('php://input'), true);
        $email = $input['email'] ?? null;
        $count = $input['count'] ?? 1;
        $date = $input['date'] ?? date('Y-m-d');

        if (!$email) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'error' => '缺少用户邮箱参数'
            ]);
            return;
        }

        // 使用INSERT ... ON DUPLICATE KEY UPDATE来处理记录不存在的情况
        $query = "INSERT INTO user_usage_stats (user_email, usage_date, usage_count)
                 VALUES (:email, :date, :count)
                 ON DUPLICATE KEY UPDATE
                 usage_count = usage_count + :count,
                 updated_at = CURRENT_TIMESTAMP";
        
        $stmt = $conn->prepare($query);
        $stmt->bindParam(':email', $email);
        $stmt->bindParam(':count', $count, PDO::PARAM_INT);
        $stmt->bindParam(':date', $date);
        $stmt->execute();
        
        // 获取更新后的记录
        $selectQuery = "SELECT * FROM user_usage_stats 
                       WHERE user_email = :email AND usage_date = :date";
        $selectStmt = $conn->prepare($selectQuery);
        $selectStmt->bindParam(':email', $email);
        $selectStmt->bindParam(':date', $date);
        $selectStmt->execute();
        
        $result = $selectStmt->fetch();
        
        echo json_encode([
            'success' => true,
            'message' => '使用次数记录成功',
            'data' => $result
        ]);
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'error' => '记录失败'
        ]);
    }
}
?>
