/**
 * 关键词列表API
 * 提供分页、搜索、分类筛选功能
 */

export default async function handler(req, res) {
  // 设置CORS头
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  if (req.method === 'GET') {
    return handleGetKeywords(req, res);
  }

  return res.status(405).json({
    success: false,
    error: '不支持的请求方法，请使用GET'
  });
}

/**
 * 处理获取关键词列表
 */
async function handleGetKeywords(req, res) {
  try {
    const {
      page = 1,
      limit = 20,
      search = '',
      category = '',
      analyzed_only = '',
      competition_level = '',
      date_range = '',
      sort_by = 'created_at',
      sort_order = 'desc'
    } = req.query;

    // 验证参数
    const pageNum = Math.max(1, parseInt(page) || 1);
    const limitNum = Math.min(100, Math.max(1, parseInt(limit) || 20));

    // 获取关键词列表

    // 构建查询参数
    const queryParams = new URLSearchParams({
      page: pageNum.toString(),
      limit: limitNum.toString()
    });

    if (search) queryParams.append('search', search);
    if (category) queryParams.append('category', category);
    if (analyzed_only) queryParams.append('analyzed_only', analyzed_only);
    if (competition_level) queryParams.append('competition_level', competition_level);
    if (date_range) queryParams.append('date_range', date_range);
    if (sort_by) queryParams.append('sort_by', sort_by);
    if (sort_order) queryParams.append('sort_order', sort_order);
    // 调用PHP API
    const apiUrl = `${process.env.NEXT_PUBLIC_PHP_API_URL}/keywords.php?${queryParams.toString()}`;

    const response = await fetch(apiUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`PHP API请求失败: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    // PHP API响应处理

    if (!data.success) {
      return res.status(500).json({
        success: false,
        error: data.message || 'PHP API返回错误',
        data: null
      });
    }

    // 处理关键词数据，添加计算字段
    const processedKeywords = (data.data.items || []).map(keyword => {
      const recommendedDomains = generateSimpleDomain(keyword.keyword);

      return {
        ...keyword,
        // 添加分析状态
        is_analyzed: !!(keyword.user_intent || keyword.user_pain_point || keyword.competition_level),
        // 添加竞争难度颜色
        competition_color: getCompetitionColor(keyword.competition_score),
        // 添加推荐域名
        recommended_domains: recommendedDomains,
        // 格式化日期
        created_at_formatted: formatDate(keyword.created_at),
        analyzed_at_formatted: keyword.analyzed_at ? formatDate(keyword.analyzed_at) : null
      };
    });

    // 确保使用正确的分页信息
    const paginationInfo = data.data.pagination;

    return res.status(200).json({
      success: true,
      data: {
        keywords: processedKeywords,
        pagination: paginationInfo || {
          current_page: pageNum,
          per_page: limitNum,
          total: 0, // 如果没有分页信息，设为0
          total_pages: 0, // 如果没有分页信息，设为0
          has_next: false,
          has_prev: false
        },
        filters: {
          search,
          category,
          analyzed_only,
          competition_level,
          date_range,
          sort_by,
          sort_order
        },
        statistics: data.data.statistics || {}
      },
      message: `成功获取 ${processedKeywords.length} 个关键词`
    });

  } catch (error) {
    console.error('获取关键词列表失败:', error);
    return res.status(500).json({
      success: false,
      error: '服务器内部错误: ' + error.message,
      data: null
    });
  }
}

/**
 * 获取竞争难度颜色
 */
function getCompetitionColor(score) {
  if (!score) return '#gray';

  const numScore = parseInt(score);
  if (numScore <= 3) return '#22c55e';    // 绿色 - 简单
  if (numScore <= 6) return '#eab308';    // 黄色 - 中等
  if (numScore <= 8) return '#f97316';    // 橙色 - 困难
  return '#ef4444';                       // 红色 - 非常困难
}

/**
 * 生成简单的推荐域名
 */
function generateSimpleDomain(keyword) {
  if (!keyword) return [];

  // 清理关键词：移除特殊字符，转小写，移除空格
  const cleanKeyword = keyword.toLowerCase()
    .replace(/[^a-z0-9\s]/g, '')
    .replace(/\s+/g, '');

  // 只返回一个域名：关键词.com
  const domain = `${cleanKeyword}.com`;

  return [{
    domain: domain,
    check_url: `https://wanwang.aliyun.com/domain/searchresult/?keyword=${cleanKeyword}`
  }];
}

/**
 * 格式化日期
 */
function formatDate(dateString) {
  if (!dateString) return null;

  try {
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  } catch {
    return dateString;
  }
}
