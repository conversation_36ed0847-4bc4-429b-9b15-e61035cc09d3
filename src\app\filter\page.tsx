'use client'

import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card'
import { Badge } from '../../components/ui/badge'
import { Button } from '../../components/ui/button'
import { Textarea } from '../../components/ui/textarea'
import { Alert, AlertDescription } from '../../components/ui/alert'
import { Switch } from '../../components/ui/switch'
import { Label } from '../../components/ui/label'
import {
  Filter,
  Copy,
  Download,
  Trash2,
  CheckCircle,
  XCircle,
  Info,
  Loader2,
  FileText,
  Target,
  Scissors,
  Eye,
  ArrowRight
} from 'lucide-react'
import FrontendHeader from '../../components/frontend-header'
import FrontendFooter from '../../components/frontend-footer'
import { Toaster, toast } from 'sonner'

interface FilterResult {
  accepted: string[]
  rejected: string[]
  duplicates: string[]
  extracted: string[] // 新增：提取的品牌名称
  stats: {
    total: number
    accepted: number
    rejected: number
    duplicates: number
    extracted: number // 新增：提取数量统计
  }
}

export default function FilterPage() {
  const [inputText, setInputText] = useState('')
  const [isProcessing, setIsProcessing] = useState(false)
  const [filterResult, setFilterResult] = useState<FilterResult | null>(null)
  const [enableBrandExtraction, setEnableBrandExtraction] = useState(false)
  const [showPreview, setShowPreview] = useState(false)

  // 品牌名称提取逻辑
  const extractBrandName = (text: string): string => {
    if (!text || !text.trim()) return text

    const originalText = text.trim()

    // 定义常见的分隔符，按优先级排序
    const separators = [
      ':', '：', // 冒号（英文和中文）
      ' - ', ' – ', ' — ', // 短横线（带空格）
      ' | ', ' ｜ ', // 竖线（带空格）
      ' • ', ' · ', // 圆点（带空格）
      ' / ', ' ／ ', // 斜杠（带空格）
      ' ~ ', ' ～ ', // 波浪号（带空格）
      ' > ', ' » ', // 箭头符号
      ' \\| ', // 转义的竖线
    ]

    // 尝试每个分隔符
    for (const separator of separators) {
      if (originalText.includes(separator)) {
        const parts = originalText.split(separator)
        if (parts.length >= 2 && parts[0].trim()) {
          let brandName = parts[0].trim()

          // 清理品牌名称
          brandName = cleanBrandName(brandName)

          // 验证提取的品牌名称是否有效
          if (isValidBrandName(brandName)) {
            return brandName
          }
        }
      }
    }

    // 如果没有找到分隔符，检查是否有其他模式
    // 例如：App Name (Description) -> App Name
    const parenthesesMatch = originalText.match(/^([^(]+)\s*\([^)]*\)/)
    if (parenthesesMatch && parenthesesMatch[1].trim()) {
      let brandName = parenthesesMatch[1].trim()
      brandName = cleanBrandName(brandName)
      if (isValidBrandName(brandName)) {
        return brandName
      }
    }

    // 例如：App Name [Description] -> App Name
    const bracketsMatch = originalText.match(/^([^[]+)\s*\[[^\]]*\]/)
    if (bracketsMatch && bracketsMatch[1].trim()) {
      let brandName = bracketsMatch[1].trim()
      brandName = cleanBrandName(brandName)
      if (isValidBrandName(brandName)) {
        return brandName
      }
    }

    return originalText // 如果无法提取，返回原文
  }

  // 清理品牌名称
  const cleanBrandName = (brandName: string): string => {
    return brandName
      .trim()
      .replace(/\s+/g, ' ') // 多个空格合并为一个
      .replace(/[""'']/g, '') // 移除引号
      .replace(/^\W+|\W+$/g, '') // 移除开头和结尾的非字母数字字符
      .trim()
  }

  // 验证品牌名称是否有效
  const isValidBrandName = (brandName: string): boolean => {
    if (!brandName || brandName.length < 2) return false
    if (brandName.length > 50) return false // 品牌名称不应该太长

    // 必须包含字母
    if (!/[a-zA-Z]/.test(brandName)) return false

    // 不应该是纯数字
    if (/^\d+$/.test(brandName)) return false

    // 不应该包含太多特殊字符
    const specialCharCount = (brandName.match(/[^\w\s]/g) || []).length
    if (specialCharCount > brandName.length * 0.3) return false

    return true
  }

  // 获取品牌提取预览
  const getBrandExtractionPreview = (): Array<{original: string, extracted: string}> => {
    if (!inputText.trim()) return []

    const lines = inputText.split('\n')
      .map(line => line.trim())
      .filter(line => line.length > 0)
      .slice(0, 10) // 只预览前10个

    return lines.map(line => ({
      original: line,
      extracted: extractBrandName(line)
    })).filter(item => item.original !== item.extracted) // 只显示有变化的
  }

  // 过滤关键词的核心逻辑
  const filterKeywords = (text: string): FilterResult => {
    if (!text.trim()) {
      return {
        accepted: [],
        rejected: [],
        duplicates: [],
        extracted: [],
        stats: { total: 0, accepted: 0, rejected: 0, duplicates: 0, extracted: 0 }
      }
    }

    // 解析输入文本，按行分割并清理
    let lines = text.split('\n')
      .map(line => line.trim())
      .filter(line => line.length > 0)

    // 如果启用品牌提取，先进行品牌名称提取
    const extracted: string[] = []
    if (enableBrandExtraction) {

      lines = lines.map(line => {
        const extractedBrand = extractBrandName(line)
        if (extractedBrand !== line) {
          extracted.push(`${line} → ${extractedBrand}`)
          return extractedBrand
        }
        return line
      })
    }

    const allKeywords = new Set<string>()
    const duplicates: string[] = []
    const uniqueKeywords: string[] = []

    // 去重处理
    lines.forEach(line => {
      if (allKeywords.has(line.toLowerCase())) {
        duplicates.push(line)
      } else {
        allKeywords.add(line.toLowerCase())
        uniqueKeywords.push(line)
      }
    })

    const accepted: string[] = []
    const rejected: string[] = []

    // 过滤逻辑
    uniqueKeywords.forEach(keyword => {
      const keywordText = keyword.trim()
      const words = keywordText.split(/\s+/).filter(word => word.length > 0)
      const wordCount = words.length

      // 1. 拒绝单个单词
      if (wordCount < 2) {
        rejected.push(keyword)
        return
      }

      // 1.1 拒绝空格数量不符合要求的关键词（最少1个空格，最多3个空格）
      const spaceCount = keywordText.split(' ').length - 1
      if (spaceCount < 1 || spaceCount > 3) {
        rejected.push(keyword)
        return
      }

      // 2. 拒绝过短或过长的关键词
      if (keywordText.length < 3 || keywordText.length > 100) {
        rejected.push(keyword)
        return
      }

      // 3. 拒绝纯数字或主要由数字组成的关键词
      if (/^\d+$/.test(keywordText) || /^\d+[\s\d]*\d+$/.test(keywordText)) {
        rejected.push(keyword)
        return
      }

      // 4. 拒绝包含中文字符的关键词
      if (/[\u4e00-\u9fff]/.test(keywordText)) {
        rejected.push(keyword)
        return
      }

      // 5. 拒绝包含其他非英文字符的关键词
      if (/[^\u0000-\u007F\s]/.test(keywordText)) {
        rejected.push(keyword)
        return
      }

      // 6. 拒绝包含特殊符号的关键词
      if (/[^\w\s-]/.test(keywordText)) {
        rejected.push(keyword)
        return
      }

      // 7. 拒绝没有英文字母的关键词
      if (!/[a-zA-Z]/.test(keywordText)) {
        rejected.push(keyword)
        return
      }

      // 8. 确保关键词主要由英文字母组成（至少70%）
      const englishLetterCount = (keywordText.match(/[a-zA-Z]/g) || []).length
      const totalCharCount = keywordText.replace(/\s/g, '').length
      if (totalCharCount > 0 && englishLetterCount / totalCharCount < 0.7) {
        rejected.push(keyword)
        return
      }

      // 9. 拒绝明显的品牌词（大写字母开头的单个词）
      const hasBrandPattern = words.some(word =>
        /^[A-Z][a-z]+$/.test(word) && word.length > 6
      )
      if (hasBrandPattern && wordCount === 1) {
        rejected.push(keyword)
        return
      }

      // 10. 拒绝违规相关内容
      const adultKeywords = [
        // 成人内容
        'porn', 'sex', 'adult', 'nude', 'xxx', 'escort', 'strip', 'cam', 'webcam',
        'erotic', 'fetish', 'bdsm', 'threesome', 'orgy', 'milf', 'teen', 'barely',
        'virgin', 'slut', 'whore', 'pussy', 'cock', 'dick', 'penis', 'vagina',
        'masturbate', 'orgasm', 'climax', 'intimate', 'seductive', 'horny',

        // 赌博相关
        'casino', 'gambling', 'bet', 'poker', 'slots', 'blackjack', 'roulette',
        'lottery', 'jackpot', 'win money', 'easy money', 'bingo', 'scratch',
        'odds', 'wager', 'stake', 'gamble', 'betting', 'casino online',

        // 毒品相关
        'drug', 'weed', 'marijuana', 'cannabis', 'cocaine', 'heroin', 'meth',
        'ecstasy', 'lsd', 'mdma', 'crack', 'crystal', 'pills', 'substance',
        'narcotic', 'opioid', 'stimulant', 'psychedelic', 'ketamine',

        // 暴力相关
        'violence', 'murder', 'kill', 'death', 'suicide', 'bomb', 'weapon',
        'gun', 'knife', 'blood', 'torture', 'abuse', 'assault', 'revenge',

        // 欺诈相关
        'scam', 'fraud', 'fake', 'counterfeit', 'pyramid', 'ponzi', 'mlm',
        'get rich quick', 'easy profit', 'guaranteed return', 'investment scam',

        // 其他违规内容
        'hack', 'crack', 'piracy', 'illegal', 'stolen', 'bootleg', 'torrent',
        'yellow', 'spinach', 'plant', 'fantasy', 'illusion', 'magic'
      ]
      const hasAdultContent = adultKeywords.some(adult =>
        keywordText.toLowerCase().includes(adult)
      )
      if (hasAdultContent) {
        rejected.push(keyword)
        return
      }

      // 11. 拒绝包含网址的关键词
      // 更完整的顶级域名正则表达式
      const completeTLDRegex = /\.(com|net|org|edu|gov|mil|int|co|uk|de|fr|jp|cn|ru|br|au|ca|in|it|nl|es|se|no|dk|fi|pl|cz|hu|ro|bg|hr|si|sk|ee|lv|lt|gr|pt|ie|lu|mt|cy|be|at|ch|li|is|fo|ad|sm|va|mc|gg|je|im|gi|fk|sh|ac|io|ai|as|aq|ag|ar|am|aw|az|bs|bh|bd|bb|by|bz|bj|bm|bt|bo|ba|bw|bv|br|io|bn|bg|bf|bi|kh|cm|cv|ky|cf|td|cl|cx|cc|co|km|cg|cd|ck|cr|ci|hr|cu|cy|cz|dk|dj|dm|do|ec|eg|sv|gq|er|ee|et|fk|fo|fj|fi|fr|gf|pf|tf|ga|gm|ge|de|gh|gi|gr|gl|gd|gp|gu|gt|gn|gw|gy|ht|hm|va|hn|hk|hu|is|in|id|ir|iq|ie|il|it|jm|jp|jo|kz|ke|ki|kp|kr|kw|kg|la|lv|lb|ls|lr|ly|li|lt|lu|mo|mk|mg|mw|my|mv|ml|mt|mh|mq|mr|mu|yt|mx|fm|md|mc|mn|ms|ma|mz|mm|na|nr|np|nl|an|nc|nz|ni|ne|ng|nu|nf|mp|no|om|pk|pw|ps|pa|pg|py|pe|ph|pn|pl|pt|pr|qa|re|ro|ru|rw|sh|kn|lc|pm|vc|ws|sm|st|sa|sn|sc|sl|sg|sk|si|sb|so|za|gs|es|lk|sd|sr|sj|sz|se|ch|sy|tw|tj|tz|th|tl|tg|tk|to|tt|tn|tr|tm|tc|tv|ug|ua|ae|gb|us|um|uy|uz|vu|ve|vn|vg|vi|wf|eh|ye|zm|zw|aaa|aarp|abarth|abb|abbott|abbvie|abc|able|abogado|abudhabi|academy|accenture|accountant|accountants|aco|actor|ads|adult|aeg|aero|aetna|afamilycompany|afl|africa|agakhan|agency|aig|aigo|airbus|airforce|airtel|akdn|alfaromeo|alibaba|alipay|allfinanz|allstate|ally|alsace|alstom|amazon|americanexpress|americanfamily|amex|amfam|amica|amsterdam|analytics|android|anquan|anz|aol|apartments|app|apple|aquarelle|arab|aramco|archi|army|art|arte|asda|associates|athleta|attorney|auction|audi|audible|audio|auspost|author|auto|autos|avianca|aws|axa|azure|baby|baidu|banamex|bananarepublic|band|bank|bar|barcelona|barclaycard|barclays|barefoot|bargains|baseball|basketball|bauhaus|bayern|bbc|bbt|bbva|bcg|bcn|beats|beauty|beer|bentley|berlin|best|bestbuy|bet|bharti|bible|bid|bike|bing|bingo|bio|black|blackfriday|blockbuster|blog|bloomberg|blue|bms|bmw|bnpparibas|boats|boehringer|bofa|bom|bond|boo|book|booking|bosch|bostik|boston|bot|boutique|box|bradesco|bridgestone|broadway|broker|brother|brussels|budapest|bugatti|build|builders|business|buy|buzz|bzh|cab|cafe|cal|call|calvinklein|cam|camera|camp|cancerresearch|canon|capetown|capital|capitalone|car|caravan|cards|care|career|careers|cars|casa|case|caseih|cash|casino|catering|catholic|cba|cbn|cbre|cbs|ceb|center|ceo|cern|cfa|cfd|chanel|channel|charity|chase|chat|cheap|chintai|christmas|chrome|church|cipriani|circle|cisco|citadel|citi|citic|city|cityeats|claims|cleaning|click|clinic|clinique|clothing|cloud|club|clubmed|coach|codes|coffee|college|cologne|comcast|commbank|community|company|compare|computer|comsec|condos|construction|consulting|contact|contractors|cooking|cookingchannel|cool|corsica|country|coupon|coupons|courses|cpa|credit|creditcard|creditunion|cricket|crown|crs|cruise|cruises|csc|cuisinella|cymru|cyou|dabur|dad|dance|data|date|dating|datsun|day|dclk|dds|deal|dealer|deals|degree|delivery|dell|deloitte|delta|democrat|dental|dentist|desi|design|dev|dhl|diamonds|diet|digital|direct|directory|discount|discover|dish|diy|dnp|docs|doctor|dog|domains|dot|download|drive|dtv|dubai|duck|dunlop|dupont|durban|dvag|dvr|earth|eat|eco|edeka|email|emerck|energy|engineer|engineering|enterprises|epson|equipment|ericsson|erni|esq|estate|esurance|etisalat|eurovision|eus|events|exchange|expert|exposed|express|extraspace|fage|fail|fairwinds|faith|family|fan|fans|farm|farmers|fashion|fast|fedex|feedback|ferrari|ferrero|fiat|fidelity|fido|film|final|finance|financial|fire|firestone|firmdale|fish|fishing|fit|fitness|flickr|flights|flir|florist|flowers|fly|foo|food|foodnetwork|football|ford|forex|forsale|forum|foundation|fox|free|fresenius|frl|frogans|frontdoor|frontier|ftr|fujitsu|fujixerox|fun|fund|furniture|futbol|fyi|gal|gallery|gallo|gallup|game|games|gap|garden|gay|gbiz|gdn|gea|gent|genting|george|ggee|gift|gifts|gives|giving|glade|glass|gle|global|globo|gmail|gmbh|gmo|gmx|godaddy|gold|goldpoint|golf|goo|goodyear|goog|google|gop|got|grainger|graphics|gratis|green|gripe|grocery|group|guardian|gucci|guge|guide|guitars|guru|hair|hamburg|hangout|haus|hbo|hdfc|hdfcbank|health|healthcare|help|helsinki|here|hermes|hgtv|hiphop|hisamitsu|hitachi|hiv|hkt|hockey|holdings|holiday|homedepot|homegoods|homes|homesense|honda|horse|hospital|host|hosting|hot|hoteles|hotels|hotmail|house|how|hsbc|hughes|hyatt|hyundai|ibm|icbc|ice|icu|ieee|ifm|ikano|imamat|imdb|immo|immobilien|inc|industries|infiniti|info|ing|ink|institute|insurance|insure|intel|international|intuit|investments|ipiranga|irish|ismaili|ist|istanbul|itau|itv|iveco|jaguar|java|jcb|jcp|jeep|jetzt|jewelry|jio|jll|jmp|jnj|jobs|joburg|jot|joy|jpmorgan|jprs|juegos|juniper|kaufen|kddi|kerryhotels|kerrylogistics|kerryproperties|kfh|kia|kim|kinder|kindle|kitchen|kiwi|koeln|komatsu|kosher|kpmg|kpn|krd|kred|kuokgroup|kyoto|lacaixa|lamborghini|lamer|lancaster|lancia|land|landrover|lanxess|lasalle|lat|latino|latrobe|law|lawyer|lds|lease|leclerc|lefrak|legal|lego|lexus|lgbt|liaison|lidl|life|lifeinsurance|lifestyle|lighting|like|lilly|limited|limo|lincoln|linde|link|lipsy|live|living|lixil|llc|llp|loan|loans|locker|locus|loft|lol|london|lotte|lotto|love|lpl|lplfinancial|ltd|ltda|lundbeck|lupin|luxe|luxury|macys|madrid|maif|maison|makeup|man|management|mango|map|market|marketing|markets|marriott|marshalls|maserati|mattel|mba|mckinsey|med|media|meet|melbourne|meme|memorial|men|menu|merckmsd|metlife|miami|microsoft|mini|mint|mit|mitsubishi|mlb|mls|mma|mobile|moda|moe|moi|mom|monash|money|monster|mormon|mortgage|moscow|moto|motorcycles|mov|movie|msd|mtn|mtr|music|mutual|nab|nagoya|name|nationwide|natura|navy|nba|nec|netbank|netflix|network|neustar|new|newholland|news|next|nextdirect|nexus|nfl|ngo|nhk|nico|nike|nikon|ninja|nissan|nissay|nokia|northwesternmutual|norton|now|nowruz|nowtv|nra|nrw|ntt|nyc|obi|observer|off|office|okinawa|olayan|olayangroup|oldnavy|ollo|omega|one|ong|onl|online|onyourside|ooo|open|oracle|orange|organic|origins|osaka|otsuka|ott|ovh|page|panasonic|paris|pars|partners|parts|party|passagens|pay|pccw|pet|pfizer|pharmacy|phd|philips|phone|photo|photography|photos|physio|pics|pictet|pictures|pid|pin|ping|pink|pioneer|pizza|place|play|playstation|plumbing|plus|pnc|pohl|poker|politie|porn|pramerica|praxi|press|prime|prod|productions|prof|progressive|promo|properties|property|protection|pru|prudential|pub|pwc|qpon|quebec|quest|qvc|racing|radio|raid|read|realestate|realtor|realty|recipes|red|redstone|redumbrella|rehab|reise|reisen|reit|reliance|ren|rent|rentals|repair|report|republican|rest|restaurant|review|reviews|rexroth|rich|richardli|ricoh|rightathome|ril|rio|rip|rmit|rocher|rocks|rodeo|rogers|room|rsvp|rugby|ruhr|run|rwe|ryukyu|saarland|safe|safety|sakura|sale|salon|samsclub|samsung|sandvik|sandvikcoromant|sanofi|sap|sarl|sas|save|saxo|sbi|sbs|sca|scb|schaeffler|schmidt|scholarships|school|schule|schwarz|science|scjohnson|scor|scot|search|seat|secure|security|seek|select|sener|services|ses|seven|sew|sex|sexy|sfr|shangrila|sharp|shaw|shell|shia|shiksha|shoes|shop|shopping|shouji|show|showtime|shriram|silk|sina|singles|site|ski|skin|sky|skype|sling|smart|smile|sncf|soccer|social|softbank|software|sohu|solar|solutions|song|sony|soy|spa|space|sport|spot|spreadbetting|srl|stada|staples|star|statebank|statefarm|stc|stcgroup|stockholm|storage|store|stream|studio|study|style|sucks|supplies|supply|support|surf|surgery|suzuki|swatch|swiftcover|swiss|sydney|symantec|systems|tab|taipei|talk|taobao|target|tatamotors|tatar|tattoo|tax|taxi|tci|tdk|team|tech|technology|tel|temasek|tennis|teva|thd|theater|theatre|tiaa|tickets|tienda|tiffany|tips|tires|tirol|tjmaxx|tjx|tkmaxx|tmall|today|tokyo|tools|top|toray|toshiba|total|tours|town|toyota|toys|trade|trading|training|travel|travelchannel|travelers|travelersinsurance|trust)\b/i
      if (completeTLDRegex.test(keywordText)) {
        rejected.push(keyword)
        return
      }

      // 12. 拒绝常见的大产品/平台名称
      const bigBrands = [
        // 科技巨头
        'google', 'facebook', 'amazon', 'apple', 'microsoft', 'netflix',
        'meta', 'alphabet', 'tesla', 'nvidia', 'intel', 'amd', 'oracle',
        'salesforce', 'adobe', 'zoom', 'slack', 'dropbox', 'shopify',

        // 社交媒体
        'youtube', 'instagram', 'twitter', 'linkedin', 'tiktok', 'snapchat',
        'pinterest', 'reddit', 'discord', 'telegram', 'whatsapp', 'wechat',
        'line', 'viber', 'skype', 'clubhouse', 'twitch', 'onlyfans',

        // 音乐/娱乐
        'spotify', 'pandora', 'soundcloud', 'itunes', 'disney', 'hulu',
        'hbo', 'paramount', 'peacock', 'discovery', 'warner', 'universal',
        'sony music', 'columbia', 'atlantic', 'capitol',

        // 电商/服务
        'uber', 'lyft', 'airbnb', 'booking', 'expedia', 'tripadvisor',
        'doordash', 'grubhub', 'postmates', 'instacart', 'walmart',
        'target', 'costco', 'bestbuy', 'homedepot', 'lowes',

        // 品牌/制造商
        'samsung', 'sony', 'lg', 'panasonic', 'toshiba', 'canon', 'nikon',
        'hp', 'dell', 'lenovo', 'asus', 'acer', 'msi', 'razer',

        // 服装/时尚
        'nike', 'adidas', 'puma', 'reebok', 'under armour', 'lululemon',
        'zara', 'h&m', 'uniqlo', 'gap', 'forever21', 'urban outfitters',

        // 汽车品牌
        'toyota', 'honda', 'ford', 'chevrolet', 'bmw', 'mercedes', 'audi',
        'volkswagen', 'hyundai', 'kia', 'nissan', 'mazda', 'subaru',
        'lexus', 'infiniti', 'acura', 'cadillac', 'lincoln', 'jeep',

        // 金融服务
        'paypal', 'stripe', 'square', 'venmo', 'cashapp', 'zelle',
        'mastercard', 'visa', 'amex', 'discover', 'jpmorgan', 'wells fargo',
        'bank of america', 'citibank', 'chase', 'goldman sachs',

        // 云服务/企业软件
        'aws', 'azure', 'gcp', 'cloudflare', 'digitalocean', 'linode',
        'heroku', 'vercel', 'netlify', 'github', 'gitlab', 'bitbucket',
        'jira', 'confluence', 'notion', 'airtable', 'monday', 'asana',

        // 游戏/娱乐
        'steam', 'epic games', 'blizzard', 'activision', 'ubisoft', 'ea',
        'rockstar', 'bethesda', 'nintendo', 'playstation', 'xbox',
        'riot games', 'valve', 'mojang', 'roblox', 'fortnite',

        // 单个单词
        'msft'
      ]
      const hasBigBrand = bigBrands.some(brand =>
        keywordText.toLowerCase().includes(brand)
      )
      if (hasBigBrand) {
        rejected.push(keyword)
        return
      }

      // 通过所有过滤条件的关键词
      accepted.push(keyword)
    })

    return {
      accepted,
      rejected,
      duplicates,
      extracted,
      stats: {
        total: lines.length,
        accepted: accepted.length,
        rejected: rejected.length,
        duplicates: duplicates.length,
        extracted: extracted.length
      }
    }
  }

  // 处理过滤
  const handleFilter = async () => {
    if (!inputText.trim()) {
      toast.error('请输入要过滤的关键词')
      return
    }

    setIsProcessing(true)
    
    try {
      // 模拟处理时间
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      const result = filterKeywords(inputText)
      setFilterResult(result)
      
      toast.success(`过滤完成！获得 ${result.stats.accepted} 个有效关键词`)
    } catch {
      toast.error('过滤过程出错，请重试')
    } finally {
      setIsProcessing(false)
    }
  }

  // 复制结果
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text).then(() => {
      toast.success(`复制成功！${filterResult?.accepted.length || 0}个关键词已复制到剪贴板`)
    }).catch(() => {
      toast.error('复制失败，请重试')
    })
  }

  // 导出CSV
  const exportCSV = () => {
    if (!filterResult || filterResult.accepted.length === 0) {
      toast.error('没有可导出的数据')
      return
    }

    const csvContent = [
      'Category: All categories',
      'TOP',
      ...filterResult.accepted
    ].join('\n')

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', `filtered_keywords_${new Date().getTime()}.csv`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    toast.success(`CSV导出成功！${filterResult.accepted.length}个关键词已保存`)
  }

  // 导出TXT
  const exportTXT = () => {
    if (!filterResult || filterResult.accepted.length === 0) {
      toast.error('没有可导出的数据')
      return
    }

    const txtContent = filterResult.accepted.join('\n')
    const blob = new Blob([txtContent], { type: 'text/plain;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', `filtered_keywords_${new Date().getTime()}.txt`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    toast.success(`TXT导出成功！${filterResult.accepted.length}个关键词已保存`)
  }

  // 清空所有数据
  const clearAll = () => {
    setInputText('')
    setFilterResult(null)
    toast.success('已清空所有数据')
  }

  return (
    <div className="min-h-screen bg-background">
      {/* 使用统一前台顶部组件 */}
      <FrontendHeader />

      {/* 主要内容区域 - 固定布局 */}
      <section className="py-4 px-4">
        <div className="container mx-auto">
          <div className="max-w-6xl mx-auto">

            {/* 输入区域 */}
            <div className="mb-6">
              <Card className="idea-card-enhanced">
                <CardHeader className="pb-3">
                  <CardTitle className="flex items-center space-x-2 text-lg">
                    <FileText className="h-5 w-5 text-primary" />
                    <span>关键词过滤</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="relative">
                    <Textarea
                      placeholder="请粘贴关键词列表，每行一个关键词"
                      value={inputText}
                      onChange={(e) => setInputText(e.target.value)}
                      className="!min-h-[120px] !max-h-80 !h-auto text-base resize-none overflow-y-auto border-2 focus:border-primary/50 [field-sizing:initial]"
                      disabled={isProcessing}
                      style={{
                        scrollbarWidth: 'thin',
                        scrollbarColor: 'rgb(203 213 225) transparent',
                        height: 'auto',
                        minHeight: '120px',
                        maxHeight: '320px'
                      }}
                    />
                  </div>

                  {/* 品牌提取选项 */}
                  <Card className="data-card-enhanced border-primary/20 bg-gradient-to-r from-primary/5 to-purple-500/5">
                    <CardContent className="p-4">
                      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
                        <div className="flex items-center space-x-4">
                          <div className="flex items-center justify-center w-10 h-10 rounded-full bg-primary/10 border border-primary/20">
                            <Scissors className="h-5 w-5 text-primary" />
                          </div>
                          <div className="flex flex-col">
                            <div className="flex items-center space-x-3">
                              <Label htmlFor="brand-extraction" className="text-sm font-semibold text-foreground cursor-pointer">
                                智能品牌提取
                              </Label>
                              <Switch
                                id="brand-extraction"
                                checked={enableBrandExtraction}
                                onCheckedChange={setEnableBrandExtraction}
                                disabled={isProcessing}
                              />
                            </div>
                            <p className="text-xs text-muted-foreground mt-1">
                              自动提取 &quot;品牌名: 描述&quot; 中的品牌名称，支持多种分隔符
                            </p>
                          </div>
                        </div>

                        {enableBrandExtraction && inputText.trim() && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setShowPreview(!showPreview)}
                            disabled={isProcessing}
                            className="bg-white/80 backdrop-blur-sm border-primary/30 text-primary hover:bg-primary/10 hover:text-primary hover:border-primary/50 transition-all duration-200"
                          >
                            <Eye className="h-4 w-4 mr-1" />
                            {showPreview ? '隐藏预览' : '预览提取'}
                          </Button>
                        )}
                      </div>
                    </CardContent>
                  </Card>

                  {/* 品牌提取预览 */}
                  {enableBrandExtraction && showPreview && inputText.trim() && (
                    <Card className="data-card-enhanced border-primary/20 bg-primary/5">
                      <CardHeader className="pb-3">
                        <CardTitle className="text-sm font-medium text-primary flex items-center">
                          <Eye className="h-4 w-4 mr-2" />
                          品牌提取预览（前10个有变化的）
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="pt-0">
                        <div className="space-y-3 max-h-48 overflow-y-auto">
                          {getBrandExtractionPreview().map((item, index) => (
                            <div key={index} className="flex items-center space-x-3 p-2 bg-white/60 rounded-md border border-primary/10">
                              <span className="text-muted-foreground text-xs truncate flex-1 font-mono">{item.original}</span>
                              <ArrowRight className="h-3 w-3 text-primary flex-shrink-0" />
                              <span className="text-primary font-medium text-xs truncate flex-1 font-mono">{item.extracted}</span>
                            </div>
                          ))}
                          {getBrandExtractionPreview().length === 0 && (
                            <div className="text-center py-4">
                              <p className="text-xs text-muted-foreground italic">没有检测到可提取的品牌名称</p>
                            </div>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  )}

                  <div className="flex items-center justify-between flex-wrap gap-3">
                    <div className="text-sm text-muted-foreground">
                      已输入 {inputText.split('\n').filter(line => line.trim()).length} 行关键词
                      {enableBrandExtraction && (
                        <span className="ml-2 text-blue-600">
                          (启用品牌提取)
                        </span>
                      )}
                    </div>

                    <div className="flex space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={clearAll}
                        disabled={isProcessing || !inputText.trim()}
                        className="bg-white/80 backdrop-blur-sm border-gray-200 text-gray-700 hover:bg-red-50 hover:text-red-600 hover:border-red-300"
                      >
                        <Trash2 className="h-4 w-4 mr-1" />
                        清空
                      </Button>

                      <Button
                        size="sm"
                        onClick={handleFilter}
                        disabled={isProcessing || !inputText.trim()}
                        className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white border-0"
                      >
                        {isProcessing ? (
                          <>
                            <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                            处理中...
                          </>
                        ) : (
                          <>
                            <Filter className="h-4 w-4 mr-1" />
                            开始过滤
                          </>
                        )}
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* 过滤规则说明 */}
              <Alert className="border-primary/30 bg-card border-2 mt-4">
                <Info className="h-4 w-4 text-primary" />
                <AlertDescription className="text-foreground text-sm">
                  <strong>过滤规则：</strong>自动排除单个单词、中文内容、特殊符号、乱码、违规内容、大品牌名称等，只保留2-4个单词的英文长尾关键词。
                  {enableBrandExtraction && (
                    <span className="block mt-2 text-blue-600">
                      <strong>品牌提取：</strong>自动识别并提取 &quot;品牌名: 描述&quot;、&quot;品牌名 - 描述&quot;、&quot;品牌名 | 描述&quot; 等格式中的品牌名称。
                    </span>
                  )}
                </AlertDescription>
              </Alert>
            </div>



            {/* 结果展示区域 */}
            {filterResult && (
              <div className="mt-6 space-y-6">
                {/* 统计卡片 */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <Card className="data-card-enhanced stats-card-info">
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                      <CardTitle className="text-sm font-medium text-info/80">总数量</CardTitle>
                      <FileText className="h-5 w-5 text-info" />
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">{filterResult.stats.total}</div>
                      <p className="text-xs text-muted-foreground">输入关键词</p>
                    </CardContent>
                  </Card>

                  <Card className="data-card-enhanced stats-card-success">
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                      <CardTitle className="text-sm font-medium text-success/80">通过</CardTitle>
                      <CheckCircle className="h-5 w-5 text-success" />
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">{filterResult.stats.accepted}</div>
                      <p className="text-xs text-muted-foreground">有效关键词</p>
                    </CardContent>
                  </Card>

                  <Card className="data-card-enhanced stats-card-warning">
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                      <CardTitle className="text-sm font-medium text-warning/80">拒绝</CardTitle>
                      <XCircle className="h-5 w-5 text-warning" />
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">{filterResult.stats.rejected}</div>
                      <p className="text-xs text-muted-foreground">无效关键词</p>
                    </CardContent>
                  </Card>

                  <Card className="data-card-enhanced stats-card-info">
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                      <CardTitle className="text-sm font-medium text-info/80">重复</CardTitle>
                      <Target className="h-5 w-5 text-info" />
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">{filterResult.stats.duplicates}</div>
                      <p className="text-xs text-muted-foreground">重复关键词</p>
                    </CardContent>
                  </Card>

                </div>

                {/* 主要内容区域 */}
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  {/* 通过的关键词 */}
                  <Card className="lg:col-span-2">
                  <CardHeader>
                    <CardTitle className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
                      <div className="flex items-center space-x-2">
                        <CheckCircle className="h-5 w-5 text-success" />
                        <span>通过过滤 ({filterResult.stats.accepted})</span>
                      </div>
                      <div className="flex flex-wrap gap-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => copyToClipboard(filterResult.accepted.join('\n'))}
                          disabled={filterResult.accepted.length === 0}
                          className="bg-white/80 backdrop-blur-sm border-gray-200 text-gray-700 hover:bg-blue-50 hover:text-blue-600 hover:border-blue-300 flex-1 sm:flex-none"
                        >
                          <Copy className="h-4 w-4 mr-1" />
                          <span className="hidden xs:inline">复制</span>
                          <span className="xs:hidden">复制</span>
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={exportCSV}
                          disabled={filterResult.accepted.length === 0}
                          className="bg-white/80 backdrop-blur-sm border-gray-200 text-gray-700 hover:bg-green-50 hover:text-green-600 hover:border-green-300 flex-1 sm:flex-none"
                        >
                          <Download className="h-4 w-4 mr-1" />
                          <span className="hidden xs:inline">CSV</span>
                          <span className="xs:hidden">CSV</span>
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={exportTXT}
                          disabled={filterResult.accepted.length === 0}
                          className="bg-white/80 backdrop-blur-sm border-gray-200 text-gray-700 hover:bg-purple-50 hover:text-purple-600 hover:border-purple-300 flex-1 sm:flex-none"
                        >
                          <Download className="h-4 w-4 mr-1" />
                          <span className="hidden xs:inline">TXT</span>
                          <span className="xs:hidden">TXT</span>
                        </Button>
                      </div>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {filterResult.accepted.length > 0 ? (
                      <div className="max-h-96 overflow-y-auto space-y-2">
                        {filterResult.accepted.map((keyword, index) => (
                          <div key={index} className="p-3 bg-success/10 border border-success/20 rounded-lg">
                            <span className="text-sm font-medium text-success-foreground">{keyword}</span>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-8 text-muted-foreground">
                        没有通过过滤的关键词
                      </div>
                    )}
                  </CardContent>
                </Card>

                  {/* 右侧区域 */}
                  <div className="space-y-6">
                    {/* 被拒绝的关键词 */}
                    <Card>
                      <CardHeader>
                        <CardTitle className="flex items-center space-x-2">
                          <XCircle className="h-5 w-5 text-destructive" />
                          <span>被过滤 ({filterResult.stats.rejected + filterResult.stats.duplicates})</span>
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        {(filterResult.rejected.length > 0 || filterResult.duplicates.length > 0) ? (
                          <div className="max-h-96 overflow-y-auto space-y-2">
                            {/* 被拒绝的关键词 */}
                            {filterResult.rejected.map((keyword, index) => (
                              <div key={`rejected-${index}`} className="p-3 bg-destructive/10 border border-destructive/20 rounded-lg">
                                <div className="flex items-center justify-between">
                                  <span className="text-sm text-destructive-foreground">{keyword}</span>
                                  <Badge variant="destructive" className="text-xs">
                                    无效
                                  </Badge>
                                </div>
                              </div>
                            ))}

                            {/* 重复的关键词 */}
                            {filterResult.duplicates.map((keyword, index) => (
                              <div key={`duplicate-${index}`} className="p-3 bg-warning/10 border border-warning/20 rounded-lg">
                                <div className="flex items-center justify-between">
                                  <span className="text-sm text-warning-foreground">{keyword}</span>
                                  <Badge variant="outline" className="text-xs border-warning text-warning">
                                    重复
                                  </Badge>
                                </div>
                              </div>
                            ))}
                          </div>
                        ) : (
                          <div className="text-center py-8 text-muted-foreground">
                            没有被过滤的关键词
                          </div>
                        )}
                      </CardContent>
                    </Card>

                    {/* 品牌提取结果 */}
                    {enableBrandExtraction && filterResult.stats.extracted > 0 && (
                      <Card className="data-card-enhanced border-primary/20">
                        <CardHeader>
                          <CardTitle className="flex items-center space-x-2">
                            <Scissors className="h-5 w-5 text-primary" />
                            <span>品牌提取 ({filterResult.stats.extracted})</span>
                          </CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="max-h-96 overflow-y-auto space-y-3">
                            {filterResult.extracted.map((extraction, index) => (
                              <div key={index} className="p-3 bg-primary/10 border border-primary/20 rounded-lg">
                                <div className="flex items-center space-x-3 text-sm">
                                  <span className="text-muted-foreground truncate flex-1 font-mono">{extraction.split(' → ')[0]}</span>
                                  <ArrowRight className="h-3 w-3 text-primary flex-shrink-0" />
                                  <span className="text-primary font-medium truncate flex-1 font-mono">{extraction.split(' → ')[1]}</span>
                                </div>
                              </div>
                            ))}
                          </div>
                        </CardContent>
                      </Card>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* 使用说明 */}
            {!filterResult && (
              <Card className="idea-card-enhanced">
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Info className="h-6 w-6 text-primary" />
                    <span>使用说明</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h4 className="font-semibold text-foreground mb-3">过滤标准</h4>
                      <ul className="space-y-2 text-sm text-muted-foreground">
                        <li className="flex items-start space-x-2">
                          <span className="text-destructive">✗</span>
                          <span>单个单词</span>
                        </li>
                        <li className="flex items-start space-x-2">
                          <span className="text-destructive">✗</span>
                          <span>空格数量不符合要求</span>
                        </li>
                        <li className="flex items-start space-x-2">
                          <span className="text-destructive">✗</span>
                          <span>中文内容和非英文字符</span>
                        </li>
                        <li className="flex items-start space-x-2">
                          <span className="text-destructive">✗</span>
                          <span>特殊符号、乱码和纯数字</span>
                        </li>
                        <li className="flex items-start space-x-2">
                          <span className="text-destructive">✗</span>
                          <span>违规相关内容</span>
                        </li>
                        <li className="flex items-start space-x-2">
                          <span className="text-destructive">✗</span>
                          <span>网址和大品牌名称</span>
                        </li>
                        <li className="flex items-start space-x-2">
                          <span className="text-success">✓</span>
                          <span>英文长尾关键词</span>
                        </li>
                      </ul>
                    </div>

                    <div>
                      <h4 className="font-semibold text-foreground mb-3">智能功能</h4>
                      <ul className="space-y-2 text-sm text-muted-foreground">
                        <li className="flex items-start space-x-2">
                          <span className="text-primary">•</span>
                          <span>品牌提取：自动提取 &quot;Peacock TV: Stream TV&quot; → &quot;Peacock TV&quot;</span>
                        </li>
                        <li className="flex items-start space-x-2">
                          <span className="text-primary">•</span>
                          <span>支持分隔符：冒号(:)、短横线(-)、竖线(|)等</span>
                        </li>
                        <li className="flex items-start space-x-2">
                          <span className="text-primary">•</span>
                          <span>实时预览：查看提取效果再决定是否启用</span>
                        </li>
                        <li className="flex items-start space-x-2">
                          <span className="text-primary">•</span>
                          <span>智能清理：自动去除多余空格和特殊字符</span>
                        </li>
                      </ul>
                    </div>

                    <div>
                      <h4 className="font-semibold text-foreground mb-3">导出功能</h4>
                      <ul className="space-y-2 text-sm text-muted-foreground">
                        <li className="flex items-start space-x-2">
                          <span className="text-primary">•</span>
                          <span>CSV格式：兼容后台导入（从第3行开始）</span>
                        </li>
                        <li className="flex items-start space-x-2">
                          <span className="text-primary">•</span>
                          <span>TXT格式：纯文本，每行一个关键词</span>
                        </li>
                        <li className="flex items-start space-x-2">
                          <span className="text-primary">•</span>
                          <span>一键复制：快速复制到剪贴板</span>
                        </li>
                        <li className="flex items-start space-x-2">
                          <span className="text-primary">•</span>
                          <span>自动去重：避免重复关键词</span>
                        </li>
                      </ul>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </section>

      {/* 使用统一前台底部组件 */}
      <FrontendFooter />

      {/* Toast通知组件 */}
      <Toaster position="top-right" richColors closeButton duration={5000} />
    </div>
  )
}
