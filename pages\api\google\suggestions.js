/**
 * Google搜索建议API
 * 提供关键词搜索建议功能
 */

const googleAPI = require('../../../lib/google-api');

export default async function handler(req, res) {
  // 设置CORS头
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  if (req.method === 'GET') {
    return handleGetSuggestions(req, res);
  }

  if (req.method === 'POST') {
    return handleBatchSuggestions(req, res);
  }

  return res.status(405).json({
    success: false,
    error: '不支持的请求方法'
  });
}

/**
 * 处理单个关键词的搜索建议
 */
async function handleGetSuggestions(req, res) {
  try {
    const { q: query, limit = 10 } = req.query;

    if (!query) {
      return res.status(400).json({
        success: false,
        error: '缺少查询参数 q'
      });
    }

    const maxSuggestions = Math.min(parseInt(limit) || 10, 20); // 最多20个建议

    // 获取关键词搜索建议

    const result = await googleAPI.getSuggestions(query, maxSuggestions);

    if (result.success) {
      return res.status(200).json({
        success: true,
        data: {
          query: query,
          suggestions: result.suggestions,
          total: result.total,
          source: result.source
        },
        message: `成功获取 ${result.total} 个搜索建议`
      });
    } else {
      return res.status(500).json({
        success: false,
        error: result.error,
        data: {
          query: query,
          suggestions: []
        }
      });
    }

  } catch (error) {
    console.error('获取搜索建议失败:', error);
    return res.status(500).json({
      success: false,
      error: '服务器内部错误: ' + error.message
    });
  }
}

/**
 * 处理批量关键词的搜索建议
 */
async function handleBatchSuggestions(req, res) {
  try {
    const { queries, limit = 10 } = req.body;

    if (!queries || !Array.isArray(queries) || queries.length === 0) {
      return res.status(400).json({
        success: false,
        error: '请提供有效的关键词数组'
      });
    }

    if (queries.length > 50) {
      return res.status(400).json({
        success: false,
        error: '批量处理最多支持50个关键词'
      });
    }

    const maxSuggestions = Math.min(parseInt(limit) || 10, 20);

    // 批量获取关键词搜索建议

    const result = await googleAPI.getBatchSuggestions(queries, maxSuggestions);

    // 统计成功和失败的数量
    const successful = result.results.filter(r => r.success).length;
    const failed = result.results.filter(r => !r.success).length;
    const totalSuggestions = result.results.reduce((sum, r) => sum + (r.suggestions?.length || 0), 0);

    return res.status(200).json({
      success: true,
      data: {
        summary: {
          total_queries: result.total,
          processed: result.processed,
          successful: successful,
          failed: failed,
          total_suggestions: totalSuggestions
        },
        results: result.results
      },
      message: `批量处理完成: ${successful} 成功, ${failed} 失败, 共获得 ${totalSuggestions} 个建议`
    });

  } catch (error) {
    console.error('批量获取搜索建议失败:', error);
    return res.status(500).json({
      success: false,
      error: '服务器内部错误: ' + error.message
    });
  }
}
