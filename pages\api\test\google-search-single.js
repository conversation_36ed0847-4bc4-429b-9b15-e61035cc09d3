/**
 * Google搜索API单个密钥测试接口
 * 测试指定的Google搜索API密钥
 */

export default async function handler(req, res) {
  // 设置CORS头
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  if (req.method === 'POST') {
    return handleTestSingleGoogleKey(req, res);
  }

  return res.status(405).json({
    success: false,
    error: '不支持的请求方法，请使用POST'
  });
}

/**
 * 处理单个Google搜索API密钥测试
 */
async function handleTestSingleGoogleKey(req, res) {
  try {
    const { keyIndex, testQuery = 'catchideas.com' } = req.body;

    if (!keyIndex || keyIndex < 1 || keyIndex > 20) {
      return res.status(400).json({
        success: false,
        error: '无效的密钥索引，必须在1-20之间'
      });
    }

    const apiKey = process.env[`GOOGLE_SEARCH_API_KEY_${keyIndex}`];
    const searchCx = process.env[`GOOGLE_SEARCH_CX_${keyIndex}`];
    
    if (!apiKey || !searchCx) {
      return res.status(200).json({
        success: true,
        result: {
          keyIndex: keyIndex,
          keyName: `GOOGLE_SEARCH_API_KEY_${keyIndex}`,
          apiKey: apiKey ? `${apiKey.slice(0, 8)}...` : '',
          searchCx: searchCx ? `${searchCx.slice(0, 8)}...` : '',
          status: 'error',
          error: !apiKey ? '密钥未配置' : '搜索引擎ID未配置'
        }
      });
    }

    try {
      // 构建Google Custom Search API URL
      const searchUrl = new URL('https://www.googleapis.com/customsearch/v1');
      searchUrl.searchParams.append('key', apiKey);
      searchUrl.searchParams.append('cx', searchCx);
      searchUrl.searchParams.append('q', testQuery);
      searchUrl.searchParams.append('num', '1'); // 只请求1个结果进行测试

      // 创建AbortController用于超时控制
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000); // 10秒超时

      const response = await fetch(searchUrl.toString(), {
        method: 'GET',
        headers: {
          'User-Agent': 'CatchIdeas/1.0'
        },
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        let errorMessage = `HTTP ${response.status}`;
        
        if (response.status === 403) {
          errorMessage = 'API密钥无效或配额不足 - 检查密钥配置和今日配额';
        } else if (response.status === 400) {
          errorMessage = '请求参数错误 - 检查搜索引擎ID配置';
        } else if (response.status === 429) {
          errorMessage = '请求频率过高 - 建议稍后重试或检查今日配额';
        }

        throw new Error(errorMessage);
      }

      const data = await response.json();

      // 检查响应数据
      if (data.error) {
        throw new Error(data.error.message || '搜索API返回错误');
      }

      return res.status(200).json({
        success: true,
        result: {
          keyIndex: keyIndex,
          keyName: `GOOGLE_SEARCH_API_KEY_${keyIndex}`,
          apiKey: `${apiKey.slice(0, 8)}...`,
          searchCx: `${searchCx.slice(0, 8)}...`,
          status: 'success',
          testResult: {
            totalResults: data.searchInformation?.totalResults || '0',
            searchTime: data.searchInformation?.searchTime || '0',
            resultsCount: data.items?.length || 0
          }
        }
      });

    } catch (err) {
      let errorMessage = err.message;
      if (err.name === 'AbortError') {
        errorMessage = '请求超时';
      }

      return res.status(200).json({
        success: true,
        result: {
          keyIndex: keyIndex,
          keyName: `GOOGLE_SEARCH_API_KEY_${keyIndex}`,
          apiKey: apiKey ? `${apiKey.slice(0, 8)}...` : '',
          searchCx: searchCx ? `${searchCx.slice(0, 8)}...` : '',
          status: 'error',
          error: errorMessage
        }
      });
    }

  } catch {
    return res.status(500).json({
      success: false,
      error: '服务器内部错误'
    });
  }
}
