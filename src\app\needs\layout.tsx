import type { Metadata } from "next";

export const metadata: Metadata = {
  title: "需求导航 | CatchIdeas",
  description: "全年龄段用户需求导向关键词导航，已验证Google Trends热度。涵盖学习教育、工作职场、生活管理、娱乐休闲、创意表达等10大分类，500+需求词根，支持自定义组合生成，助力精准用户需求挖掘。",
  authors: [{ name: "CatchIdeas团队" }],
  creator: "CatchIdeas",
  publisher: "CatchIdeas",
  robots: "index, follow",
  openGraph: {
    type: "website",
    locale: "zh_CN",
    url: "https://catchideas.com/needs",
    title: "需求导航 | CatchIdeas",
    description: "全年龄段用户需求导向关键词导航，已验证Google Trends热度。涵盖学习教育、工作职场、生活管理、娱乐休闲、创意表达等10大分类，500+需求词根，支持自定义组合生成，助力精准用户需求挖掘。",
    siteName: "CatchIdeas",
  },
  twitter: {
    card: "summary_large_image",
    title: "需求导航 | CatchIdeas",
    description: "全年龄段用户需求导向关键词导航，已验证Google Trends热度。涵盖学习教育、工作职场、生活管理、娱乐休闲、创意表达等10大分类，500+需求词根，支持自定义组合生成，助力精准用户需求挖掘。",
  },
  alternates: {
    canonical: "https://catchideas.com/needs",
  },
};

export default function NeedsLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}
