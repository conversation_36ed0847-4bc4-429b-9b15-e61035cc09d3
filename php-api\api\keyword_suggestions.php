<?php
require_once __DIR__ . '/../config/cors.php';
require_once __DIR__ . '/../models/KeywordSuggestion.php';
require_once __DIR__ . '/../utils/Response.php';

// 处理CORS预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {
    $keywordSuggestionModel = new KeywordSuggestion();
    
    switch ($_SERVER['REQUEST_METHOD']) {
        case 'GET':
            handleGet($keywordSuggestionModel);
            break;
        case 'POST':
            handlePost($keywordSuggestionModel);
            break;
        case 'DELETE':
            handleDelete($keywordSuggestionModel);
            break;
        default:
            Response::methodNotAllowed();
    }
} catch (Exception $e) {
    Response::serverError('服务器内部错误: ' . $e->getMessage());
}

/**
 * 处理GET请求 - 获取关键词建议
 */
function handleGet($keywordSuggestionModel) {
    if (!isset($_GET['keyword_id'])) {
        Response::validationError(['keyword_id' => '关键词ID不能为空']);
    }
    
    $keywordId = $_GET['keyword_id'];
    $suggestions = $keywordSuggestionModel->getByKeywordId($keywordId);
    
    Response::success([
        'keyword_id' => $keywordId,
        'suggestions' => $suggestions,
        'total' => count($suggestions)
    ]);
}

/**
 * 处理POST请求 - 保存关键词建议
 */
function handlePost($keywordSuggestionModel) {
    $data = Response::getRequestData();
    
    // 验证必需字段
    if (!isset($data['keyword_id']) || empty($data['keyword_id'])) {
        Response::validationError(['keyword_id' => '关键词ID不能为空']);
    }
    
    if (!isset($data['suggestions']) || !is_array($data['suggestions']) || empty($data['suggestions'])) {
        Response::validationError(['suggestions' => '建议列表不能为空']);
    }
    
    if (!isset($data['source']) || empty($data['source'])) {
        Response::validationError(['source' => '建议来源不能为空']);
    }
    
    $keywordId = $data['keyword_id'];
    $suggestions = $data['suggestions'];
    $source = $data['source'];
    
    // 验证来源值
    $allowedSources = ['google_suggestions_1', 'google_suggestions_2'];
    if (!in_array($source, $allowedSources)) {
        Response::validationError(['source' => '无效的建议来源']);
    }
    
    // 过滤和清理建议
    $cleanSuggestions = [];
    foreach ($suggestions as $suggestion) {
        $cleanSuggestion = trim($suggestion);
        if (!empty($cleanSuggestion) && strlen($cleanSuggestion) <= 255) {
            $cleanSuggestions[] = $cleanSuggestion;
        }
    }
    
    if (empty($cleanSuggestions)) {
        Response::validationError(['suggestions' => '没有有效的建议']);
    }
    
    // 保存建议
    $success = $keywordSuggestionModel->createBatch($keywordId, $cleanSuggestions, $source);
    
    if (!$success) {
        Response::serverError('保存建议失败');
    }
    
    // 获取保存后的建议
    $savedSuggestions = $keywordSuggestionModel->getByKeywordId($keywordId);
    
    Response::success([
        'keyword_id' => $keywordId,
        'suggestions' => $savedSuggestions,
        'total' => count($savedSuggestions),
        'source' => $source
    ], '建议保存成功', 201);
}

/**
 * 处理DELETE请求 - 删除关键词建议
 */
function handleDelete($keywordSuggestionModel) {
    if (!isset($_GET['keyword_id'])) {
        Response::validationError(['keyword_id' => '关键词ID不能为空']);
    }
    
    $keywordId = $_GET['keyword_id'];
    $success = $keywordSuggestionModel->deleteByKeywordId($keywordId);
    
    if (!$success) {
        Response::serverError('删除建议失败');
    }
    
    Response::success(['keyword_id' => $keywordId], '建议删除成功');
}
?>
