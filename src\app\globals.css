@import "tailwindcss";

@custom-variant dark (&:is(.dark *));

@layer base {
  :root {
    /* CatchIdeas 创意发现配色方案 - 创意紫 + 洞察蓝 */
    --background: 0 0% 99%;                     /* #FCFCFC 极浅灰白 */
    --foreground: 222 84% 5%;                   /* #0F0F23 深蓝黑 */
    --card: 240 5% 98%;                         /* #F8F9FB 浅灰卡片 */
    --card-foreground: 222 84% 5%;              /* #0F0F23 卡片文字 */
    --popover: 0 0% 100%;                       /* #FFFFFF 弹窗背景 */
    --popover-foreground: 222 84% 5%;           /* #0F0F23 弹窗文字 */

    /* 创意紫系列 - 主色调 */
    --primary: 262 83% 58%;                     /* #8B5CF6 紫色 - 代表创意与洞察 */
    --primary-foreground: 0 0% 100%;            /* #FFFFFF 白色文字 */

    /* 洞察蓝系列 - 辅助色 */
    --secondary: 217 91% 60%;                   /* #3B82F6 蓝色 - 代表数据与分析 */
    --secondary-foreground: 0 0% 100%;          /* #FFFFFF 白色文字 */

    /* 成功绿 - 已分析状态 */
    --success: 142 76% 36%;                     /* #059669 绿色 */
    --success-foreground: 0 0% 100%;            /* #FFFFFF 白色文字 */

    /* 警告橙 - 待分析状态 */
    --warning: 38 92% 50%;                      /* #F59E0B 橙色 */
    --warning-foreground: 0 0% 100%;            /* #FFFFFF 白色文字 */

    /* 背景色系 - 温和中性 */
    --muted: 240 5% 96%;                        /* #F8F9FA 静音背景 */
    --muted-foreground: 215 16% 47%;            /* #64748B 次要文字 */
    --accent: 262 83% 58%;                      /* #8B5CF6 强调色 */
    --accent-foreground: 0 0% 100%;             /* #FFFFFF 强调色文字 */
    --destructive: 0 84% 60%;                   /* #EF4444 错误色 */
    --destructive-foreground: 0 0% 100%;        /* #FFFFFF 错误色文字 */

    /* 边框色 - 精致分割 */
    --border: 240 6% 90%;                       /* #E2E8F0 边框色 */
    --input: 240 9% 85%;                        /* #D1D5DB 输入框边框 - 更明显 */
    --ring: 262 83% 58%;                        /* #8B5CF6 焦点环 */
    --radius: 0.75rem;                          /* 圆角 */

    /* 图表配色 */
    --chart-1: 262 83% 58%;                     /* #8B5CF6 创意紫 */
    --chart-2: 217 91% 60%;                     /* #3B82F6 洞察蓝 */
    --chart-3: 142 76% 36%;                     /* #059669 成功绿 */
    --chart-4: 38 92% 50%;                      /* #F59E0B 警告橙 */
    --chart-5: 0 84% 60%;                       /* #EF4444 错误红 */
  }

  .dark {
    /* 深色模式配色 */
    --background: 222 84% 5%;                       /* #0F0F23 深蓝黑背景 */
    --foreground: 0 0% 98%;                         /* #FAFAFA 浅色文字 */
    --card: 240 10% 8%;                             /* #1A1B23 深色卡片 */
    --card-foreground: 0 0% 95%;                    /* #F2F2F2 卡片文字 */
    --popover: 240 10% 8%;                          /* #1A1B23 弹窗背景 */
    --popover-foreground: 0 0% 95%;                 /* #F2F2F2 弹窗文字 */

    /* 主色调保持不变 */
    --primary: 262 83% 58%;                         /* #8B5CF6 紫色 */
    --primary-foreground: 0 0% 100%;                /* #FFFFFF 白色文字 */
    --secondary: 217 91% 60%;                       /* #3B82F6 蓝色 */
    --secondary-foreground: 0 0% 100%;              /* #FFFFFF 白色文字 */
    --success: 142 76% 36%;                         /* #059669 绿色 */
    --success-foreground: 0 0% 100%;                /* #FFFFFF 白色文字 */
    --warning: 38 92% 50%;                          /* #F59E0B 橙色 */
    --warning-foreground: 0 0% 100%;                /* #FFFFFF 白色文字 */

    /* 深色模式背景色系 */
    --muted: 240 10% 12%;                           /* #1E1F29 静音背景 */
    --muted-foreground: 215 16% 65%;                /* #94A3B8 次要文字 */
    --accent: 262 83% 58%;                          /* #8B5CF6 强调色 */
    --accent-foreground: 0 0% 100%;                 /* #FFFFFF 强调色文字 */
    --destructive: 0 84% 60%;                       /* #EF4444 错误色 */
    --destructive-foreground: 0 0% 100%;            /* #FFFFFF 错误色文字 */

    /* 深色模式边框色 */
    --border: 240 10% 15%;                          /* #26272F 边框色 */
    --input: 240 7% 25%;                            /* #3F3F46 输入框边框 - 更明显 */
    --ring: 262 83% 58%;                            /* #8B5CF6 焦点环 */
  }

}

@layer base {
  * {
    border-color: hsl(var(--border));
  }

  body {
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
    font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
    line-height: 1.5;
    min-height: 100vh;
  }

  html {
    scroll-behavior: smooth;
  }

  h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    color: hsl(var(--foreground));
  }

  p {
    font-weight: 400;
    color: hsl(var(--muted-foreground));
  }
}

@layer utilities {
  .line-clamp-1 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    line-clamp: 1;
  }

  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    line-clamp: 2;
  }

  .line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
    line-clamp: 3;
  }
}

@layer components {
  /* 主流卡片样式 */
  .card-enhanced {
    background-color: hsl(var(--card));
    border: 1px solid hsl(var(--border));
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: box-shadow 0.2s ease, transform 0.2s ease;
  }

  .card-enhanced:hover {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    transform: translateY(-1px);
  }

  /* 主流按钮样式 */
  .btn-primary {
    background-color: hsl(var(--primary));
    color: hsl(var(--primary-foreground));
    border: none;
    transition: background-color 0.2s ease, color 0.2s ease;
  }

  .btn-primary:hover {
    background-color: hsl(221 83% 45%); /* #1D4ED8 悬浮色 */
  }

  .btn-primary:active {
    background-color: hsl(221 83% 38%); /* #1E40AF 激活色 */
  }

  .btn-primary:disabled {
    background-color: hsl(220 13% 82%); /* #D1D5DB 禁用背景 */
    color: hsl(220 9% 68%); /* #9CA3AF 禁用文字 */
  }

  .btn-secondary {
    background-color: hsl(var(--secondary));
    color: hsl(var(--secondary-foreground));
    border: 1px solid hsl(var(--border));
    transition: background-color 0.2s ease, color 0.2s ease;
  }

  .btn-secondary:hover {
    background-color: hsl(220 9% 40%); /* 悬浮时稍深 */
  }

  /* 链接样式 */
  .link-primary {
    color: hsl(var(--primary));
    text-decoration: none;
    transition: color 0.2s ease;
  }

  .link-primary:hover {
    color: hsl(221 83% 45%); /* #1D4ED8 悬浮色 */
    text-decoration: underline;
  }

  /* 标签样式 */
  .badge-enhanced {
    background-color: hsl(var(--muted));
    color: hsl(var(--muted-foreground));
    border: 1px solid hsl(var(--border));
  }

  /* 分割线 */
  .divider {
    border-color: hsl(var(--border));
  }

  /* ===== CatchIdeas 设计系统样式 ===== */

  /* 前台创意卡片样式 */
  .idea-card-enhanced {
    background: linear-gradient(135deg, hsl(var(--card)) 0%, hsl(var(--muted)) 100%);
    border: 1px solid hsl(var(--border));
    border-radius: calc(var(--radius) + 4px);
    box-shadow: 0 2px 8px rgba(139, 92, 246, 0.08), 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(8px);
  }

  .idea-card-enhanced:hover {
    box-shadow: 0 8px 25px rgba(139, 92, 246, 0.15), 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
    border-color: hsl(var(--primary) / 0.3);
  }

  /* 后台数据卡片样式 */
  .data-card-enhanced {
    background: hsl(var(--card));
    border: 1px solid hsl(var(--border) / 0.5);
    border-radius: var(--radius);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease;
  }

  .data-card-enhanced:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-color: hsl(var(--primary) / 0.2);
  }

  /* 增强按钮样式 */
  .btn-primary-enhanced {
    background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(262 83% 65%) 100%);
    color: hsl(var(--primary-foreground));
    border: none;
    border-radius: var(--radius);
    font-weight: 600;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 2px 4px rgba(139, 92, 246, 0.2);
  }

  .btn-primary-enhanced:hover {
    background: linear-gradient(135deg, hsl(262 83% 65%) 0%, hsl(262 83% 70%) 100%);
    box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
    transform: translateY(-1px);
  }

  .btn-primary-enhanced:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(139, 92, 246, 0.2);
  }

  .btn-secondary-enhanced {
    background: linear-gradient(135deg, hsl(var(--secondary)) 0%, hsl(217 91% 65%) 100%);
    color: hsl(var(--secondary-foreground));
    border: none;
    border-radius: var(--radius);
    font-weight: 600;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
  }

  .btn-secondary-enhanced:hover {
    background: linear-gradient(135deg, hsl(217 91% 65%) 0%, hsl(217 91% 70%) 100%);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
    transform: translateY(-1px);
  }

  .btn-secondary-enhanced:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
  }

  /* 状态标签样式 */
  .badge-analyzed {
    background: linear-gradient(135deg, hsl(var(--success)) 0%, hsl(142 76% 42%) 100%);
    color: hsl(var(--success-foreground));
    border: 1px solid hsl(var(--success) / 0.3);
    font-weight: 500;
  }

  .badge-analyzing {
    background: linear-gradient(135deg, hsl(var(--warning)) 0%, hsl(38 92% 55%) 100%);
    color: hsl(var(--warning-foreground));
    border: 1px solid hsl(var(--warning) / 0.3);
    font-weight: 500;
  }



  .badge-trend {
    background: linear-gradient(135deg, hsl(var(--secondary)) 0%, hsl(217 91% 65%) 100%);
    color: hsl(var(--secondary-foreground));
    border: 1px solid hsl(var(--secondary) / 0.3);
    font-weight: 500;
  }
}

:root {
  --sidebar: hsl(0 0% 98%);
  --sidebar-foreground: hsl(240 5.3% 26.1%);
  --sidebar-primary: hsl(240 5.9% 10%);
  --sidebar-primary-foreground: hsl(0 0% 98%);
  --sidebar-accent: hsl(240 4.8% 95.9%);
  --sidebar-accent-foreground: hsl(240 5.9% 10%);
  --sidebar-border: hsl(220 13% 91%);
  --sidebar-ring: hsl(217.2 91.2% 59.8%);

}

.dark {
  --sidebar: hsl(240 5.9% 10%);
  --sidebar-foreground: hsl(240 4.8% 95.9%);
  --sidebar-primary: hsl(224.3 76.3% 48%);
  --sidebar-primary-foreground: hsl(0 0% 100%);
  --sidebar-accent: hsl(240 3.7% 15.9%);
  --sidebar-accent-foreground: hsl(240 4.8% 95.9%);
  --sidebar-border: hsl(240 3.7% 15.9%);
  --sidebar-ring: hsl(217.2 91.2% 59.8%);

}

@theme inline {
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

}

@layer base {
  * {
    border-color: hsl(var(--border));
    outline-color: hsl(var(--ring) / 0.5);
  }
  body {
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
  }
}

@layer components {
  /* CatchIdeas 设计系统样式 */

  /* 创意卡片样式 - 前台使用 */
  .idea-card-enhanced {
    background-color: hsl(var(--card));
    color: hsl(var(--card-foreground));
    border: 1px solid hsl(var(--border) / 0.5);
    border-radius: calc(var(--radius) + 4px);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease-out;
  }

  .idea-card-enhanced:hover {
    transform: translateY(-2px);
    border-color: hsl(var(--primary) / 0.2);
    box-shadow: 0 8px 24px rgba(139, 92, 246, 0.15);
  }

  /* 数据卡片样式 - 后台使用，与前台保持一致 */
  .data-card-enhanced {
    background-color: hsl(var(--card));
    color: hsl(var(--card-foreground));
    border: 1px solid hsl(var(--border) / 0.5);
    border-radius: calc(var(--radius) + 2px);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease-out;
  }

  .data-card-enhanced:hover {
    transform: translateY(-1px);
    border-color: hsl(var(--primary) / 0.15);
    box-shadow: 0 6px 20px rgba(139, 92, 246, 0.12);
  }

  /* 按钮样式增强 - 统一前后台风格 */
  .btn-primary-enhanced {
    background: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--primary) / 0.9));
    color: hsl(var(--primary-foreground));
    font-weight: 600;
    border-radius: calc(var(--radius) + 2px);
    padding: 0.75rem 1.5rem;
    box-shadow: 0 2px 8px rgba(139, 92, 246, 0.25);
    transition: all 0.3s ease-out;
    border: none;
  }

  .btn-primary-enhanced:hover {
    background: linear-gradient(135deg, hsl(var(--primary) / 0.9), hsl(var(--primary) / 0.8));
    box-shadow: 0 4px 16px rgba(139, 92, 246, 0.35);
    transform: translateY(-1px) scale(1.02);
  }

  .btn-primary-enhanced:active {
    transform: translateY(0) scale(0.98);
    box-shadow: 0 2px 8px rgba(139, 92, 246, 0.25);
  }

  .btn-secondary-enhanced {
    background: linear-gradient(135deg, hsl(var(--secondary)), hsl(var(--secondary) / 0.9));
    color: hsl(var(--secondary-foreground));
    font-weight: 600;
    border: 1px solid hsl(var(--border) / 0.3);
    border-radius: calc(var(--radius) + 2px);
    padding: 0.75rem 1.5rem;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.25);
    transition: all 0.3s ease-out;
  }

  .btn-secondary-enhanced:hover {
    background: linear-gradient(135deg, hsl(var(--secondary) / 0.9), hsl(var(--secondary) / 0.8));
    box-shadow: 0 4px 16px rgba(59, 130, 246, 0.35);
    transform: translateY(-1px) scale(1.02);
    border-color: hsl(var(--secondary) / 0.5);
  }

  .btn-secondary-enhanced:active {
    transform: translateY(0) scale(0.98);
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.25);
  }

  /* Outline按钮增强样式 */
  .btn-outline-enhanced {
    background: transparent;
    color: hsl(var(--primary));
    font-weight: 500;
    border: 2px solid hsl(var(--primary) / 0.3);
    border-radius: calc(var(--radius) + 2px);
    padding: 0.625rem 1.25rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease-out;
  }

  .btn-outline-enhanced:hover {
    background: hsl(var(--primary) / 0.05);
    border-color: hsl(var(--primary) / 0.5);
    color: hsl(var(--primary));
    box-shadow: 0 3px 12px rgba(139, 92, 246, 0.15);
    transform: translateY(-1px) scale(1.02);
  }

  .btn-outline-enhanced:active {
    transform: translateY(0) scale(0.98);
    background: hsl(var(--primary) / 0.1);
  }

  .btn-success-enhanced {
    background: linear-gradient(135deg, hsl(var(--success)), hsl(var(--success) / 0.9));
    color: hsl(var(--success-foreground));
    font-weight: 600;
    border-radius: calc(var(--radius) + 2px);
    box-shadow: 0 2px 8px rgba(34, 197, 94, 0.25);
    transition: all 0.3s ease-out;
  }

  .btn-success-enhanced:hover {
    box-shadow: 0 4px 16px rgba(34, 197, 94, 0.35);
    transform: translateY(-1px) scale(1.02);
  }

  .btn-warning-enhanced {
    background: linear-gradient(135deg, hsl(var(--warning)), hsl(var(--warning) / 0.9));
    color: hsl(var(--warning-foreground));
    font-weight: 600;
    border-radius: calc(var(--radius) + 2px);
    box-shadow: 0 2px 8px rgba(245, 158, 11, 0.25);
    transition: all 0.3s ease-out;
  }

  .btn-warning-enhanced:hover {
    box-shadow: 0 4px 16px rgba(245, 158, 11, 0.35);
    transform: translateY(-1px) scale(1.02);
  }

  /* 后台背景样式 - 与前台保持一致 */
  .console-background {
    background: linear-gradient(135deg,
      hsl(var(--background)) 0%,
      hsl(var(--muted) / 0.3) 50%,
      hsl(var(--background)) 100%);
    min-height: 100vh;
  }

  .console-gradient-section {
    background: linear-gradient(135deg,
      hsl(var(--primary) / 0.05) 0%,
      hsl(var(--secondary) / 0.05) 50%,
      hsl(var(--primary) / 0.03) 100%);
    border-radius: calc(var(--radius) + 4px);
    border: 1px solid hsl(var(--border) / 0.5);
  }

  /* 统计卡片特殊样式 */
  .stats-card-primary {
    background: linear-gradient(135deg,
      hsl(var(--primary) / 0.1) 0%,
      hsl(var(--primary) / 0.05) 100%);
    border: 1px solid hsl(var(--primary) / 0.2);
  }

  .stats-card-success {
    background: linear-gradient(135deg,
      hsl(var(--success) / 0.1) 0%,
      hsl(var(--success) / 0.05) 100%);
    border: 1px solid hsl(var(--success) / 0.2);
  }

  .stats-card-warning {
    background: linear-gradient(135deg,
      hsl(var(--warning) / 0.1) 0%,
      hsl(var(--warning) / 0.05) 100%);
    border: 1px solid hsl(var(--warning) / 0.2);
  }

  .stats-card-secondary {
    background: linear-gradient(135deg,
      hsl(var(--secondary) / 0.1) 0%,
      hsl(var(--secondary) / 0.05) 100%);
    border: 1px solid hsl(var(--secondary) / 0.2);
  }

  /* 状态标签样式 */
  .badge-analyzed {
    background-color: hsl(var(--success)) !important;
    color: white !important;
    border: 1px solid hsl(var(--success)) !important;
    font-weight: 600;
    padding: 0.25rem 0.5rem;
    border-radius: 9999px;
    font-size: 0.75rem;
  }

  .badge-analyzing {
    background-color: #B45309 !important;
    color: white !important;
    border: 1px solid #B45309 !important;
    font-weight: 600;
    padding: 0.25rem 0.5rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    animation: pulse 2s infinite;
  }

  .badge-pending {
    background-color: #B45309 !important;
    color: white !important;
    border: 1px solid #B45309 !important;
    font-weight: 600;
    padding: 0.25rem 0.5rem;
    border-radius: 9999px;
    font-size: 0.75rem;
  }

  .badge-trend {
    background: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--secondary)));
    color: white;
    font-weight: 500;
    padding: 0.25rem 0.5rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    backdrop-filter: blur(8px);
  }

  /* 竞争难度专用样式 */
  .badge-competition-easy {
    background-color: #15803d;
    color: white;
    border: 1px solid #15803d;
    font-weight: 600;
    padding: 0.25rem 0.5rem;
    border-radius: 9999px;
    font-size: 0.75rem;
  }

  .badge-competition-medium {
    background-color: #ca8a04;
    color: white;
    border: 1px solid #ca8a04;
    font-weight: 600;
    padding: 0.25rem 0.5rem;
    border-radius: 9999px;
    font-size: 0.75rem;
  }

  .badge-competition-hard {
    background-color: #dc2626;
    color: white;
    border: 1px solid #dc2626;
    font-weight: 600;
    padding: 0.25rem 0.5rem;
    border-radius: 9999px;
    font-size: 0.75rem;
  }

  /* 分析状态可视化 */
  .status-analyzed {
    color: hsl(var(--success));
    background: hsl(var(--success) / 0.1);
  }

  .status-analyzing {
    color: hsl(var(--warning));
    background: hsl(var(--warning) / 0.1);
    animation: pulse 2s infinite;
  }

  .status-pending {
    color: hsl(var(--muted-foreground));
    background: hsl(var(--muted));
  }

  /* 文字对比度 */
  .text-high-contrast {
    color: hsl(var(--foreground));
  }

  .text-medium-contrast {
    color: hsl(var(--muted-foreground));
  }

  .text-success {
    color: hsl(var(--success));
  }

  .text-warning {
    color: hsl(var(--warning));
  }

  .text-error {
    color: hsl(var(--destructive));
  }

  /* 大厂风格动画效果 */
  @keyframes blob {
    0% {
      transform: translate(0px, 0px) scale(1);
    }
    33% {
      transform: translate(30px, -50px) scale(1.1);
    }
    66% {
      transform: translate(-20px, 20px) scale(0.9);
    }
    100% {
      transform: translate(0px, 0px) scale(1);
    }
  }

  .animate-blob {
    animation: blob 7s infinite;
  }

  .animation-delay-2000 {
    animation-delay: 2s;
  }

  .animation-delay-4000 {
    animation-delay: 4s;
  }

  /* 网格背景图案 */
  .bg-grid-pattern {
    background-image:
      linear-gradient(rgba(139, 92, 246, 0.1) 1px, transparent 1px),
      linear-gradient(90deg, rgba(139, 92, 246, 0.1) 1px, transparent 1px);
    background-size: 20px 20px;
  }

  /* 增强的切换按钮样式 */
  .enhanced-tabs-list {
    background: linear-gradient(145deg, hsl(var(--muted) / 0.3), hsl(var(--muted) / 0.1));
    backdrop-filter: blur(8px);
    border: 1px solid hsl(var(--border) / 0.3);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  }

  .enhanced-tab-trigger {
    position: relative;
    background: linear-gradient(145deg, hsl(var(--background) / 0.8), hsl(var(--background) / 0.6));
    backdrop-filter: blur(4px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .enhanced-tab-trigger:hover {
    background: linear-gradient(145deg, hsl(var(--background)), hsl(var(--background) / 0.9));
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  }

  .enhanced-tab-trigger[data-state="active"] {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
  }

  /* ===== Sparkles Logo 旋转动画 ===== */

  /* 定义旋转动画 */
  @keyframes sparkles-rotate {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }

  /* Sparkles图标基础旋转样式 */
  .sparkles-rotate {
    transform-origin: center;
    animation: sparkles-rotate 8s linear infinite;
    transition: animation-duration 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }

  /* 鼠标悬停时加速旋转 */
  .sparkles-rotate:hover {
    animation-duration: 1s;
  }

  /* 更平滑的过渡效果 */
  .sparkles-smooth {
    transform-origin: center;
    animation: sparkles-rotate 8s linear infinite;
    transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .sparkles-smooth:hover {
    animation-duration: 1s;
    filter: brightness(1.1) saturate(1.2);
  }

  /* 为不同位置的图标提供不同的动画延迟 */
  .sparkles-delay-1 {
    animation-delay: 0s;
  }

  .sparkles-delay-2 {
    animation-delay: 2s;
  }

  .sparkles-delay-3 {
    animation-delay: 4s;
  }

  /* 打字机效果 */
  .typewriter-container {
    display: flex;
    align-items: center;
    min-height: 2rem;
  }

  .typewriter-text {
    white-space: nowrap;
    border-right: 2px solid #8B5CF6;
    animation: blink-caret 0.75s step-end infinite;
    min-height: 1.5rem;
    display: inline-block;
  }

  .typewriter-cursor {
    display: none; /* 隐藏额外的光标，使用border-right代替 */
  }

  /* 光标闪烁效果 */
  @keyframes blink-caret {
    from, to {
      border-color: transparent;
    }
    50% {
      border-color: #8B5CF6;
    }
  }

  @keyframes blink {
    0%, 50% {
      opacity: 1;
    }
    51%, 100% {
      opacity: 0;
    }
  }
}
