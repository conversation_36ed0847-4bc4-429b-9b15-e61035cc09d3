import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams
  const url = searchParams.get('url')

  if (!url) {
    return NextResponse.json({ error: 'URL parameter is required' }, { status: 400 })
  }

  try {
    // 设置请求头，模拟浏览器请求
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/plain,text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
      },
      // 设置超时时间
      signal: AbortSignal.timeout(10000), // 10秒超时
    })

    if (response.ok) {
      const content = await response.text()
      
      // 验证内容是否像sitemap.txt
      const lines = content.split('\n').filter(line => line.trim())
      const validLines = lines.filter(line => {
        const trimmed = line.trim()
        return trimmed.startsWith('http://') || trimmed.startsWith('https://')
      })

      // 如果大部分行都是URL，认为是有效的sitemap
      const isValidSitemap = validLines.length > 0 && (validLines.length / lines.length) > 0.5

      if (isValidSitemap) {
        return new NextResponse(content, {
          status: 200,
          headers: {
            'Content-Type': 'text/plain',
          },
        })
      } else {
        return NextResponse.json({ error: 'Invalid sitemap format' }, { status: 400 })
      }
    } else {
      return NextResponse.json({ 
        error: `HTTP ${response.status}: ${response.statusText}` 
      }, { status: response.status })
    }
  } catch (error) {
    console.error('Sitemap check error:', error)
    
    if (error instanceof Error) {
      if (error.name === 'AbortError') {
        return NextResponse.json({ error: 'Request timeout' }, { status: 408 })
      }
      return NextResponse.json({ error: error.message }, { status: 500 })
    }
    
    return NextResponse.json({ error: 'Unknown error occurred' }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  return NextResponse.json({ error: 'Method not allowed' }, { status: 405 })
}
