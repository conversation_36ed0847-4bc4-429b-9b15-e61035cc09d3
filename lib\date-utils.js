/**
 * 日期工具函数
 * 提供统一的日期格式化功能
 */

/**
 * 获取当前日期的中文字符串格式
 * @returns {string} 格式：2025年6月28日
 */
export function getCurrentDateString() {
  const currentDate = new Date()
  const currentYear = currentDate.getFullYear()
  return `${currentYear}年${currentDate.getMonth() + 1}月${currentDate.getDate()}日`
}

/**
 * 获取当前年份
 * @returns {number} 当前年份
 */
export function getCurrentYear() {
  return new Date().getFullYear()
}

/**
 * 获取今天的日期字符串（用于API token管理）
 * @returns {string} 格式：Mon Jun 28 2025
 */
export function getTodayString() {
  return new Date().toDateString()
}
