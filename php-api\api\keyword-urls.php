<?php
/**
 * 关键词URL生成API - 生成所有关键词详情页面URL（txt格式）
 */

require_once '../config/cors.php';
require_once '../config/database.php';

// 设置为纯文本输出
header('Content-Type: text/plain; charset=utf-8');

try {
    // 只允许GET请求
    if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
        http_response_code(405);
        echo 'Method Not Allowed';
        exit;
    }

    // 获取查询参数
    $baseUrl = $_GET['base_url'] ?? 'https://catchideas.com';
    $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : null;
    $analyzed_only = isset($_GET['analyzed_only']) ? (bool)$_GET['analyzed_only'] : false;

    // 清理base_url，确保没有尾部斜杠
    $baseUrl = rtrim($baseUrl, '/');

    // 获取数据库连接
    $database = new Database();
    $pdo = $database->getConnection();

    // 构建SQL查询
    $sql = "SELECT id FROM keywords WHERE 1=1";

    $params = [];

    // 如果只要已分析的关键词
    if ($analyzed_only) {
        $sql .= " AND is_analyzed = 1";
    }

    // 按创建时间排序
    $sql .= " ORDER BY created_at DESC";

    // 添加限制
    if ($limit && $limit > 0) {
        $sql .= " LIMIT :limit";
        $params['limit'] = $limit;
    }

    $stmt = $pdo->prepare($sql);

    // 绑定参数
    foreach ($params as $key => $value) {
        $stmt->bindValue(':' . $key, $value, PDO::PARAM_INT);
    }

    $stmt->execute();
    $keywords = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // 生成URL列表并直接输出
    foreach ($keywords as $keyword) {
        echo $baseUrl . '/keyword/' . $keyword['id'] . "\n";
    }

} catch (PDOException $e) {
    http_response_code(500);
    echo 'Database Error';
} catch (Exception $e) {
    http_response_code(500);
    echo 'Server Error';
}
?>
