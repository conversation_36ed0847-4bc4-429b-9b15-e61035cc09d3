import type { Metadata } from 'next'

// 获取关键词数据的服务端函数
async function getKeyword(id: string) {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'https://catchideas.com'}/api/keywords/${id}`, {
      // 启用缓存，5分钟内重复请求使用缓存
      next: {
        revalidate: 300 // 5分钟缓存
      },
      // 设置超时时间
      signal: AbortSignal.timeout(8000) // 8秒超时
    })

    if (!response.ok) {
      console.error(`API request failed: ${response.status} ${response.statusText}`)
      return null
    }

    const data = await response.json()

    // 验证数据结构
    if (data && data.success && data.data && data.data.keyword) {
      return data.data
    }

    console.error('Invalid API response structure:', data)
    return null
  } catch (error) {
    console.error('Error fetching keyword:', error)
    return null
  }
}

// 动态生成页面metadata
export async function generateMetadata({ params }: { params: { id: string } }): Promise<Metadata> {
  const keyword = await getKeyword(params.id)

  if (!keyword || !keyword.keyword) {
    return {
      title: '关键词分析 | CatchIdeas',
      description: '专业的关键词分析平台，提供用户意图分析、竞争难度评估、市场机会发现等服务',
      robots: 'noindex'
    }
  }

  // 生成SEO标题和描述，确保关键词不为空
  const keywordText = keyword.keyword.trim()
  if (!keywordText) {
    return {
      title: '关键词分析 | CatchIdeas',
      description: '专业的关键词分析平台，提供用户意图分析、竞争难度评估、市场机会发现等服务',
      robots: 'noindex'
    }
  }

  const title = `${keywordText} - 关键词分析报告 | CatchIdeas`
  const description = `深度分析关键词"${keywordText}"的用户意图、竞争难度、市场机会。获取专业的项目建议和策略指导，发现有价值的互联网项目机会。`
  
  return {
    title,
    description,
    authors: [{ name: "CatchIdeas团队" }],
    creator: "CatchIdeas",
    publisher: "CatchIdeas",
    robots: "index, follow",
    openGraph: {
      type: "article",
      locale: "zh_CN",
      url: `https://catchideas.com/keyword/${params.id}`,
      title,
      description,
      siteName: "CatchIdeas",
    },
    twitter: {
      card: "summary_large_image",
      title,
      description,
    },
    alternates: {
      canonical: `https://catchideas.com/keyword/${params.id}`,
    },
  }
}

export default function KeywordDetailLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return children
}
