/**
 * 关键词详情API
 * 获取单个关键词的完整信息
 */

export default async function handler(req, res) {
  // 只允许GET请求
  if (req.method !== 'GET') {
    return res.status(405).json({
      success: false,
      error: '方法不被允许',
      data: null
    });
  }

  try {
    const { id } = req.query;

    if (!id) {
      return res.status(400).json({
        success: false,
        error: '缺少关键词ID参数',
        data: null
      });
    }

    // 调用PHP API获取关键词详情
    const apiUrl = `${process.env.NEXT_PUBLIC_PHP_API_URL}/keywords.php?id=${encodeURIComponent(id)}`;

    const response = await fetch(apiUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error('PHP API请求失败');
    }

    const data = await response.json();

    if (!data.success) {
      return res.status(404).json({
        success: false,
        error: data.message || '关键词不存在',
        data: null
      });
    }

    // 处理关键词数据，添加计算字段
    const keyword = data.data;

    // 生成简单的推荐域名
    const recommendedDomains = generateSimpleDomain(keyword.keyword);

    const processedKeyword = {
      ...keyword,
      // 添加分析状态
      is_analyzed: !!(keyword.user_intent || keyword.user_pain_point || keyword.competition_level),
      // 添加竞争难度颜色
      competition_color: getCompetitionColor(keyword.competition_score),
      // 添加推荐域名
      recommended_domains: recommendedDomains,
      // 格式化日期
      created_at_formatted: keyword.created_at ? formatDate(keyword.created_at) : null,
      analyzed_at_formatted: keyword.analyzed_at ? formatDate(keyword.analyzed_at) : null
    };

    return res.status(200).json({
      success: true,
      data: processedKeyword,
      message: '成功获取关键词详情'
    });

  } catch (error) {
    console.error('获取关键词详情失败:', error);
    return res.status(500).json({
      success: false,
      error: '服务器内部错误: ' + error.message,
      data: null
    });
  }
}

// 获取竞争难度颜色
function getCompetitionColor(score) {
  if (!score) return '#gray';
  
  const numScore = parseInt(score);
  if (numScore <= 3) return '#22c55e'; // 绿色 - 简单
  if (numScore <= 6) return '#eab308'; // 黄色 - 中等
  return '#ef4444'; // 红色 - 困难
}

// 生成简单的推荐域名
function generateSimpleDomain(keyword) {
  if (!keyword) return [];

  // 清理关键词：移除特殊字符，转小写，移除空格
  const cleanKeyword = keyword.toLowerCase()
    .replace(/[^a-z0-9\s]/g, '')
    .replace(/\s+/g, '');

  // 只返回一个域名：关键词.com
  const domain = `${cleanKeyword}.com`;

  return [{
    domain: domain,
    check_url: `https://wanwang.aliyun.com/domain/searchresult/?keyword=${cleanKeyword}`
  }];
}

// 格式化日期
function formatDate(dateString) {
  if (!dateString) return null;
  
  try {
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  } catch {
    return dateString;
  }
}
