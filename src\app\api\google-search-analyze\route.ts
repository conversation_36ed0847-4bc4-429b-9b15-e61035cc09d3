import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '../../../../pages/api/auth/[...nextauth]'

// 导入Google使用统计追踪器
import googleUsageTracker from '../../../../lib/google-usage-tracker'

export async function POST(request: NextRequest) {
  try {
    // 检查用户登录状态
    const session = await getServerSession(authOptions)

    if (!session || !session.user?.email) {
      return NextResponse.json({
        success: false,
        error: '请先登录Google账户'
      }, { status: 401 })
    }

    const userEmail = session.user.email
    const isAdmin = userEmail === '<EMAIL>'

    const { keyword } = await request.json()

    if (!keyword || typeof keyword !== 'string') {
      return NextResponse.json({
        success: false,
        error: '请提供有效的关键词'
      }, { status: 400 })
    }

    // 检查非管理员用户的使用次数限制
    if (!isAdmin) {
      try {
        const today = new Date().toISOString().split('T')[0]
        const apiBaseUrl = process.env.NEXT_PUBLIC_PHP_API_URL || 'https://api.xstty.com/api'
        const usageResponse = await fetch(`${apiBaseUrl}/user-usage.php?email=${encodeURIComponent(userEmail)}&date=${today}`)

        if (usageResponse.ok) {
          const usageData = await usageResponse.json()
          const dailyUsage = usageData.success && usageData.data ? (usageData.data.usage_count || 0) : 0

          if (dailyUsage >= 10) {
            return NextResponse.json({
              success: false,
              error: '今日使用次数已达上限（10次），请明天再试'
            }, { status: 429 })
          }
        }
      } catch (error) {
        // 如果检查失败，继续执行（容错处理）
      }
    }

    // 获取可用的Google API密钥
    let keyIndex: number
    let selectedKey: string
    let searchEngineId: string

    try {
      // 使用Google使用统计追踪器获取最佳密钥
      keyIndex = await googleUsageTracker.getAvailableKey(100) // 100次/天限制
      selectedKey = process.env[`GOOGLE_SEARCH_API_KEY_${keyIndex}`] || ''
      searchEngineId = process.env[`GOOGLE_SEARCH_CX_${keyIndex}`] || ''

      // 如果指定密钥不存在，尝试找到第一个可用的密钥
      if (!selectedKey || !searchEngineId) {
        for (let i = 1; i <= 20; i++) {
          const apiKey = process.env[`GOOGLE_SEARCH_API_KEY_${i}`]
          const cx = process.env[`GOOGLE_SEARCH_CX_${i}`]
          if (apiKey && cx) {
            keyIndex = i
            selectedKey = apiKey
            searchEngineId = cx
            break
          }
        }
      }

      if (!selectedKey || !searchEngineId) {
        return NextResponse.json({
          success: false,
          error: 'Google API配置不完整，请检查环境变量'
        }, { status: 500 })
      }
    } catch (error) {
      // 如果获取密钥失败，使用随机密钥作为fallback
      keyIndex = Math.floor(Math.random() * 20) + 1
      selectedKey = process.env[`GOOGLE_SEARCH_API_KEY_${keyIndex}`] || ''
      searchEngineId = process.env[`GOOGLE_SEARCH_CX_${keyIndex}`] || ''

      if (!selectedKey || !searchEngineId) {
        return NextResponse.json({
          success: false,
          error: 'Google API配置不完整'
        }, { status: 500 })
      }
    }

    // 构建Google Custom Search API请求URL
    const searchUrl = `https://www.googleapis.com/customsearch/v1?key=${selectedKey}&cx=${searchEngineId}&q=${encodeURIComponent(keyword)}&num=10`

    // 创建超时控制器
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), 15000) // 15秒超时

    try {
      // 发送API请求
      const response = await fetch(searchUrl, {
        signal: controller.signal,
        headers: {
          'User-Agent': 'CatchIdeas-SearchAnalyzer/1.0'
        }
      })

      clearTimeout(timeoutId)

      if (!response.ok) {
        const errorText = await response.text()
        
        // 如果是403错误且包含suspended信息，标记密钥为超限
        if (response.status === 403 && errorText.includes('suspended')) {
          try {
            await googleUsageTracker.recordUsage(keyIndex, 100)
          } catch (recordError) {
            // 静默处理记录错误
          }
        }

        return NextResponse.json({
          success: false,
          error: '搜索服务暂时不可用，请稍后重试'
        }, { status: 500 })
      }

      const data = await response.json()

      // 记录正常使用
      try {
        await googleUsageTracker.recordUsage(keyIndex, 1)
      } catch (recordError) {
        // 静默处理记录错误
      }

      // 检查返回数据格式
      if (!data.searchInformation) {
        return NextResponse.json({
          success: false,
          error: 'Google API返回数据格式异常'
        }, { status: 500 })
      }

      // 处理搜索结果数据
      const processedData = {
        searchInformation: {
          formattedTotalResults: data.searchInformation.formattedTotalResults || '0',
          searchTime: data.searchInformation.searchTime || 0
        },
        items: (data.items || []).map((item: any) => {
          // 过滤并保留重要的meta标签
          const filteredMetatags = (item.pagemap?.metatags || []).map((metatag: any) => {
            const importantFields: any = {}

            // 核心SEO字段
            if (metatag['og:title']) importantFields['og:title'] = metatag['og:title']
            if (metatag['og:description']) importantFields['og:description'] = metatag['og:description']
            if (metatag['og:image']) importantFields['og:image'] = metatag['og:image']
            if (metatag['og:type']) importantFields['og:type'] = metatag['og:type']
            if (metatag['og:image:width']) importantFields['og:image:width'] = metatag['og:image:width']
            if (metatag['og:image:height']) importantFields['og:image:height'] = metatag['og:image:height']

            // Twitter卡片
            if (metatag['twitter:title']) importantFields['twitter:title'] = metatag['twitter:title']
            if (metatag['twitter:description']) importantFields['twitter:description'] = metatag['twitter:description']
            if (metatag['twitter:card']) importantFields['twitter:card'] = metatag['twitter:card']
            if (metatag['twitter:image']) importantFields['twitter:image'] = metatag['twitter:image']

            // 基础技术标签
            if (metatag['viewport']) importantFields['viewport'] = metatag['viewport']
            if (metatag['theme-color']) importantFields['theme-color'] = metatag['theme-color']
            if (metatag['description']) importantFields['description'] = metatag['description']
            if (metatag['keywords']) importantFields['keywords'] = metatag['keywords']
            if (metatag['author']) importantFields['author'] = metatag['author']

            return importantFields
          }).filter((metatag: any) => Object.keys(metatag).length > 0)

          return {
            title: item.title || '无标题',
            link: item.link || '',
            displayLink: item.displayLink || '',
            snippet: item.snippet || '无摘要',
            pagemap: {
              metatags: filteredMetatags,
              cse_image: item.pagemap?.cse_image || []
            }
          }
        })
      }

      // 记录用户使用次数（非管理员）
      if (!isAdmin) {
        try {
          const today = new Date().toISOString().split('T')[0]
          const apiBaseUrl = process.env.NEXT_PUBLIC_PHP_API_URL || 'https://api.xstty.com/api'
          await fetch(`${apiBaseUrl}/user-usage.php`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              email: userEmail,
              count: 1,
              date: today
            })
          })
        } catch (error) {
          // 静默处理记录错误
        }
      }

      return NextResponse.json({
        success: true,
        data: processedData,
        meta: {
          keyword,
          keyIndex,
          timestamp: new Date().toISOString(),
          userEmail,
          isAdmin
        }
      })

    } catch (fetchError: any) {
      clearTimeout(timeoutId)
      
      // 即使请求失败，也记录使用次数
      try {
        await googleUsageTracker.recordUsage(keyIndex, 1)
      } catch (recordError) {
        // 静默处理记录错误
      }

      if (fetchError.name === 'AbortError') {
        return NextResponse.json({
          success: false,
          error: '请求超时，请稍后重试'
        }, { status: 408 })
      }

      return NextResponse.json({
        success: false,
        error: `网络请求失败: ${fetchError.message}`
      }, { status: 500 })
    }

  } catch (error: any) {
    console.error('Google search analyze error:', error)
    
    return NextResponse.json({
      success: false,
      error: '服务器内部错误'
    }, { status: 500 })
  }
}

export async function GET() {
  return NextResponse.json({
    success: false,
    error: '请使用POST方法'
  }, { status: 405 })
}
