'use client'

import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card'
import { Badge } from '../../components/ui/badge'
import { Button } from '../../components/ui/button'
import { Input } from '../../components/ui/input'
import { Label } from '../../components/ui/label'

import { Alert, AlertDescription } from '../../components/ui/alert'
import { Calculator, TrendingUp, Users, DollarSign, Download, AlertTriangle, CheckCircle, XCircle } from 'lucide-react'
import FrontendHeader from '../../components/frontend-header'
import FrontendFooter from '../../components/frontend-footer'

interface CalculationResults {
  // 转化漏斗
  ctr: number
  visitorConversionRate: number
  registrationConversionRate: number
  paymentConversionRate: number
  overallConversionRate: number
  
  // 成本指标
  cpc: number
  cpa: number
  cac: number
  cpm: number
  
  // 价值指标
  aov: number
  ltv: number
  subscriptionLtv: number
  customerLifecycle: number
  
  // 投资回报
  roi: number
  roas: number
  ltvCacRatio: number
  breakEvenTime: number
}

export default function MarketingROICalculatorPage() {
  // 基础数据输入
  const [adSpend, setAdSpend] = useState<string>('')
  const [timePeriod, setTimePeriod] = useState<string>('30')
  const [impressions, setImpressions] = useState<string>('')
  const [clicks, setClicks] = useState<string>('')
  const [visitors, setVisitors] = useState<string>('')
  const [registrations, setRegistrations] = useState<string>('')
  const [paidCustomers, setPaidCustomers] = useState<string>('')
  
  // 收入数据
  const [totalRevenue, setTotalRevenue] = useState<string>('')
  const [totalOrders, setTotalOrders] = useState<string>('')
  const [monthlyRevenue, setMonthlyRevenue] = useState<string>('')
  const [grossMargin, setGrossMargin] = useState<string>('70')
  const [monthlyChurnRate, setMonthlyChurnRate] = useState<string>('5')
  const [purchaseFrequency, setPurchaseFrequency] = useState<string>('2')
  
  // 计算结果
  const [results, setResults] = useState<CalculationResults | null>(null)

  // 数值转换辅助函数
  const parseNumber = (value: string): number => {
    if (!value) return 0
    const num = parseFloat(value.replace(/,/g, ''))
    return isNaN(num) ? 0 : num
  }

  // 计算所有指标
  const calculateMetrics = (): CalculationResults => {
    const adSpendNum = parseNumber(adSpend)
    const impressionsNum = parseNumber(impressions)
    const clicksNum = parseNumber(clicks)
    const visitorsNum = parseNumber(visitors)
    const registrationsNum = parseNumber(registrations)
    const paidCustomersNum = parseNumber(paidCustomers)
    const totalRevenueNum = parseNumber(totalRevenue)
    const totalOrdersNum = parseNumber(totalOrders)
    const monthlyRevenueNum = parseNumber(monthlyRevenue)
    const grossMarginNum = parseNumber(grossMargin) / 100
    const monthlyChurnRateNum = parseNumber(monthlyChurnRate) / 100
    const purchaseFrequencyNum = parseNumber(purchaseFrequency)

    // 转化漏斗计算
    const ctr = impressionsNum > 0 ? (clicksNum / impressionsNum) * 100 : 0
    const visitorConversionRate = clicksNum > 0 ? (visitorsNum / clicksNum) * 100 : 0
    const registrationConversionRate = visitorsNum > 0 ? (registrationsNum / visitorsNum) * 100 : 0
    const paymentConversionRate = registrationsNum > 0 ? (paidCustomersNum / registrationsNum) * 100 : 0
    const overallConversionRate = impressionsNum > 0 ? (paidCustomersNum / impressionsNum) * 100 : 0

    // 成本指标计算
    const cpc = clicksNum > 0 ? adSpendNum / clicksNum : 0
    const cpa = paidCustomersNum > 0 ? adSpendNum / paidCustomersNum : 0
    const cac = paidCustomersNum > 0 ? adSpendNum / paidCustomersNum : 0
    const cpm = impressionsNum > 0 ? (adSpendNum / impressionsNum) * 1000 : 0

    // 价值指标计算
    const aov = totalOrdersNum > 0 ? totalRevenueNum / totalOrdersNum : 0
    const customerLifecycle = monthlyChurnRateNum > 0 ? 1 / monthlyChurnRateNum : 0
    const ltv = aov * purchaseFrequencyNum * customerLifecycle
    const subscriptionLtv = monthlyChurnRateNum > 0 ? (monthlyRevenueNum * grossMarginNum) / monthlyChurnRateNum : 0

    // 投资回报计算
    const roi = adSpendNum > 0 ? ((totalRevenueNum - adSpendNum) / adSpendNum) * 100 : 0
    const roas = adSpendNum > 0 ? totalRevenueNum / adSpendNum : 0
    const ltvCacRatio = cac > 0 ? ltv / cac : 0
    const monthlyProfit = monthlyRevenueNum * grossMarginNum
    const breakEvenTime = monthlyProfit > 0 ? cac / monthlyProfit : 0

    return {
      ctr, visitorConversionRate, registrationConversionRate, paymentConversionRate, overallConversionRate,
      cpc, cpa, cac, cpm,
      aov, ltv, subscriptionLtv, customerLifecycle,
      roi, roas, ltvCacRatio, breakEvenTime
    }
  }

  // 执行计算
  const handleCalculate = () => {
    const calculatedResults = calculateMetrics()
    setResults(calculatedResults)
  }

  // 获取健康度评估
  const getHealthStatus = (metric: string, value: number) => {
    switch (metric) {
      case 'ltvCacRatio':
        if (value >= 3) return { status: 'healthy', color: 'text-green-600', icon: CheckCircle }
        if (value >= 1) return { status: 'warning', color: 'text-yellow-600', icon: AlertTriangle }
        return { status: 'danger', color: 'text-red-600', icon: XCircle }
      case 'roi':
        if (value >= 100) return { status: 'healthy', color: 'text-green-600', icon: CheckCircle }
        if (value >= 0) return { status: 'warning', color: 'text-yellow-600', icon: AlertTriangle }
        return { status: 'danger', color: 'text-red-600', icon: XCircle }
      case 'roas':
        if (value >= 4) return { status: 'healthy', color: 'text-green-600', icon: CheckCircle }
        if (value >= 2) return { status: 'warning', color: 'text-yellow-600', icon: AlertTriangle }
        return { status: 'danger', color: 'text-red-600', icon: XCircle }
      default:
        return { status: 'neutral', color: 'text-gray-600', icon: CheckCircle }
    }
  }

  // 导出CSV
  const exportToCSV = () => {
    if (!results) return

    const csvData = [
      ['指标类别', '指标名称', '数值', '单位', '健康状态'],
      ['转化漏斗', '点击率(CTR)', results.ctr.toFixed(2), '%', ''],
      ['转化漏斗', '访客转化率', results.visitorConversionRate.toFixed(2), '%', ''],
      ['转化漏斗', '注册转化率', results.registrationConversionRate.toFixed(2), '%', ''],
      ['转化漏斗', '付费转化率', results.paymentConversionRate.toFixed(2), '%', ''],
      ['转化漏斗', '整体转化率', results.overallConversionRate.toFixed(2), '%', ''],
      ['成本指标', 'CPC(每次点击成本)', results.cpc.toFixed(2), '元', ''],
      ['成本指标', 'CPA(每次行动成本)', results.cpa.toFixed(2), '元', ''],
      ['成本指标', 'CAC(客户获取成本)', results.cac.toFixed(2), '元', ''],
      ['成本指标', 'CPM(千次展示成本)', results.cpm.toFixed(2), '元', ''],
      ['价值指标', 'AOV(平均订单价值)', results.aov.toFixed(2), '元', ''],
      ['价值指标', 'LTV(客户终身价值)', results.ltv.toFixed(2), '元', ''],
      ['价值指标', '订阅LTV', results.subscriptionLtv.toFixed(2), '元', ''],
      ['价值指标', '客户生命周期', results.customerLifecycle.toFixed(1), '月', ''],
      ['投资回报', 'ROI(投资回报率)', results.roi.toFixed(2), '%', getHealthStatus('roi', results.roi).status],
      ['投资回报', 'ROAS(广告支出回报率)', results.roas.toFixed(2), '倍', getHealthStatus('roas', results.roas).status],
      ['投资回报', 'LTV/CAC比值', results.ltvCacRatio.toFixed(2), '倍', getHealthStatus('ltvCacRatio', results.ltvCacRatio).status],
      ['投资回报', '盈亏平衡时间', results.breakEvenTime.toFixed(1), '月', ''],
      ['', '', '', '', ''],
      ['计算时间', new Date().toLocaleString(), '', '', ''],
      ['数据来源', 'CatchIdeas营销ROI计算器', '', '', ''],
    ]

    const csvContent = csvData.map(row => row.map(cell => `"${cell}"`).join(',')).join('\n')
    const blob = new Blob(['\uFEFF' + csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', `营销ROI分析_${new Date().toISOString().split('T')[0]}.csv`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  return (
    <div className="min-h-screen bg-background">
      <FrontendHeader />
      
      {/* 页面标题 */}
      <section className="py-8 px-4 bg-gradient-to-r from-blue-50 to-purple-50 border-b">
        <div className="container mx-auto text-center">
          <div className="flex items-center justify-center mb-4">
            <div className="p-3 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl mr-4">
              <Calculator className="h-8 w-8 text-white" />
            </div>
            <h1 className="text-4xl font-bold text-gray-900">营销ROI综合计算器</h1>
          </div>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            从广告投入到客户价值的全链路分析，数据驱动的营销决策支持工具
          </p>
        </div>
      </section>

      {/* 主要内容区域 */}
      <section className="py-8 px-4">
        <div className="container mx-auto max-w-7xl">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* 数据输入区 */}
                <div className="space-y-6">
                  {/* 基础数据 */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center">
                        <DollarSign className="h-5 w-5 mr-2 text-blue-600" />
                        基础数据
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <Label htmlFor="adSpend">广告费用 (元)</Label>
                          <Input
                            id="adSpend"
                            type="text"
                            placeholder="10000"
                            value={adSpend}
                            onChange={(e) => setAdSpend(e.target.value)}
                            className="h-12 text-lg border-2 border-purple-200 focus:border-purple-500"
                          />
                        </div>
                        <div>
                          <Label htmlFor="timePeriod">时间周期 (天)</Label>
                          <Input
                            id="timePeriod"
                            type="text"
                            placeholder="30"
                            value={timePeriod}
                            onChange={(e) => setTimePeriod(e.target.value)}
                            className="h-12 text-lg border-2 border-purple-200 focus:border-purple-500"
                          />
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* 流量数据 */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center">
                        <Users className="h-5 w-5 mr-2 text-green-600" />
                        流量数据
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <Label htmlFor="impressions">展示次数</Label>
                          <Input
                            id="impressions"
                            type="text"
                            placeholder="100000"
                            value={impressions}
                            onChange={(e) => setImpressions(e.target.value)}
                            className="h-12 text-lg border-2 border-green-200 focus:border-green-500"
                          />
                        </div>
                        <div>
                          <Label htmlFor="clicks">点击次数</Label>
                          <Input
                            id="clicks"
                            type="text"
                            placeholder="2000"
                            value={clicks}
                            onChange={(e) => setClicks(e.target.value)}
                            className="h-12 text-lg border-2 border-green-200 focus:border-green-500"
                          />
                        </div>
                        <div>
                          <Label htmlFor="visitors">访客数</Label>
                          <Input
                            id="visitors"
                            type="text"
                            placeholder="1800"
                            value={visitors}
                            onChange={(e) => setVisitors(e.target.value)}
                            className="h-12 text-lg border-2 border-green-200 focus:border-green-500"
                          />
                        </div>
                        <div>
                          <Label htmlFor="registrations">注册数</Label>
                          <Input
                            id="registrations"
                            type="text"
                            placeholder="360"
                            value={registrations}
                            onChange={(e) => setRegistrations(e.target.value)}
                            className="h-12 text-lg border-2 border-green-200 focus:border-green-500"
                          />
                        </div>
                        <div>
                          <Label htmlFor="paidCustomers">付费客户数</Label>
                          <Input
                            id="paidCustomers"
                            type="text"
                            placeholder="72"
                            value={paidCustomers}
                            onChange={(e) => setPaidCustomers(e.target.value)}
                            className="h-12 text-lg border-2 border-green-200 focus:border-green-500"
                          />
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* 收入数据 */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center">
                        <TrendingUp className="h-5 w-5 mr-2 text-purple-600" />
                        收入数据
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <Label htmlFor="totalRevenue">总收入 (元)</Label>
                          <Input
                            id="totalRevenue"
                            type="text"
                            placeholder="50000"
                            value={totalRevenue}
                            onChange={(e) => setTotalRevenue(e.target.value)}
                            className="h-12 text-lg border-2 border-purple-200 focus:border-purple-500"
                          />
                        </div>
                        <div>
                          <Label htmlFor="totalOrders">总订单数</Label>
                          <Input
                            id="totalOrders"
                            type="text"
                            placeholder="100"
                            value={totalOrders}
                            onChange={(e) => setTotalOrders(e.target.value)}
                            className="h-12 text-lg border-2 border-purple-200 focus:border-purple-500"
                          />
                        </div>
                        <div>
                          <Label htmlFor="monthlyRevenue">月均收入 (元)</Label>
                          <Input
                            id="monthlyRevenue"
                            type="text"
                            placeholder="15000"
                            value={monthlyRevenue}
                            onChange={(e) => setMonthlyRevenue(e.target.value)}
                            className="h-12 text-lg border-2 border-purple-200 focus:border-purple-500"
                          />
                        </div>
                        <div>
                          <Label htmlFor="grossMargin">毛利率 (%)</Label>
                          <Input
                            id="grossMargin"
                            type="text"
                            placeholder="70"
                            value={grossMargin}
                            onChange={(e) => setGrossMargin(e.target.value)}
                            className="h-12 text-lg border-2 border-purple-200 focus:border-purple-500"
                          />
                        </div>
                        <div>
                          <Label htmlFor="monthlyChurnRate">月流失率 (%)</Label>
                          <Input
                            id="monthlyChurnRate"
                            type="text"
                            placeholder="5"
                            value={monthlyChurnRate}
                            onChange={(e) => setMonthlyChurnRate(e.target.value)}
                            className="h-12 text-lg border-2 border-purple-200 focus:border-purple-500"
                          />
                        </div>
                        <div>
                          <Label htmlFor="purchaseFrequency">购买频率 (次/月)</Label>
                          <Input
                            id="purchaseFrequency"
                            type="text"
                            placeholder="2"
                            value={purchaseFrequency}
                            onChange={(e) => setPurchaseFrequency(e.target.value)}
                            className="h-12 text-lg border-2 border-purple-200 focus:border-purple-500"
                          />
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* 计算按钮 */}
                  <Card>
                    <CardContent className="pt-6">
                      <Button
                        onClick={handleCalculate}
                        className="w-full h-12 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white font-semibold"
                      >
                        <Calculator className="h-4 w-4 mr-2" />
                        开始计算ROI
                      </Button>
                    </CardContent>
                  </Card>
                </div>

                {/* 计算结果区 */}
                <div className="space-y-6">
                  {results && (
                    <>
                      {/* 关键指标概览 */}
                      <Card>
                        <CardHeader>
                          <CardTitle className="flex items-center">
                            <TrendingUp className="h-5 w-5 mr-2 text-green-600" />
                            关键指标概览
                          </CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="grid grid-cols-2 gap-4">
                            <div className="text-center p-4 bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg">
                              <div className="text-2xl font-bold text-green-600">
                                {results.roi.toFixed(1)}%
                              </div>
                              <div className="text-sm text-gray-600">ROI 投资回报率</div>
                              <div className="flex items-center justify-center mt-1">
                                {(() => {
                                  const health = getHealthStatus('roi', results.roi)
                                  const Icon = health.icon
                                  return <Icon className={`h-4 w-4 ${health.color}`} />
                                })()}
                              </div>
                            </div>
                            <div className="text-center p-4 bg-gradient-to-r from-blue-50 to-cyan-50 rounded-lg">
                              <div className="text-2xl font-bold text-blue-600">
                                {results.roas.toFixed(1)}
                              </div>
                              <div className="text-sm text-gray-600">ROAS 广告回报率</div>
                              <div className="flex items-center justify-center mt-1">
                                {(() => {
                                  const health = getHealthStatus('roas', results.roas)
                                  const Icon = health.icon
                                  return <Icon className={`h-4 w-4 ${health.color}`} />
                                })()}
                              </div>
                            </div>
                            <div className="text-center p-4 bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg">
                              <div className="text-2xl font-bold text-purple-600">
                                {results.ltvCacRatio.toFixed(1)}:1
                              </div>
                              <div className="text-sm text-gray-600">LTV/CAC 比值</div>
                              <div className="flex items-center justify-center mt-1">
                                {(() => {
                                  const health = getHealthStatus('ltvCacRatio', results.ltvCacRatio)
                                  const Icon = health.icon
                                  return <Icon className={`h-4 w-4 ${health.color}`} />
                                })()}
                              </div>
                            </div>
                            <div className="text-center p-4 bg-gradient-to-r from-orange-50 to-red-50 rounded-lg">
                              <div className="text-2xl font-bold text-orange-600">
                                {results.breakEvenTime.toFixed(1)}
                              </div>
                              <div className="text-sm text-gray-600">盈亏平衡 (月)</div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>

                      {/* 转化漏斗分析 */}
                      <Card>
                        <CardHeader>
                          <CardTitle>转化漏斗分析</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="space-y-3">
                            <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                              <span className="font-medium">点击率 (CTR)</span>
                              <Badge variant="outline">{results.ctr.toFixed(2)}%</Badge>
                            </div>
                            <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                              <span className="font-medium">访客转化率</span>
                              <Badge variant="outline">{results.visitorConversionRate.toFixed(2)}%</Badge>
                            </div>
                            <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                              <span className="font-medium">注册转化率</span>
                              <Badge variant="outline">{results.registrationConversionRate.toFixed(2)}%</Badge>
                            </div>
                            <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                              <span className="font-medium">付费转化率</span>
                              <Badge variant="outline">{results.paymentConversionRate.toFixed(2)}%</Badge>
                            </div>
                            <div className="flex justify-between items-center p-3 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border-2 border-blue-200">
                              <span className="font-bold">整体转化率</span>
                              <Badge className="bg-gradient-to-r from-blue-500 to-purple-600 text-white">
                                {results.overallConversionRate.toFixed(4)}%
                              </Badge>
                            </div>
                          </div>
                        </CardContent>
                      </Card>

                      {/* 成本与价值分析 */}
                      <Card>
                        <CardHeader>
                          <CardTitle>成本与价值分析</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="grid grid-cols-2 gap-4">
                            <div className="space-y-3">
                              <h4 className="font-semibold text-red-600">成本指标</h4>
                              <div className="space-y-2">
                                <div className="flex justify-between">
                                  <span className="text-sm">CPC (每次点击)</span>
                                  <span className="font-medium">¥{results.cpc.toFixed(2)}</span>
                                </div>
                                <div className="flex justify-between">
                                  <span className="text-sm">CPA (每次行动)</span>
                                  <span className="font-medium">¥{results.cpa.toFixed(2)}</span>
                                </div>
                                <div className="flex justify-between">
                                  <span className="text-sm">CAC (客户获取)</span>
                                  <span className="font-medium">¥{results.cac.toFixed(2)}</span>
                                </div>
                                <div className="flex justify-between">
                                  <span className="text-sm">CPM (千次展示)</span>
                                  <span className="font-medium">¥{results.cpm.toFixed(2)}</span>
                                </div>
                              </div>
                            </div>
                            <div className="space-y-3">
                              <h4 className="font-semibold text-green-600">价值指标</h4>
                              <div className="space-y-2">
                                <div className="flex justify-between">
                                  <span className="text-sm">AOV (平均订单)</span>
                                  <span className="font-medium">¥{results.aov.toFixed(2)}</span>
                                </div>
                                <div className="flex justify-between">
                                  <span className="text-sm">LTV (终身价值)</span>
                                  <span className="font-medium">¥{results.ltv.toFixed(2)}</span>
                                </div>
                                <div className="flex justify-between">
                                  <span className="text-sm">订阅 LTV</span>
                                  <span className="font-medium">¥{results.subscriptionLtv.toFixed(2)}</span>
                                </div>
                                <div className="flex justify-between">
                                  <span className="text-sm">客户生命周期</span>
                                  <span className="font-medium">{results.customerLifecycle.toFixed(1)}月</span>
                                </div>
                              </div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>

                      {/* 导出功能 */}
                      <Card>
                        <CardContent className="pt-6">
                          <Button
                            onClick={exportToCSV}
                            variant="outline"
                            className="w-full h-12 border-2 border-gray-300 hover:border-gray-500"
                          >
                            <Download className="h-4 w-4 mr-2" />
                            导出CSV数据
                          </Button>
                        </CardContent>
                      </Card>
                    </>
                  )}

                  {!results && (
                    <Card>
                      <CardContent className="text-center py-12">
                        <Calculator className="h-16 w-16 mx-auto text-gray-400 mb-4" />
                        <h3 className="text-xl font-semibold text-gray-600 mb-2">等待计算</h3>
                        <p className="text-gray-500">
                          请填写左侧数据并点击"开始计算ROI"按钮
                        </p>
                      </CardContent>
                    </Card>
                  )}
                </div>
              </div>
        </div>
      </section>

      <FrontendFooter />
    </div>
  )
}
