import type { Metadata } from "next";

export const metadata: Metadata = {
  title: "Sitemap检测工具 | CatchIdeas",
  description: "批量检测网站sitemap.txt文件的专业工具，支持单个和批量域名检测，自动验证sitemap文件存在性，一键下载所有可用文件，助力SEO分析和网站优化。",
  authors: [{ name: "CatchIdeas团队" }],
  creator: "CatchIdeas",
  publisher: "CatchIdeas",
  robots: "index, follow",
  keywords: [
    "Sitemap检测",
    "sitemap.txt检测",
    "网站地图检测",
    "批量sitemap检测",
    "SEO工具",
    "网站分析工具",
    "域名批量检测",
    "sitemap下载",
    "网站优化工具",
    "SEO分析"
  ],
  openGraph: {
    type: "website",
    locale: "zh_CN",
    url: "https://catchideas.com/sitemap-checker",
    title: "Sitemap检测工具 | CatchIdeas",
    description: "批量检测网站sitemap.txt文件的专业工具，支持单个和批量域名检测，自动验证sitemap文件存在性，一键下载所有可用文件，助力SEO分析和网站优化。",
    siteName: "CatchIdeas",
  },
  twitter: {
    card: "summary_large_image",
    title: "Sitemap检测工具 | CatchIdeas",
    description: "批量检测网站sitemap.txt文件的专业工具，支持单个和批量域名检测，自动验证sitemap文件存在性，一键下载所有可用文件，助力SEO分析和网站优化。",
  },
  alternates: {
    canonical: "https://catchideas.com/sitemap-checker",
  },
};

export default function SitemapCheckerLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}
