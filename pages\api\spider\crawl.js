/**
 * 谷歌蜘蛛模拟抓取API
 * 模拟谷歌蜘蛛访问指定URL，获取页面信息和HTML内容
 */

import { JSDOM } from 'jsdom'

export default async function handler(req, res) {
  // 设置CORS头
  res.setHeader('Access-Control-Allow-Origin', '*')
  res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS')
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type')

  if (req.method === 'OPTIONS') {
    return res.status(200).end()
  }

  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      error: '不支持的请求方法，请使用POST'
    })
  }

  try {
    const { url } = req.body

    if (!url) {
      return res.status(400).json({
        success: false,
        error: '缺少URL参数'
      })
    }

    // 验证URL格式
    let targetUrl
    try {
      targetUrl = new URL(url)
    } catch {
      return res.status(400).json({
        success: false,
        error: '无效的URL格式'
      })
    }

    // 模拟谷歌蜘蛛的User-Agent
    const googleBotUserAgent = 'Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)'
    
    const startTime = Date.now()

    // 发起HTTP请求
    const response = await fetch(targetUrl.toString(), {
      method: 'GET',
      headers: {
        'User-Agent': googleBotUserAgent,
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate',
        'DNT': '1',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
      },
      timeout: 10000, // 10秒超时，Vercel限制
    })

    const endTime = Date.now()
    const responseTime = endTime - startTime

    // 获取响应内容
    const htmlContent = await response.text()
    const contentLength = Buffer.byteLength(htmlContent, 'utf8')

    // 解析HTML，提取SEO信息
    const dom = new JSDOM(htmlContent)
    const document = dom.window.document

    // 提取页面信息
    const title = document.querySelector('title')?.textContent?.trim() || ''

    const descriptionMeta = document.querySelector('meta[name="description"]') ||
                           document.querySelector('meta[property="og:description"]')
    const description = descriptionMeta?.getAttribute('content')?.trim() || ''

    const keywordsMeta = document.querySelector('meta[name="keywords"]')
    const keywords = keywordsMeta?.getAttribute('content')?.trim() || ''

    // 提取更多SEO信息
    const ogTitle = document.querySelector('meta[property="og:title"]')?.getAttribute('content')?.trim() || ''
    const ogImage = document.querySelector('meta[property="og:image"]')?.getAttribute('content')?.trim() || ''
    const ogUrl = document.querySelector('meta[property="og:url"]')?.getAttribute('content')?.trim() || ''
    const ogType = document.querySelector('meta[property="og:type"]')?.getAttribute('content')?.trim() || ''

    const twitterCard = document.querySelector('meta[name="twitter:card"]')?.getAttribute('content')?.trim() || ''
    const twitterTitle = document.querySelector('meta[name="twitter:title"]')?.getAttribute('content')?.trim() || ''
    const twitterDescription = document.querySelector('meta[name="twitter:description"]')?.getAttribute('content')?.trim() || ''
    const twitterImage = document.querySelector('meta[name="twitter:image"]')?.getAttribute('content')?.trim() || ''

    const canonical = document.querySelector('link[rel="canonical"]')?.getAttribute('href')?.trim() || ''
    const robots = document.querySelector('meta[name="robots"]')?.getAttribute('content')?.trim() || ''
    const viewport = document.querySelector('meta[name="viewport"]')?.getAttribute('content')?.trim() || ''
    const charset = document.querySelector('meta[charset]')?.getAttribute('charset') ||
                   document.querySelector('meta[http-equiv="Content-Type"]')?.getAttribute('content')?.match(/charset=([^;]+)/)?.[1] || ''

    // 统计页面元素
    const h1Count = document.querySelectorAll('h1').length
    const h2Count = document.querySelectorAll('h2').length
    const h3Count = document.querySelectorAll('h3').length
    const imgCount = document.querySelectorAll('img').length
    const linkCount = document.querySelectorAll('a').length
    const internalLinks = Array.from(document.querySelectorAll('a[href]')).filter(a => {
      const href = a.getAttribute('href')
      return href && (href.startsWith('/') || href.includes(targetUrl.hostname))
    }).length
    const externalLinks = linkCount - internalLinks

    // 获取响应头信息
    const headers = {}
    response.headers.forEach((value, key) => {
      headers[key] = value
    })

    // 构建结果
    const result = {
      url: targetUrl.toString(),
      title,
      description,
      keywords,
      statusCode: response.status,
      responseTime,
      contentLength,
      htmlContent,
      headers,
      seo: {
        ogTitle,
        ogImage,
        ogUrl,
        ogType,
        twitterCard,
        twitterTitle,
        twitterDescription,
        twitterImage,
        canonical,
        robots,
        viewport,
        charset
      },
      stats: {
        h1Count,
        h2Count,
        h3Count,
        imgCount,
        linkCount,
        internalLinks,
        externalLinks
      },
      timestamp: new Date().toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    }

    return res.status(200).json({
      success: true,
      data: result,
      message: '页面抓取成功'
    })

  } catch (error) {
    console.error('Spider crawl error:', error)

    // 处理不同类型的错误
    let errorMessage = '抓取失败'
    
    if (error.code === 'ENOTFOUND') {
      errorMessage = '域名解析失败，请检查URL是否正确'
    } else if (error.code === 'ECONNREFUSED') {
      errorMessage = '连接被拒绝，目标服务器可能不可用'
    } else if (error.code === 'ETIMEDOUT') {
      errorMessage = '请求超时，目标服务器响应过慢'
    } else if (error.name === 'AbortError') {
      errorMessage = '请求被中止，可能是网络问题'
    } else if (error.message) {
      errorMessage = error.message
    }

    return res.status(500).json({
      success: false,
      error: errorMessage,
      details: process.env.NODE_ENV === 'development' ? error.stack : undefined
    })
  }
}

// 配置API路由
export const config = {
  api: {
    bodyParser: {
      sizeLimit: '1mb',
    },
    responseLimit: '10mb', // 允许较大的响应，因为HTML内容可能很大
  },
}
