<?php
require_once __DIR__ . '/../config/database.php';

class KeywordReport {
    private $conn;
    private $table_name = "keyword_reports";

    public function __construct() {
        $database = new Database();
        $this->conn = $database->getConnection();
    }

    /**
     * 创建关键词报告
     */
    public function create($data) {
        $query = "INSERT INTO " . $this->table_name . "
                  (keyword_id, related_keywords, similarity_analysis, search_volume_estimate,
                   competition_intensity, market_opportunity, google_search_results, search_metrics,
                   top_competitors_analysis, title_patterns_analysis, content_gap_analysis,
                   ai_analysis, recommendation_score, action_suggestions, top_competitors,
                   competitive_advantages, content_strategy, target_audience, monetization_potential,
                   seo_difficulty, trend_analysis, seasonal_patterns, analysis_model,
                   analysis_version, confidence_score)
                  VALUES
                  (:keyword_id, :related_keywords, :similarity_analysis, :search_volume_estimate,
                   :competition_intensity, :market_opportunity, :google_search_results, :search_metrics,
                   :top_competitors_analysis, :title_patterns_analysis, :content_gap_analysis,
                   :ai_analysis, :recommendation_score, :action_suggestions, :top_competitors,
                   :competitive_advantages, :content_strategy, :target_audience, :monetization_potential,
                   :seo_difficulty, :trend_analysis, :seasonal_patterns, :analysis_model,
                   :analysis_version, :confidence_score)";

        $stmt = $this->conn->prepare($query);

        // 绑定参数
        $stmt->bindParam(":keyword_id", $data['keyword_id']);
        $stmt->bindParam(":related_keywords", $data['related_keywords']);
        $stmt->bindParam(":similarity_analysis", $data['similarity_analysis']);
        $stmt->bindParam(":search_volume_estimate", $data['search_volume_estimate']);
        $stmt->bindParam(":competition_intensity", $data['competition_intensity']);
        $stmt->bindParam(":market_opportunity", $data['market_opportunity']);
        $stmt->bindParam(":google_search_results", $data['google_search_results']);
        $stmt->bindParam(":search_metrics", $data['search_metrics']);
        $stmt->bindParam(":top_competitors_analysis", $data['top_competitors_analysis']);
        $stmt->bindParam(":title_patterns_analysis", $data['title_patterns_analysis']);
        $stmt->bindParam(":content_gap_analysis", $data['content_gap_analysis']);
        $stmt->bindParam(":ai_analysis", $data['ai_analysis']);
        $stmt->bindParam(":recommendation_score", $data['recommendation_score']);
        $stmt->bindParam(":action_suggestions", $data['action_suggestions']);
        $stmt->bindParam(":top_competitors", $data['top_competitors']);
        $stmt->bindParam(":competitive_advantages", $data['competitive_advantages']);
        $stmt->bindParam(":content_strategy", $data['content_strategy']);
        $stmt->bindParam(":target_audience", $data['target_audience']);
        $stmt->bindParam(":monetization_potential", $data['monetization_potential']);
        $stmt->bindParam(":seo_difficulty", $data['seo_difficulty']);
        $stmt->bindParam(":trend_analysis", $data['trend_analysis']);
        $stmt->bindParam(":seasonal_patterns", $data['seasonal_patterns']);
        $stmt->bindParam(":analysis_model", $data['analysis_model']);
        $stmt->bindParam(":analysis_version", $data['analysis_version']);
        $stmt->bindParam(":confidence_score", $data['confidence_score']);

        return $stmt->execute();
    }

    /**
     * 根据关键词ID获取报告
     */
    public function getByKeywordId($keywordId) {
        $query = "SELECT * FROM " . $this->table_name . " 
                  WHERE keyword_id = :keyword_id 
                  ORDER BY created_at DESC 
                  LIMIT 1";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":keyword_id", $keywordId);
        $stmt->execute();
        
        return $stmt->fetch();
    }

    /**
     * 更新报告
     */
    public function update($keywordId, $data) {
        $set_clauses = [];
        $params = [':keyword_id' => $keywordId];

        $allowed_fields = [
            'related_keywords', 'similarity_analysis', 'search_volume_estimate',
            'competition_intensity', 'market_opportunity', 'google_search_results',
            'search_metrics', 'top_competitors_analysis', 'title_patterns_analysis',
            'content_gap_analysis', 'ai_analysis', 'recommendation_score',
            'action_suggestions', 'top_competitors', 'competitive_advantages',
            'content_strategy', 'target_audience', 'monetization_potential',
            'seo_difficulty', 'trend_analysis', 'seasonal_patterns',
            'analysis_model', 'analysis_version', 'confidence_score'
        ];

        foreach ($allowed_fields as $field) {
            if (isset($data[$field])) {
                $set_clauses[] = "$field = :$field";
                $params[":$field"] = $data[$field];
            }
        }

        if (empty($set_clauses)) {
            return false;
        }

        $query = "UPDATE " . $this->table_name . " 
                  SET " . implode(", ", $set_clauses) . " 
                  WHERE keyword_id = :keyword_id";

        $stmt = $this->conn->prepare($query);
        return $stmt->execute($params);
    }

    /**
     * 删除报告
     */
    public function deleteByKeywordId($keywordId) {
        $query = "DELETE FROM " . $this->table_name . " WHERE keyword_id = :keyword_id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":keyword_id", $keywordId);
        return $stmt->execute();
    }

    /**
     * 检查是否已有报告
     */
    public function hasReport($keywordId) {
        $query = "SELECT COUNT(*) as count FROM " . $this->table_name . " 
                  WHERE keyword_id = :keyword_id";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":keyword_id", $keywordId);
        $stmt->execute();
        
        $result = $stmt->fetch();
        return $result['count'] > 0;
    }
}
