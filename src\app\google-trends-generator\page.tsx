'use client'

import { useState, useMemo } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card'
import { Badge } from '../../components/ui/badge'
import { Button } from '../../components/ui/button'
import { Textarea } from '../../components/ui/textarea'
import { Alert, AlertDescription } from '../../components/ui/alert'
import { 
  TrendingUp, 
  ExternalLink, 
  Copy, 
  Trash2, 
  Info, 
  Target,
  Search,
  Globe,
  Zap
} from 'lucide-react'
import FrontendHeader from '../../components/frontend-header'
import FrontendFooter from '../../components/frontend-footer'
import { Toaster, toast } from 'sonner'

interface KeywordGroup {
  id: number
  keywords: string[]
}

export default function GoogleTrendsGeneratorPage() {
  const [inputText, setInputText] = useState('')
  const [keywordGroups, setKeywordGroups] = useState<KeywordGroup[]>([])

  // 生成Google Trends链接（单个关键词）
  const generateTrendsUrl = (keyword: string) => {
    const query = `${keyword},gpts`
    return `https://trends.google.com/trends/explore?date=now%207-d&geo=US&q=${encodeURIComponent(query)}&hl=en-US`
  }

  // 解析输入文本并分组
  const processKeywords = () => {
    if (!inputText.trim()) {
      toast.error('请输入关键词')
      return
    }

    // 解析关键词（只按换行符分割，保留空格）
    const keywords = inputText
      .split(/[\n\r]+/)
      .map(k => k.trim())
      .filter(k => k.length > 0)

    if (keywords.length === 0) {
      toast.error('未找到有效关键词')
      return
    }

    // 按每组5个关键词分组
    const groups: KeywordGroup[] = []
    for (let i = 0; i < keywords.length; i += 5) {
      const groupKeywords = keywords.slice(i, i + 5)
      groups.push({
        id: i / 5 + 1,
        keywords: groupKeywords
      })
    }

    setKeywordGroups(groups)
    toast.success(`成功生成 ${groups.length} 个关键词组`)
  }

  // 批量打开某组的趋势链接（学习自needs页面）
  const openGroupTrends = (group: KeywordGroup) => {
    group.keywords.forEach((keyword, index) => {
      // 随机间隔500ms-1500ms，避免触发谷歌频率限制
      const randomDelay = Math.random() * 1000 + 500 + (index * 200) // 基础随机500-1500ms + 递增间隔
      setTimeout(() => {
        window.open(generateTrendsUrl(keyword), '_blank', 'noopener,noreferrer')
      }, randomDelay)
    })
    toast.success(`已打开 ${group.keywords.length} 个趋势链接，请稍等加载完成`)
  }



  // 清空结果
  const clearResults = () => {
    setKeywordGroups([])
    setInputText('')
    toast.success('已清空所有结果')
  }

  // 统计信息
  const stats = useMemo(() => {
    const totalKeywords = keywordGroups.reduce((sum, group) => sum + group.keywords.length, 0)
    return {
      totalGroups: keywordGroups.length,
      totalKeywords,
      avgKeywordsPerGroup: keywordGroups.length > 0 ? (totalKeywords / keywordGroups.length).toFixed(1) : '0'
    }
  }, [keywordGroups])

  return (
    <div className="min-h-screen bg-background">
      <Toaster position="top-right" />
      {/* 使用统一前台顶部组件 */}
      <FrontendHeader />

      {/* 页面标题区域 */}
      <section className="py-8 px-4 bg-gradient-to-br from-blue-50 to-purple-50 border-b">
        <div className="container mx-auto">
          <div className="max-w-4xl mx-auto text-center">
            <div className="flex items-center justify-center mb-4">
              <div className="p-3 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl mr-4">
                <TrendingUp className="h-8 w-8 text-white" />
              </div>
              <h1 className="text-4xl font-bold text-foreground">谷歌趋势查询工具</h1>
            </div>
            <p className="text-lg text-muted-foreground mb-6">
              每行输入一个关键词，自动分组生成谷歌趋势查询链接，每组5个关键词，一键批量打开
            </p>
            <div className="flex items-center justify-center gap-6 text-sm text-muted-foreground">
              <div className="flex items-center">
                <Target className="h-4 w-4 mr-2 text-blue-500" />
                智能分组
              </div>
              <div className="flex items-center">
                <Globe className="h-4 w-4 mr-2 text-purple-500" />
                批量查询
              </div>
              <div className="flex items-center">
                <Zap className="h-4 w-4 mr-2 text-green-500" />
                一键打开
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 输入区域 */}
      <section className="py-8 px-4">
        <div className="container mx-auto">
          <div className="max-w-4xl mx-auto">
            <Card className="shadow-lg border-2 border-blue-100">
              <CardHeader>
                <CardTitle className="flex items-center text-xl">
                  <Search className="h-5 w-5 mr-2 text-blue-500" />
                  关键词输入
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Textarea
                    placeholder="请输入关键词，每行一个：&#10;Banana Cat&#10;MARVEL SNAP&#10;CarX Street&#10;&#10;支持带空格的关键词，不会被拆分"
                    value={inputText}
                    onChange={(e) => setInputText(e.target.value)}
                    className="min-h-[120px] border-2 border-blue-200 focus:border-blue-500 focus:ring-blue-500"
                    rows={6}
                  />
                </div>
                
                <Alert className="border-blue-200 bg-blue-50">
                  <Info className="h-4 w-4 text-blue-500" />
                  <AlertDescription className="text-blue-700">
                    <strong>使用说明：</strong>
                    每行输入一个关键词，每组最多5个关键词，超出部分会自动分到下一组。支持带空格的关键词（如"Banana Cat"），建议使用英文关键词以获得更准确的趋势数据。
                  </AlertDescription>
                </Alert>

                <div className="flex gap-3">
                  <Button
                    onClick={processKeywords}
                    className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white"
                    disabled={!inputText.trim()}
                  >
                    <TrendingUp className="h-4 w-4 mr-2" />
                    生成趋势链接
                  </Button>
                  
                  {keywordGroups.length > 0 && (
                    <Button
                      onClick={clearResults}
                      variant="outline"
                      className="border-red-200 text-red-600 hover:bg-red-50 hover:border-red-300"
                    >
                      <Trash2 className="h-4 w-4 mr-2" />
                      清空结果
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* 统计信息区域 */}
      {keywordGroups.length > 0 && (
        <section className="py-6 px-4 bg-muted/30">
          <div className="container mx-auto">
            <div className="max-w-4xl mx-auto">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card className="text-center">
                  <CardContent className="p-4">
                    <div className="text-2xl font-bold text-blue-600">{stats.totalGroups}</div>
                    <div className="text-sm text-muted-foreground">关键词组</div>
                  </CardContent>
                </Card>
                <Card className="text-center">
                  <CardContent className="p-4">
                    <div className="text-2xl font-bold text-purple-600">{stats.totalKeywords}</div>
                    <div className="text-sm text-muted-foreground">总关键词数</div>
                  </CardContent>
                </Card>
                <Card className="text-center">
                  <CardContent className="p-4">
                    <div className="text-2xl font-bold text-green-600">{stats.avgKeywordsPerGroup}</div>
                    <div className="text-sm text-muted-foreground">平均每组</div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </section>
      )}

      {/* 结果显示区域 */}
      {keywordGroups.length > 0 && (
        <section className="py-8 px-4">
          <div className="container mx-auto">
            <div className="max-w-6xl mx-auto">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-2xl font-bold text-foreground">关键词分组结果</h2>
                <Badge variant="secondary" className="text-sm">
                  共 {keywordGroups.length} 组
                </Badge>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {keywordGroups.map((group) => (
                  <Card key={group.id} className="hover:shadow-lg transition-all duration-200 border-2 border-gray-100 hover:border-blue-200">
                    <CardHeader className="pb-3">
                      <CardTitle className="flex items-center justify-between text-lg">
                        <span className="flex items-center">
                          <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white text-sm font-bold mr-3">
                            {group.id}
                          </div>
                          第 {group.id} 组
                        </span>
                        <Badge variant="outline" className="text-xs">
                          {group.keywords.length} 个词
                        </Badge>
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      {/* 关键词列表 */}
                      <div className="flex flex-wrap gap-2">
                        {group.keywords.map((keyword, index) => (
                          <Badge key={index} variant="secondary" className="text-sm px-3 py-1">
                            {keyword}
                          </Badge>
                        ))}
                      </div>

                      {/* 操作按钮 */}
                      <div className="flex flex-col gap-2">
                        <Button
                          onClick={() => openGroupTrends(group)}
                          className="w-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white"
                        >
                          <ExternalLink className="h-4 w-4 mr-2" />
                          批量打开趋势
                        </Button>
                      </div>


                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          </div>
        </section>
      )}

      {/* 使用指南区域 */}
      <section className="py-8 px-4 bg-muted/30">
        <div className="container mx-auto">
          <div className="max-w-4xl mx-auto">
            <Card className="border-2 border-blue-100">
              <CardHeader>
                <CardTitle className="flex items-center text-xl">
                  <Info className="h-5 w-5 mr-2 text-blue-500" />
                  使用指南
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="font-semibold text-foreground mb-2">🎯 功能特点</h4>
                    <ul className="text-sm text-muted-foreground space-y-1">
                      <li>• 智能分组：每组最多5个关键词</li>
                      <li>• 批量查询：一键打开多个趋势链接</li>
                      <li>• 保留空格：支持带空格的关键词</li>
                      <li>• 智能间隔：避免触发频率限制</li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="font-semibold text-foreground mb-2">📊 查询策略</h4>
                    <ul className="text-sm text-muted-foreground space-y-1">
                      <li>• 使用英文关键词获得更准确数据</li>
                      <li>• 关注7天短期趋势变化</li>
                      <li>• 对比gpts基准热度</li>
                      <li>• 关注飙升关键词机会</li>
                    </ul>
                  </div>
                </div>

                <Alert className="border-yellow-200 bg-yellow-50">
                  <TrendingUp className="h-4 w-4 text-yellow-600" />
                  <AlertDescription className="text-yellow-700">
                    <strong>提示：</strong>
                    "批量打开趋势"会为每个关键词单独打开谷歌趋势页面，方便逐个分析每个关键词的热度变化。
                    系统会自动控制打开间隔，避免触发频率限制。
                  </AlertDescription>
                </Alert>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* 使用统一前台底部组件 */}
      <FrontendFooter />
    </div>
  )
}
