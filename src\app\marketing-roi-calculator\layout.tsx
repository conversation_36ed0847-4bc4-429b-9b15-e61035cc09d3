import type { Metadata } from "next";

export const metadata: Metadata = {
  title: "营销ROI综合计算器 | CatchIdeas",
  description: "专业的营销投资回报率计算工具，支持CAC、LTV、ROAS、ROI等关键指标分析，从广告投入到客户价值的全链路计算，数据驱动的营销决策支持。",
  authors: [{ name: "CatchIdeas团队" }],
  creator: "CatchIdeas",
  publisher: "CatchIdeas",
  robots: "index, follow",
  keywords: [
    "营销ROI计算器",
    "CAC计算",
    "LTV计算",
    "ROAS计算",
    "客户获取成本",
    "客户终身价值",
    "广告投资回报率",
    "转化率分析",
    "营销效果评估",
    "数字营销工具"
  ],
  openGraph: {
    type: "website",
    locale: "zh_CN",
    url: "https://catchideas.com/marketing-roi-calculator",
    title: "营销ROI综合计算器 | CatchIdeas",
    description: "专业的营销投资回报率计算工具，支持CAC、LTV、ROAS、ROI等关键指标分析，从广告投入到客户价值的全链路计算，数据驱动的营销决策支持。",
    siteName: "CatchIdeas",
  },
  twitter: {
    card: "summary_large_image",
    title: "营销ROI综合计算器 | CatchIdeas",
    description: "专业的营销投资回报率计算工具，支持CAC、LTV、ROAS、ROI等关键指标分析，从广告投入到客户价值的全链路计算，数据驱动的营销决策支持。",
  },
  alternates: {
    canonical: "https://catchideas.com/marketing-roi-calculator",
  },
};

export default function MarketingROICalculatorLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}
