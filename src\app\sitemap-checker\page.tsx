'use client'

import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card'
import { Badge } from '../../components/ui/badge'
import { Button } from '../../components/ui/button'
import { Input } from '../../components/ui/input'
import { Label } from '../../components/ui/label'
import { Textarea } from '../../components/ui/textarea'
import { Globe, Download, CheckCircle, XCircle, Loader2, Plus, FileText } from 'lucide-react'
import FrontendHeader from '../../components/frontend-header'
import FrontendFooter from '../../components/frontend-footer'

interface SitemapResult {
  domain: string
  url: string
  sitemapUrl: string
  status: 'checking' | 'found' | 'not-found' | 'error'
  content?: string
  error?: string
}

export default function SitemapCheckerPage() {
  const [singleDomain, setSingleDomain] = useState<string>('')
  const [batchDomains, setBatchDomains] = useState<string>('')
  const [results, setResults] = useState<SitemapResult[]>([])
  const [mode, setMode] = useState<'single' | 'batch'>('single')
  const [isChecking, setIsChecking] = useState<boolean>(false)

  // 标准化域名格式
  const normalizeDomain = (input: string): string => {
    let domain = input.trim()
    
    // 如果没有协议，添加https://
    if (!domain.startsWith('http://') && !domain.startsWith('https://')) {
      domain = 'https://' + domain
    }
    
    // 移除末尾的斜杠
    domain = domain.replace(/\/$/, '')
    
    return domain
  }

  // 从域名提取主域名用于文件命名
  const extractDomainName = (url: string): string => {
    try {
      const urlObj = new URL(url)
      return urlObj.hostname.replace(/^www\./, '')
    } catch {
      return url.replace(/https?:\/\//, '').replace(/^www\./, '').replace(/\/$/, '')
    }
  }

  // 检查单个sitemap
  const checkSitemap = async (domain: string): Promise<SitemapResult> => {
    const normalizedDomain = normalizeDomain(domain)
    const sitemapUrl = `${normalizedDomain}/sitemap.txt`
    
    const result: SitemapResult = {
      domain: extractDomainName(normalizedDomain),
      url: normalizedDomain,
      sitemapUrl,
      status: 'checking'
    }

    try {
      // 使用代理或直接请求（这里需要处理CORS问题）
      const response = await fetch(`/api/sitemap-check?url=${encodeURIComponent(sitemapUrl)}`)
      
      if (response.ok) {
        const content = await response.text()
        result.status = 'found'
        result.content = content
      } else {
        result.status = 'not-found'
        result.error = `HTTP ${response.status}`
      }
    } catch (error) {
      result.status = 'error'
      result.error = error instanceof Error ? error.message : '网络错误'
    }

    return result
  }

  // 处理单个域名
  const processSingleDomain = async () => {
    if (!singleDomain.trim()) return

    setIsChecking(true)
    const result = await checkSitemap(singleDomain.trim())
    setResults(prev => [...prev, result])
    setSingleDomain('')
    setIsChecking(false)
  }

  // 处理批量域名
  const processBatchDomains = async () => {
    if (!batchDomains.trim()) return

    const domains = batchDomains.trim().split('\n')
      .map(d => d.trim())
      .filter(d => d)
      .slice(0, 10) // 限制最多10个

    if (domains.length === 0) {
      alert('请输入有效的域名')
      return
    }

    if (domains.length > 10) {
      alert('批量处理最多支持10个域名')
      return
    }

    setIsChecking(true)

    // 创建初始结果
    const initialResults = domains.map(domain => ({
      domain: extractDomainName(normalizeDomain(domain)),
      url: normalizeDomain(domain),
      sitemapUrl: `${normalizeDomain(domain)}/sitemap.txt`,
      status: 'checking' as const
    }))

    setResults(prev => [...prev, ...initialResults])

    // 并发检查所有域名
    const promises = domains.map(async (domain, index) => {
      const result = await checkSitemap(domain)
      
      // 更新对应的结果
      setResults(prev => prev.map((item, i) => 
        i === prev.length - domains.length + index ? result : item
      ))
      
      return result
    })

    await Promise.all(promises)
    setBatchDomains('')
    setIsChecking(false)
  }

  // 下载单个sitemap
  const downloadSitemap = (result: SitemapResult) => {
    if (result.status !== 'found' || !result.content) return

    const blob = new Blob([result.content], { type: 'text/plain' })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `${result.domain}-sitemap.txt`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
  }

  // 批量下载所有成功的sitemap
  const downloadAllSitemaps = () => {
    const successResults = results.filter(r => r.status === 'found' && r.content)
    
    if (successResults.length === 0) {
      alert('没有可下载的sitemap文件')
      return
    }

    successResults.forEach(result => {
      setTimeout(() => downloadSitemap(result), 100) // 稍微延迟避免浏览器阻止
    })
  }

  // 清空结果
  const clearResults = () => {
    setResults([])
  }

  // 删除单个结果
  const removeResult = (index: number) => {
    setResults(prev => prev.filter((_, i) => i !== index))
  }

  const getStatusIcon = (status: SitemapResult['status']) => {
    switch (status) {
      case 'checking':
        return <Loader2 className="h-4 w-4 animate-spin text-blue-500" />
      case 'found':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'not-found':
      case 'error':
        return <XCircle className="h-4 w-4 text-red-500" />
    }
  }

  const getStatusText = (result: SitemapResult) => {
    switch (result.status) {
      case 'checking':
        return '检查中...'
      case 'found':
        return 'sitemap.txt 存在'
      case 'not-found':
        return 'sitemap.txt 不存在'
      case 'error':
        return `错误: ${result.error}`
    }
  }

  const successCount = results.filter(r => r.status === 'found').length

  return (
    <div className="min-h-screen bg-background">
      <FrontendHeader />
      
      {/* 页面标题 */}
      <section className="py-8 px-4 bg-gradient-to-r from-blue-50 to-purple-50 border-b">
        <div className="container mx-auto text-center">
          <div className="flex items-center justify-center mb-4">
            <div className="p-3 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl mr-4">
              <FileText className="h-8 w-8 text-white" />
            </div>
            <h1 className="text-4xl font-bold text-gray-900">Sitemap检测工具</h1>
          </div>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            批量检测网站sitemap.txt文件，支持单个和批量处理，一键下载所有可用文件
          </p>
        </div>
      </section>

      {/* 主要内容区域 */}
      <section className="py-8 px-4">
        <div className="container mx-auto max-w-7xl">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* 输入区域 */}
            <div className="space-y-6">
              {/* 模式切换 */}
              <Card>
                <CardHeader>
                  <CardTitle>检测模式</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex gap-2">
                    <Button
                      variant={mode === 'single' ? "default" : "outline"}
                      onClick={() => setMode('single')}
                      className={mode === 'single'
                        ? "bg-gradient-to-r from-blue-500 to-purple-600 text-white hover:from-blue-600 hover:to-purple-700"
                        : "border-2 border-gray-300 hover:border-gray-500"
                      }
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      单个检测
                    </Button>
                    <Button
                      variant={mode === 'batch' ? "default" : "outline"}
                      onClick={() => setMode('batch')}
                      className={mode === 'batch'
                        ? "bg-gradient-to-r from-blue-500 to-purple-600 text-white hover:from-blue-600 hover:to-purple-700"
                        : "border-2 border-gray-300 hover:border-gray-500"
                      }
                    >
                      <Globe className="h-4 w-4 mr-2" />
                      批量检测
                    </Button>
                  </div>
                </CardContent>
              </Card>

              {/* 单个检测 */}
              {mode === 'single' && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <Plus className="h-5 w-5 mr-2 text-blue-600" />
                      单个域名检测
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <Label htmlFor="singleDomain">网站域名</Label>
                      <Input
                        id="singleDomain"
                        type="text"
                        placeholder="https://catchideas.com/ 或 catchideas.com"
                        value={singleDomain}
                        onChange={(e) => setSingleDomain(e.target.value)}
                        className="h-12 text-lg border-2 border-blue-200 focus:border-blue-500"
                      />
                    </div>
                    <Button
                      onClick={processSingleDomain}
                      disabled={isChecking}
                      className="w-full h-12 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white font-semibold"
                    >
                      {isChecking ? (
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      ) : (
                        <Plus className="h-4 w-4 mr-2" />
                      )}
                      开始检测
                    </Button>
                  </CardContent>
                </Card>
              )}

              {/* 批量检测 */}
              {mode === 'batch' && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <Globe className="h-5 w-5 mr-2 text-green-600" />
                      批量域名检测
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <Label htmlFor="batchDomains">域名列表（每行一个，最多10个）</Label>
                      <Textarea
                        id="batchDomains"
                        placeholder={`https://catchideas.com/\nexample.com\nhttps://www.google.com/\ngithub.com`}
                        value={batchDomains}
                        onChange={(e) => setBatchDomains(e.target.value)}
                        className="min-h-[200px] text-sm border-2 border-green-200 focus:border-green-500"
                      />
                    </div>
                    <Button
                      onClick={processBatchDomains}
                      disabled={isChecking}
                      className="w-full h-12 bg-gradient-to-r from-green-500 to-teal-600 hover:from-green-600 hover:to-teal-700 text-white font-semibold"
                    >
                      {isChecking ? (
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      ) : (
                        <Globe className="h-4 w-4 mr-2" />
                      )}
                      批量检测
                    </Button>
                  </CardContent>
                </Card>
              )}

              {/* 操作按钮 */}
              {results.length > 0 && (
                <Card>
                  <CardContent className="pt-6 space-y-3">
                    {successCount > 0 && (
                      <Button
                        onClick={downloadAllSitemaps}
                        className="w-full h-12 bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white font-semibold"
                      >
                        <Download className="h-4 w-4 mr-2" />
                        下载所有sitemap ({successCount}个)
                      </Button>
                    )}
                    <Button
                      onClick={clearResults}
                      variant="outline"
                      className="w-full h-12 border-2 border-red-300 hover:border-red-500 text-red-600 hover:text-red-700"
                    >
                      清空所有结果
                    </Button>
                  </CardContent>
                </Card>
              )}
            </div>

            {/* 结果显示区域 */}
            <div className="space-y-6">
              {results.length === 0 ? (
                <Card>
                  <CardContent className="text-center py-12">
                    <FileText className="h-16 w-16 mx-auto text-gray-400 mb-4" />
                    <h3 className="text-xl font-semibold text-gray-600 mb-2">等待检测</h3>
                    <p className="text-gray-500">
                      请输入域名并点击检测按钮
                    </p>
                  </CardContent>
                </Card>
              ) : (
                <>
                  {/* 统计信息 */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center justify-between">
                        <span>检测结果</span>
                        <div className="flex gap-2">
                          <Badge className="bg-blue-100 text-blue-700">
                            总计: {results.length}
                          </Badge>
                          <Badge className="bg-green-100 text-green-700">
                            成功: {successCount}
                          </Badge>
                          <Badge className="bg-red-100 text-red-700">
                            失败: {results.length - successCount}
                          </Badge>
                        </div>
                      </CardTitle>
                    </CardHeader>
                  </Card>

                  {/* 结果列表 */}
                  <Card>
                    <CardHeader>
                      <CardTitle>Sitemap检测详情</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        {results.map((result, index) => (
                          <div key={index} className="p-4 bg-gray-50 rounded-lg">
                            <div className="flex items-center justify-between mb-2">
                              <div className="flex items-center gap-2">
                                {getStatusIcon(result.status)}
                                <span className="font-medium">{result.domain}</span>
                              </div>
                              <div className="flex gap-2">
                                {result.status === 'found' && (
                                  <Button
                                    size="sm"
                                    onClick={() => downloadSitemap(result)}
                                    className="h-8 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white"
                                  >
                                    <Download className="h-3 w-3 mr-1" />
                                    下载
                                  </Button>
                                )}
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => removeResult(index)}
                                  className="h-8 border-red-300 hover:border-red-500 text-red-600"
                                >
                                  删除
                                </Button>
                              </div>
                            </div>

                            <div className="text-sm text-gray-600 mb-2">
                              <div className="flex items-center gap-2">
                                <span>状态:</span>
                                <span className={
                                  result.status === 'found' ? 'text-green-600' :
                                  result.status === 'checking' ? 'text-blue-600' :
                                  'text-red-600'
                                }>
                                  {getStatusText(result)}
                                </span>
                              </div>
                            </div>

                            <div className="text-xs text-gray-500">
                              <div>原域名: {result.url}</div>
                              <div>检测地址: {result.sitemapUrl}</div>
                              {result.content && (
                                <div className="mt-2">
                                  <span>内容预览: </span>
                                  <span className="bg-gray-200 px-2 py-1 rounded">
                                    {result.content.split('\n').length} 行内容
                                  </span>
                                </div>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                </>
              )}
            </div>
          </div>
        </div>
      </section>

      <FrontendFooter />
    </div>
  )
}
