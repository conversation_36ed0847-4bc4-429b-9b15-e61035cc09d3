<?php
require_once '../utils/Response.php';
require_once '../config/database.php';

/**
 * 分类管理API
 * 处理Google Trends分类相关操作
 */

// 处理CORS预检请求
Response::handleOptions();

// 验证请求方法
$method = Response::validateMethod(['GET']);

try {
    $database = new Database();
    $conn = $database->getConnection();
    
    switch ($method) {
        case 'GET':
            handleGet($conn);
            break;
    }
    
} catch (Exception $e) {

    Response::serverError('操作失败: ' . $e->getMessage());
}

/**
 * 处理GET请求 - 获取分类列表
 */
function handleGet($conn) {
    // 获取分类列表，包含关键词统计
    $query = "SELECT 
                c.id,
                c.name,
                c.english_name,
                COUNT(k.id) as keyword_count,
                c.last_updated
              FROM categories c
              LEFT JOIN keywords k ON c.id = k.category
              GROUP BY c.id, c.name, c.english_name, c.last_updated
              ORDER BY c.name";
    
    $stmt = $conn->prepare($query);
    $stmt->execute();
    $categories = $stmt->fetchAll();
    
    // 获取总体统计
    $statsQuery = "SELECT 
                     COUNT(*) as total_keywords,
                     COUNT(CASE WHEN category IS NOT NULL THEN 1 END) as categorized_keywords,
                     COUNT(CASE WHEN category IS NULL THEN 1 END) as uncategorized_keywords
                   FROM keywords";
    
    $statsStmt = $conn->prepare($statsQuery);
    $statsStmt->execute();
    $stats = $statsStmt->fetch();
    
    // 格式化响应数据
    $response = [
        'categories' => $categories,
        'statistics' => [
            'total_categories' => count($categories),
            'total_keywords' => (int)$stats['total_keywords'],
            'categorized_keywords' => (int)$stats['categorized_keywords'],
            'uncategorized_keywords' => (int)$stats['uncategorized_keywords'],
            'categorization_rate' => $stats['total_keywords'] > 0 
                ? round(($stats['categorized_keywords'] / $stats['total_keywords']) * 100, 2) 
                : 0
        ]
    ];
    
    Response::success($response, '分类列表获取成功');
}
?>
