'use client'

import { useState, useEffect, useCallback } from 'react'
import { <PERSON><PERSON> } from '../components/ui/button'
import { Card, CardContent, CardHeader } from '../components/ui/card'
import { Badge } from '../components/ui/badge'
import { Alert, AlertDescription } from '../components/ui/alert'
import { Skeleton } from '../components/ui/skeleton'
import { Toaster, toast } from 'sonner'
import {
  Sparkles,
  Settings,
  Upload,
  BarChart3,
  ChevronRight,
  Loader2,
  Search,
  Filter,
  Target
} from 'lucide-react'
import Link from 'next/link'
import KeywordDisplay from '../components/keyword-display'
import CategoryFilter from '../components/category-filter'
import FrontendHeader from '../components/frontend-header'
import FrontendFooter from '../components/frontend-footer'
import Pagination from '../components/pagination'

interface Keyword {
  id: string
  keyword: string
  user_intent?: string
  user_pain_point?: string
  competition_level?: 'easy' | 'medium' | 'hard'
  competition_score?: number
  competition_color?: string
  competition_description?: string
  serp_analysis?: string
  user_comment?: string
  category?: string
  recommended_domains?: Array<{
    domain: string
    check_url: string
  }>
  is_analyzed: boolean
  created_at_formatted?: string
  analyzed_at_formatted?: string
  source?: string
}

interface Filters {
  search: string
  category: string
  analyzed_only: string
  competition_level: string
  date_range: string
  sort_by: string
  sort_order: string
}

export default function Home() {
  const [keywords, setKeywords] = useState<Keyword[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [pagination, setPagination] = useState({
    current_page: 1,
    per_page: 12, // 每页显示12条
    total: 0,
    total_pages: 0,
    has_next: false,
    has_prev: false
  })
  const [statistics, setStatistics] = useState({
    total: 0,
    analyzed: 0,
    unanalyzed: 0,
    categories: {} as Record<string, number>
  })
  const [filters, setFilters] = useState<Filters>({
    search: '',
    category: '',
    analyzed_only: '',
    competition_level: '',
    date_range: '',
    sort_by: 'created_at',
    sort_order: 'desc'
  })
  const [searchTerm, setSearchTerm] = useState('')

  // 打字机效果文案数组
  const typewriterTexts = [
    "让每一个关键词选择都有依据。",
    "AI驱动关键词报告，3秒掌握选词方向。",
    "自动发现流量词，生成结构化SEO报告。",
    "写出真正有人搜、有得排的内容。",
    "下一波流量机会，就藏在今天的搜索趋势里。",
    "选对关键词，一篇文章也能赢过大站。"
  ]

  // 打字机效果
  useEffect(() => {
    let currentTextIndex = 0
    let currentCharIndex = 0
    let isDeleting = false
    let timeoutId: NodeJS.Timeout

    const typewriterElement = document.getElementById('typewriter-text')
    if (!typewriterElement) return

    const typeText = () => {
      const currentText = typewriterTexts[currentTextIndex]

      if (!isDeleting) {
        // 正在输入
        typewriterElement.textContent = currentText.substring(0, currentCharIndex + 1)
        currentCharIndex++

        if (currentCharIndex === currentText.length) {
          // 输入完成，等待2秒后开始删除
          timeoutId = setTimeout(() => {
            isDeleting = true
            typeText()
          }, 2000)
          return
        }
      } else {
        // 正在删除
        typewriterElement.textContent = currentText.substring(0, currentCharIndex)
        currentCharIndex--

        if (currentCharIndex < 0) {
          // 删除完成，切换到下一个文案
          isDeleting = false
          currentTextIndex = (currentTextIndex + 1) % typewriterTexts.length
          currentCharIndex = 0
        }
      }

      // 设置下一次执行的延迟
      const delay = isDeleting ? 50 : 100 // 删除速度更快
      timeoutId = setTimeout(typeText, delay)
    }

    // 开始打字机效果
    typeText()

    // 清理函数
    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId)
      }
    }
  }, [])

  // 获取关键词列表
  const fetchKeywords = useCallback(async (page = 1) => {
    setLoading(true)
    setError('')

    try {
      const queryParams = new URLSearchParams({
        page: page.toString(),
        limit: pagination.per_page.toString(),
        ...filters
      })

      const response = await fetch(`/api/keywords/list?${queryParams.toString()}`)
      const data = await response.json()

      if (data.success) {
        const newKeywords = data.data.keywords || []
        const paginationData = data.data.pagination || {}

        // 去重处理，防止重复的关键词
        const uniqueNewKeywords = newKeywords.filter((keyword: any, index: number, self: any[]) =>
          index === self.findIndex((k: any) => k.id === keyword.id)
        )

        // 直接替换关键词列表
        setKeywords(uniqueNewKeywords)

        // 更新分页信息
        setPagination({
          current_page: paginationData.current_page || page,
          per_page: paginationData.per_page || pagination.per_page,
          total: paginationData.total || 0,
          total_pages: paginationData.total_pages || 0,
          has_next: paginationData.has_next !== undefined ? paginationData.has_next : false,
          has_prev: paginationData.has_prev !== undefined ? paginationData.has_prev : false
        })
        setStatistics(data.data.statistics || statistics)
      } else {
        setError(data.error || '获取关键词列表失败')
      }
    } catch (err) {
      setError('网络错误，请稍后重试')
    } finally {
      setLoading(false)
    }
  }, [filters, pagination.per_page])

  // 页面跳转
  const handlePageChange = (page: number) => {
    if (page >= 1 && page <= pagination.total_pages && page !== pagination.current_page) {
      fetchKeywords(page)
      // 滚动到顶部
      window.scrollTo({ top: 0, behavior: 'smooth' })
    }
  }

  // 每页条数变化
  const handlePageSizeChange = (newPageSize: number) => {
    setPagination(prev => ({ ...prev, per_page: newPageSize }))
    fetchKeywords(1) // 重新从第一页开始
  }

  // 初始加载
  useEffect(() => {
    fetchKeywords(1)
  }, [filters])

  // 处理筛选条件变化
  const handleFilterChange = useCallback((newFilters: Filters) => {
    setFilters(newFilters)
  }, [])

  // 处理搜索
  const handleSearch = () => {
    setFilters(prev => ({ ...prev, search: searchTerm }))
  }



  // 处理评价
  const handleComment = async (keywordId: string, comment: string) => {
    try {


      const timestamp = Date.now()
      const response = await fetch(`/api/keywords/comment?t=${timestamp}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache'
        },
        body: JSON.stringify({
          id: keywordId,
          comment: comment
        })
      })

      const data = await response.json()

      if (data.success) {
        toast.success('评价提交成功')

        // 更新本地关键词数据
        setKeywords(prev => prev.map(keyword =>
          keyword.id === keywordId
            ? { ...keyword, user_comment: comment }
            : keyword
        ))
      } else {
        toast.error(data.message || '评价提交失败')
      }
    } catch (error) {
      console.error('提交评价失败:', error)
      toast.error('网络错误，请稍后重试')
    }
  }

  return (
    <div className="min-h-screen bg-background">
        {/* 使用新的统一前台顶部组件 */}
        <FrontendHeader />



      {/* 智能搜索区域 - 大厂风格 */}
      <section className="relative py-16 px-4 bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 overflow-hidden">
        {/* 背景装饰 */}
        <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
        <div className="absolute top-10 right-10 w-72 h-72 bg-blue-200 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob"></div>
        <div className="absolute top-20 left-10 w-72 h-72 bg-purple-200 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob animation-delay-2000"></div>
        <div className="absolute bottom-10 left-20 w-72 h-72 bg-pink-200 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob animation-delay-4000"></div>

        <div className="container mx-auto relative z-10">
          {/* 主标题区域 */}
          <div className="text-center mb-12">
            <h1 className="text-5xl md:text-6xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 bg-clip-text text-transparent mb-6">
              趋势关键词分析
            </h1>
            <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto leading-relaxed">
              专业的关键词热度分析工具，用数据驱动您的项目决策
            </p>

            {/* 核心搜索框 */}
            <div className="max-w-4xl mx-auto mb-8 px-4">
              <div className="relative group">
                <div className="absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl blur opacity-25 group-hover:opacity-40 transition duration-1000 group-hover:duration-200"></div>
                <div className="relative bg-white rounded-2xl shadow-2xl border border-gray-100">
                  {/* 桌面端布局 */}
                  <div className="hidden sm:flex items-center p-6">
                    <div className="flex-1 flex items-center space-x-4">
                      <div className="p-3 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl">
                        <Search className="h-6 w-6 text-white" />
                      </div>
                      <input
                        type="text"
                        placeholder="搜索关键词，获取深度分析报告..."
                        className="flex-1 text-lg placeholder-gray-400 border-0 focus:outline-none bg-transparent"
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                      />
                    </div>
                    <Button
                      onClick={handleSearch}
                      disabled={loading}
                      className="ml-4 px-8 py-3 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200"
                    >
                      {loading ? (
                        <Loader2 className="h-5 w-5 animate-spin" />
                      ) : (
                        <>
                          <Search className="h-5 w-5 mr-2" />
                          立即分析
                        </>
                      )}
                    </Button>
                  </div>

                  {/* 移动端布局 */}
                  <div className="sm:hidden">
                    {/* 搜索输入区域 */}
                    <div className="flex items-center p-4 pb-3">
                      <div className="p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg">
                        <Search className="h-5 w-5 text-white" />
                      </div>
                      <input
                        type="text"
                        placeholder="搜索关键词，获取深度分析报告..."
                        className="flex-1 ml-3 text-base placeholder-gray-400 border-0 focus:outline-none bg-transparent"
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                      />
                    </div>

                    {/* 分析按钮区域 */}
                    <div className="px-4 pb-4">
                      <Button
                        onClick={handleSearch}
                        disabled={loading}
                        className="w-full py-3 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-200"
                      >
                        {loading ? (
                          <Loader2 className="h-5 w-5 animate-spin" />
                        ) : (
                          <>
                            <Search className="h-5 w-5 mr-2" />
                            立即分析
                          </>
                        )}
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* 打字机效果展示文案 - 去掉背景框，逐字输出 */}
            <div className="mb-8">
              <div className="relative h-16 flex items-center justify-center">
                <div className="typewriter-container">
                  <span id="typewriter-text" className="typewriter-text text-lg font-medium text-gray-800"></span>
                </div>
              </div>
            </div>
          </div>

          {/* 高级筛选区域 */}
          <div className="bg-white/90 backdrop-blur-sm rounded-3xl shadow-xl border border-white/20 p-8">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg">
                  <Filter className="h-5 w-5 text-white" />
                </div>
                <h3 className="text-2xl font-bold text-gray-800">高级筛选</h3>
              </div>
              <Button
                variant="ghost"
                size="sm"
                className="text-gray-500 hover:text-gray-700"
                onClick={() => {
                  setSearchTerm('')
                  setFilters({
                    search: '',
                    category: '',
                    analyzed_only: '',
                    competition_level: '',
                    date_range: '',
                    sort_by: 'created_at',
                    sort_order: 'desc'
                  })
                  fetchKeywords(1)
                }}
              >
                重置筛选
              </Button>
            </div>

            <CategoryFilter
              onFilterChange={handleFilterChange}
              statistics={statistics}
              loading={loading}
            />
          </div>
        </div>
      </section>

      {/* 关键词列表 - 创意展示区 */}
      <section className="py-8 px-4">
        <div className="container mx-auto">
          <div className="mb-6">
            <h2 className="text-2xl font-bold text-foreground mb-2">创意关键词</h2>
            <p className="text-muted-foreground">发现趋势，捕捉灵感</p>
          </div>

          {error && (
            <Alert className="mb-6">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {loading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {Array.from({ length: 6 }).map((_, index) => (
                <Card key={index} className="idea-card-enhanced">
                  <CardHeader>
                    <Skeleton className="h-6 w-3/4" />
                    <Skeleton className="h-4 w-1/2" />
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <Skeleton className="h-4 w-full" />
                      <Skeleton className="h-4 w-2/3" />
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : keywords.length === 0 ? (
            <Card className="idea-card-enhanced text-center py-12">
              <CardContent>
                <Sparkles className="h-16 w-16 mx-auto text-primary/50 mb-4" />
                <h3 className="text-xl font-semibold text-foreground mb-2">等待创意发现</h3>
                <p className="text-muted-foreground mb-6">
                  {filters.search || filters.category || filters.analyzed_only || filters.competition_level
                    ? '没有找到符合筛选条件的关键词，试试调整筛选条件'
                    : '还没有上传任何关键词，开始您的创意发现之旅吧'
                  }
                </p>
                <Button asChild className="btn-primary-enhanced">
                  <Link href="/console/upload">
                    <Upload className="h-4 w-4 mr-2" />
                    上传关键词
                  </Link>
                </Button>
              </CardContent>
            </Card>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {keywords.map((keyword) => (
                <KeywordDisplay
                  key={keyword.id}
                  keyword={keyword}
                  onComment={handleComment}
                />
              ))}
            </div>
          )}
        </div>
      </section>

      {/* 分页组件 */}
      {!loading && keywords.length > 0 && pagination.total_pages > 1 && (
        <section className="py-8 px-4">
          <div className="container mx-auto">
            <Pagination
              currentPage={pagination.current_page}
              totalPages={pagination.total_pages}
              onPageChange={handlePageChange}
              total={pagination.total}
              pageSize={pagination.per_page}
              showQuickJumper={true}
              showSizeChanger={true}
              pageSizeOptions={[6, 12, 24, 48]}
              onPageSizeChange={handlePageSizeChange}
              className="justify-center"
            />
          </div>
        </section>
      )}



      {/* 使用新的统一前台底部组件 */}
      <FrontendFooter />

      <Toaster position="top-right" richColors closeButton duration={5000} />
    </div>
  )
}
