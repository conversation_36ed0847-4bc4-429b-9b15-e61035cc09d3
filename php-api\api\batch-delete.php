<?php
require_once '../utils/Response.php';
require_once '../models/Keyword.php';

/**
 * 批量删除关键词API
 * 专门处理批量删除操作，不修改原有keywords.php接口
 */

// 处理CORS预检请求
Response::handleOptions();

// 验证请求方法 - 只支持POST
$method = Response::validateMethod(['POST']);

try {
    $keywordModel = new Keyword();
    
    if ($method === 'POST') {
        handleBatchDelete($keywordModel);
    }
    
} catch (Exception $e) {
    Response::serverError('批量删除失败: ' . $e->getMessage());
}

/**
 * 处理批量删除请求
 */
function handleBatchDelete($keywordModel) {
    $data = Response::getRequestData();
    
    // 验证参数
    if (!isset($data['ids']) || !is_array($data['ids']) || empty($data['ids'])) {
        Response::validationError(['ids' => '请选择要删除的关键词']);
    }
    
    $ids = $data['ids'];
    
    // 验证ID格式（只支持slug格式）
    $slugPattern = '/^[a-z0-9]+(?:-[a-z0-9]+)*$/'; // slug格式：小写字母、数字、连字符
    $invalidIds = [];

    foreach ($ids as $id) {
        if (!preg_match($slugPattern, $id)) {
            $invalidIds[] = $id;
        }
    }

    if (!empty($invalidIds)) {
        Response::validationError(['ids' => '包含无效的关键词ID格式: ' . implode(', ', $invalidIds)]);
    }
    
    // 批量删除逻辑
    $deleteResults = [];
    $errors = [];
    $successCount = 0;
    $failCount = 0;
    
    // 开始事务
    $keywordModel->beginTransaction();
    
    try {
        foreach ($ids as $id) {
            // 检查关键词是否存在
            $existingKeyword = $keywordModel->getById($id);
            
            if (!$existingKeyword) {
                $deleteResults[] = [
                    'id' => $id,
                    'success' => false,
                    'error' => '关键词不存在'
                ];
                $errors[] = "关键词 {$id}: 不存在";
                $failCount++;
                continue;
            }
            
            // 删除关键词（会自动级联删除关联表数据）
            $success = $keywordModel->delete($id);
            
            if ($success) {
                $deleteResults[] = [
                    'id' => $id,
                    'success' => true,
                    'keyword' => $existingKeyword['keyword']
                ];
                $successCount++;
            } else {
                $deleteResults[] = [
                    'id' => $id,
                    'success' => false,
                    'error' => '删除操作失败'
                ];
                $errors[] = "关键词 {$existingKeyword['keyword']}: 删除操作失败";
                $failCount++;
            }
        }
        
        // 提交事务
        $keywordModel->commit();
        
        // 返回结果
        $totalCount = count($ids);
        
        if ($successCount === $totalCount) {
            // 全部成功
            Response::success([
                'total' => $totalCount,
                'success' => $successCount,
                'failed' => $failCount,
                'results' => $deleteResults
            ], "成功删除 {$successCount} 个关键词");
            
        } elseif ($successCount > 0) {
            // 部分成功
            Response::success([
                'total' => $totalCount,
                'success' => $successCount,
                'failed' => $failCount,
                'results' => $deleteResults,
                'errors' => $errors
            ], "成功删除 {$successCount} 个关键词，{$failCount} 个失败");
            
        } else {
            // 全部失败
            Response::error('批量删除失败', 400, [
                'total' => $totalCount,
                'success' => $successCount,
                'failed' => $failCount,
                'results' => $deleteResults,
                'errors' => $errors
            ]);
        }
        
    } catch (Exception $e) {
        // 回滚事务
        $keywordModel->rollback();
        
        Response::serverError('批量删除过程中发生错误: ' . $e->getMessage());
    }
}
?>
