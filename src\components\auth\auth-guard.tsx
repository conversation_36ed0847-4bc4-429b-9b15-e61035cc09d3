'use client'

import { useAuth } from '@/hooks/useAuth'
import { ReactNode } from 'react'

interface AuthGuardProps {
  children: ReactNode
  requireAdmin?: boolean
  fallback?: ReactNode
}

export function AuthGuard({ children, requireAdmin = false, fallback }: AuthGuardProps) {
  const { isLoading, isAuthenticated, isAdmin } = useAuth()

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
          <p className="text-muted-foreground">加载中...</p>
        </div>
      </div>
    )
  }

  if (!isAuthenticated) {
    return fallback || <div>请先登录</div>
  }

  if (requireAdmin && !isAdmin) {
    return fallback || <div>权限不足</div>
  }

  return <>{children}</>
}

// 仅管理员可见组件
export function AdminOnly({ children, fallback }: { children: ReactNode; fallback?: ReactNode }) {
  return (
    <AuthGuard requireAdmin={true} fallback={fallback}>
      {children}
    </AuthGuard>
  )
}

// 需要登录可见组件
export function RequireAuth({ children, fallback }: { children: ReactNode; fallback?: ReactNode }) {
  return (
    <AuthGuard requireAdmin={false} fallback={fallback}>
      {children}
    </AuthGuard>
  )
}
