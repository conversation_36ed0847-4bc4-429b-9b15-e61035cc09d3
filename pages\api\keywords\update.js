export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ 
      success: false, 
      message: 'Method not allowed' 
    })
  }

  try {
    const { 
      id, 
      keyword, 
      user_intent, 
      user_pain_point, 
      competition_level, 
      competition_score, 
      serp_analysis, 
      recommended_domains, 
      category, 
      notes 
    } = req.body

    if (!id) {
      return res.status(400).json({
        success: false,
        message: '缺少关键词ID'
      })
    }

    const apiBaseUrl = process.env.NEXT_PUBLIC_PHP_API_URL || 'https://api.xstty.com/api'
    
    // 准备更新数据（不包含ID，ID通过URL传递）
    const updateData = {
      keyword,
      user_intent,
      user_pain_point,
      competition_level,
      competition_score,
      serp_analysis,
      recommended_domains,
      category,
      notes
    }

    // 调用PHP API更新关键词（ID通过URL参数传递）
    const response = await fetch(`${apiBaseUrl}/keywords.php?id=${encodeURIComponent(id)}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(updateData)
    })

    const data = await response.json()

    if (data.success) {
      res.status(200).json({
        success: true,
        message: '关键词更新成功',
        data: data.data
      })
    } else {
      res.status(500).json({
        success: false,
        message: data.message || '更新失败'
      })
    }

  } catch (error) {
    console.error('Update keyword API error:', error)
    res.status(500).json({
      success: false,
      message: '服务器内部错误: ' + error.message
    })
  }
}
