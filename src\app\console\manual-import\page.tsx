'use client'

import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import ConsoleLayout from '../../../components/console-layout'
import ManualImportTab from '../../../components/manual-import-tab'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../../components/ui/card'
import { Alert, AlertDescription } from '../../../components/ui/alert'
import { Badge } from '../../../components/ui/badge'
import {
  FileText,
  Info,
  Type,
  Database,
  CheckCircle,
  AlertTriangle,
  Zap,
  Target,
  Edit3
} from 'lucide-react'

export default function ManualImportPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [activeTab, setActiveTab] = useState('import')

  // 设置页面标题
  useEffect(() => {
    document.title = '手动导入 - CatchIdeas'
  }, [])

  useEffect(() => {
    if (status === 'loading') return // 还在加载中

    if (!session) {
      router.push('/auth/signin') // 未登录跳转到登录页
      return
    }

    // 检查是否为管理员
    const isAdmin = session.user?.email === '<EMAIL>'
    if (!isAdmin) {
      router.push('/console/analyze') // 非管理员重定向到分析页面
      return
    }
  }, [session, status, router])

  if (status === 'loading') {
    return <div className="min-h-screen flex items-center justify-center">加载中...</div>
  }

  if (!session) {
    return null // 正在重定向
  }

  return (
    <ConsoleLayout
      title="手动关键词导入"
    >
      <div className="space-y-8">
        {/* 功能概览 - 统一卡片风格 */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card className="data-card-enhanced stats-card-primary">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-primary/80">手动输入</CardTitle>
              <Edit3 className="h-5 w-5 text-primary" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">TXT/文本</div>
              <p className="text-xs text-muted-foreground">
                支持TXT文件和在线粘贴
              </p>
            </CardContent>
          </Card>

          <Card className="data-card-enhanced stats-card-success">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-success/80">智能过滤</CardTitle>
              <Target className="h-5 w-5 text-success" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">去重过滤</div>
              <p className="text-xs text-muted-foreground">
                自动去重和基础筛选
              </p>
            </CardContent>
          </Card>

          <Card className="data-card-enhanced stats-card-secondary">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-secondary/80">实时处理</CardTitle>
              <Zap className="h-5 w-5 text-secondary" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">即时反馈</div>
              <p className="text-xs text-muted-foreground">
                实时进度和结果展示
              </p>
            </CardContent>
          </Card>
        </div>

        {/* 使用指南和导入区域 */}
        <div className="space-y-6">
          {/* 自定义切换按钮 */}
          <div className="flex justify-center mb-6">
            <div className="inline-flex bg-slate-100/50 p-1 rounded-2xl">
              <button
                onClick={() => setActiveTab('import')}
                className={`flex items-center space-x-2 px-8 py-3 mx-1 rounded-xl font-medium transition-all duration-300 hover:scale-[1.02] active:scale-[0.98] ${
                  activeTab === 'import'
                    ? 'bg-primary text-primary-foreground shadow-lg shadow-primary/25'
                    : 'text-muted-foreground hover:text-foreground hover:bg-white/70'
                }`}
              >
                <Type className="h-4 w-4" />
                <span>手动导入</span>
              </button>
              <button
                onClick={() => setActiveTab('guide')}
                className={`flex items-center space-x-2 px-8 py-3 mx-1 rounded-xl font-medium transition-all duration-300 hover:scale-[1.02] active:scale-[0.98] ${
                  activeTab === 'guide'
                    ? 'bg-secondary text-secondary-foreground shadow-lg shadow-secondary/25'
                    : 'text-muted-foreground hover:text-foreground hover:bg-white/70'
                }`}
              >
                <Info className="h-4 w-4" />
                <span>使用指南</span>
              </button>
            </div>
          </div>

          {/* 内容区域 */}
          {activeTab === 'import' && (
            <div className="space-y-6">
              {/* 重要提示 */}
              <Alert className="border-primary/30 bg-card border-2">
                <Info className="h-4 w-4 text-primary" />
                <AlertDescription className="text-foreground">
                  <strong>支持格式：</strong>TXT文件或直接粘贴文本，每行一个关键词。
                  <br />
                  <strong>处理规则：</strong>自动去重、基础过滤，移除过短和重复的关键词。
                </AlertDescription>
              </Alert>

              {/* 手动导入组件 */}
              <ManualImportTab />
            </div>
          )}

          {activeTab === 'guide' && (
            <div className="space-y-6">
            <Card className="data-card-enhanced">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <FileText className="h-5 w-5 text-primary" />
                  <span>手动导入使用指南</span>
                </CardTitle>
                <CardDescription>
                  了解如何使用手动导入功能高效管理关键词
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* 支持格式 */}
                <div>
                  <h4 className="font-semibold mb-3 flex items-center space-x-2">
                    <Badge variant="outline" className="bg-primary/10 text-primary border-primary/30">
                      <Type className="h-3 w-3 mr-1" />
                      支持格式
                    </Badge>
                  </h4>
                  <div className="space-y-2 text-sm text-muted-foreground">
                    <div className="flex items-center space-x-2">
                      <CheckCircle className="h-4 w-4 text-success" />
                      <span><strong>TXT文件：</strong>纯文本文件，每行一个关键词</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <CheckCircle className="h-4 w-4 text-success" />
                      <span><strong>在线粘贴：</strong>直接在文本框中粘贴关键词列表</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <CheckCircle className="h-4 w-4 text-success" />
                      <span><strong>混合格式：</strong>支持空行、制表符等常见分隔符</span>
                    </div>
                  </div>
                </div>

                {/* 处理流程 */}
                <div>
                  <h4 className="font-semibold mb-3 flex items-center space-x-2">
                    <Badge variant="outline" className="bg-secondary/10 text-secondary border-secondary/30">
                      <Database className="h-3 w-3 mr-1" />
                      处理流程
                    </Badge>
                  </h4>
                  <div className="space-y-3">
                    <div className="flex items-start space-x-3 p-3 bg-muted/50 rounded-lg">
                      <div className="w-6 h-6 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-xs font-bold">1</div>
                      <div>
                        <div className="font-medium">文本解析</div>
                        <div className="text-sm text-muted-foreground">自动识别分隔符，提取关键词列表</div>
                      </div>
                    </div>
                    <div className="flex items-start space-x-3 p-3 bg-muted/50 rounded-lg">
                      <div className="w-6 h-6 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-xs font-bold">2</div>
                      <div>
                        <div className="font-medium">智能去重</div>
                        <div className="text-sm text-muted-foreground">自动去除重复关键词和数据库已存在的关键词</div>
                      </div>
                    </div>
                    <div className="flex items-start space-x-3 p-3 bg-muted/50 rounded-lg">
                      <div className="w-6 h-6 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-xs font-bold">3</div>
                      <div>
                        <div className="font-medium">基础过滤</div>
                        <div className="text-sm text-muted-foreground">过滤过短关键词和格式不正确的内容</div>
                      </div>
                    </div>
                    <div className="flex items-start space-x-3 p-3 bg-muted/50 rounded-lg">
                      <div className="w-6 h-6 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-xs font-bold">4</div>
                      <div>
                        <div className="font-medium">数据入库</div>
                        <div className="text-sm text-muted-foreground">将通过筛选的关键词保存到数据库</div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* 注意事项 */}
                <div>
                  <h4 className="font-semibold mb-3 flex items-center space-x-2">
                    <Badge variant="outline" className="bg-warning/10 text-warning border-warning/30">
                      <AlertTriangle className="h-3 w-3 mr-1" />
                      注意事项
                    </Badge>
                  </h4>
                  <div className="space-y-2 text-sm text-muted-foreground">
                    <div className="flex items-center space-x-2">
                      <AlertTriangle className="h-4 w-4 text-warning" />
                      <span>每次最多支持导入1000个关键词</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <AlertTriangle className="h-4 w-4 text-warning" />
                      <span>基础过滤会移除过短和重复的关键词</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <AlertTriangle className="h-4 w-4 text-warning" />
                      <span>建议使用高质量的关键词列表以提高通过率</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
            </div>
          )}
        </div>
      </div>
    </ConsoleLayout>
  )
}
