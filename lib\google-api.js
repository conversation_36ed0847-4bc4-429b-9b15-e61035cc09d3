/**
 * Google搜索建议API调用器
 * 支持两个API接口轮换调用和XML响应解析
 */

// 检查是否需要使用代理（国内环境）
const isChina = process.env.NODE_ENV === 'production' ? false : true; // 开发环境假设是国内

class GoogleSuggestionsAPI {
  constructor() {
    // 两个Google搜索建议API接口
    this.apis = [
      {
        name: 'Google Suggest API 1',
        url: process.env.GOOGLE_SUGGESTIONS_API_1 || 'https://suggestqueries.google.com/complete/search',
        params: {
          client: 'chrome',
          hl: 'en',
          output: 'xml'
        }
      },
      {
        name: 'Google Suggest API 2',
        url: process.env.GOOGLE_SUGGESTIONS_API_2 || 'https://clients1.google.com/complete/search',
        params: {
          client: 'chrome',
          hl: 'en',
          output: 'xml'
        }
      }
    ];
    
    this.currentApiIndex = 0;
    this.requestDelay = 200; // 200ms延迟避免被限制
    this.lastRequestTime = 0;
    this.maxRetries = 2;
  }

  /**
   * 获取下一个API配置
   */
  getNextAPI() {
    const api = this.apis[this.currentApiIndex];
    this.currentApiIndex = (this.currentApiIndex + 1) % this.apis.length;
    return api;
  }

  /**
   * 确保请求间隔
   */
  async rateLimitDelay() {
    const now = Date.now();
    const timeSinceLastRequest = now - this.lastRequestTime;
    if (timeSinceLastRequest < this.requestDelay) {
      await new Promise(resolve => setTimeout(resolve, this.requestDelay - timeSinceLastRequest));
    }
    this.lastRequestTime = Date.now();
  }

  /**
   * 解析XML响应
   */
  parseXMLResponse(xmlText) {
    try {
      const suggestions = [];

      // 使用正则表达式解析XML中的suggestion标签
      const suggestionRegex = /<suggestion data="([^"]+)"/g;
      let match;

      while ((match = suggestionRegex.exec(xmlText)) !== null) {
        const suggestion = match[1];
        if (suggestion && suggestion.trim()) {
          // 解码HTML实体
          const decodedSuggestion = this.decodeHTMLEntities(suggestion.trim());
          suggestions.push(decodedSuggestion);
        }
      }

      return suggestions;
    } catch (error) {
      return [];
    }
  }

  /**
   * 解码HTML实体
   */
  decodeHTMLEntities(text) {
    const entities = {
      '&#39;': "'",
      '&quot;': '"',
      '&amp;': '&',
      '&lt;': '<',
      '&gt;': '>',
      '&nbsp;': ' '
    };

    return text.replace(/&#?\w+;/g, (entity) => {
      return entities[entity] || entity;
    });
  }

  /**
   * 简单的关键词过滤函数 - 过滤包含特殊符号的关键词
   */
  shouldFilterKeyword(keyword) {
    if (!keyword || typeof keyword !== 'string') return true;

    const keywordText = keyword.trim();

    // 过滤包含特殊符号的关键词（保留字母、数字、空格、连字符）
    if (/[^\w\s-]/.test(keywordText)) return true;

    return false;
  }

  /**
   * 获取搜索建议
   */
  async getSuggestions(query, maxSuggestions = 10) {
    if (!query || query.trim().length === 0) {
      return {
        success: false,
        error: '查询词不能为空',
        suggestions: []
      };
    }

    await this.rateLimitDelay();

    let lastError = null;
    
    // 尝试所有API接口
    for (let attempt = 0; attempt < this.maxRetries; attempt++) {
      const api = this.getNextAPI();
      
      try {
        // 构建请求URL
        const params = new URLSearchParams({
          ...api.params,
          q: query.trim()
        });

        const url = `${api.url}?${params.toString()}`;

        const response = await fetch(url, {
          method: 'GET',
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/xml,application/xml,application/xhtml+xml,text/html;q=0.9,text/plain;q=0.8,image/png,*/*;q=0.5',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
          }
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const xmlText = await response.text();

        const suggestions = this.parseXMLResponse(xmlText);

        if (suggestions.length > 0) {
          // 去重、排除原查询词并过滤特殊符号
          const filteredSuggestions = [...new Set(suggestions)]
            .filter(s => s.toLowerCase() !== query.toLowerCase() && !this.shouldFilterKeyword(s));

          // 限制返回数量
          const finalSuggestions = filteredSuggestions.slice(0, maxSuggestions);

          return {
            success: true,
            suggestions: finalSuggestions,
            source: api.name,
            total: finalSuggestions.length
          };
        }

      } catch (error) {
        lastError = error;
        
        // 如果不是最后一次尝试，等待一下再试下一个API
        if (attempt < this.maxRetries - 1) {
          await new Promise(resolve => setTimeout(resolve, 500));
        }
      }
    }

    return {
      success: false,
      error: lastError ? lastError.message : '所有API接口都失败了',
      suggestions: []
    };
  }

  /**
   * 批量获取多个关键词的搜索建议
   */
  async getBatchSuggestions(queries, maxSuggestionsPerQuery = 10) {
    const results = [];
    const total = queries.length;

    for (let i = 0; i < queries.length; i++) {
      const query = queries[i];

      try {
        const result = await this.getSuggestions(query, maxSuggestionsPerQuery);

        results.push({
          query: query,
          success: result.success,
          suggestions: result.suggestions || [],
          source: result.source,
          error: result.error
        });

        // 批量处理间的延迟
        if (i < queries.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 300));
        }

      } catch (error) {
        results.push({
          query: query,
          success: false,
          suggestions: [],
          error: error.message
        });
      }
    }
    
    return {
      success: true,
      total: total,
      processed: results.length,
      results: results
    };
  }

  /**
   * 获取API使用统计
   */
  getStats() {
    return {
      apis: this.apis.map(api => api.name),
      currentApiIndex: this.currentApiIndex,
      requestDelay: this.requestDelay,
      maxRetries: this.maxRetries
    };
  }
}

// 创建单例实例
const googleAPI = new GoogleSuggestionsAPI();

module.exports = googleAPI;
