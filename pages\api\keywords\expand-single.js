// HTML实体解码函数
function decodeHTMLEntities(text) {
  const entities = {
    '&#39;': "'",
    '&quot;': '"',
    '&amp;': '&',
    '&lt;': '<',
    '&gt;': '>',
    '&nbsp;': ' '
  };

  return text.replace(/&#?\w+;/g, (entity) => {
    return entities[entity] || entity;
  });
}

// 简单的关键词过滤函数 - 过滤包含特殊符号的关键词
function shouldFilterKeyword(keyword) {
  if (!keyword || typeof keyword !== 'string') return true;

  const keywordText = keyword.trim();

  // 过滤包含特殊符号的关键词（保留字母、数字、空格、连字符）
  if (/[^\w\s-]/.test(keywordText)) return true;

  return false;
}

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      message: 'Method not allowed'
    })
  }

  try {
    const { id } = req.body

    // 验证参数
    if (!id) {
      return res.status(400).json({
        success: false,
        message: '关键词ID不能为空'
      })
    }

    const apiBaseUrl = process.env.NEXT_PUBLIC_PHP_API_URL || 'https://api.xstty.com/api'

    // 先获取关键词信息
    const keywordResponse = await fetch(`${apiBaseUrl}/keywords.php?id=${encodeURIComponent(id)}`)
    const keywordData = await keywordResponse.json()

    if (!keywordData.success) {
      return res.status(404).json({
        success: false,
        message: '关键词不存在'
      })
    }

    const keyword = keywordData.data.keyword

    // Google搜索建议API列表 - 使用英文获取英文关键词
    const googleApis = [
      'https://suggestqueries.google.com/complete/search?output=toolbar&hl=en&q=',
      'https://clients1.google.com/complete/search?hl=en&output=toolbar&q='
    ]

    let suggestions = []
    let usedSource = ''

    // 轮换使用两个API
    for (let i = 0; i < googleApis.length; i++) {
      try {
        const apiUrl = googleApis[i] + encodeURIComponent(keyword)

        const response = await fetch(apiUrl, {
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/xml,application/xml,application/xhtml+xml,text/html;q=0.9,text/plain;q=0.8,image/png,*/*;q=0.5',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
          },
          timeout: 10000
        })

        if (response.ok) {
          const xmlText = await response.text()

          // 解析XML响应
          const suggestionMatches = xmlText.match(/<suggestion data="([^"]+)"/g)
          if (suggestionMatches) {
            suggestions = suggestionMatches.map(match => {
              const suggestion = match.match(/data="([^"]+)"/)[1]
              // 完整的HTML实体解码
              const decoded = decodeHTMLEntities(suggestion)
              return decoded
            }).filter(s => s && s !== keyword && s.length > 1 && !shouldFilterKeyword(s)) // 过滤掉原关键词、太短的建议和包含&符号的关键词

            usedSource = i === 0 ? 'google_suggestions_1' : 'google_suggestions_2'
            break
          }
        }
      } catch {
        continue
      }
    }

    if (suggestions.length === 0) {
      return res.status(200).json({
        success: true,
        message: '暂无相关搜索建议',
        data: {
          keyword_id: id,
          suggestions: [],
          source: 'no_suggestions',
          total: 0
        }
      })
    }

    // 限制建议数量
    const limitedSuggestions = suggestions.slice(0, 10)

    // 调用PHP API保存建议
    try {
      const saveResponse = await fetch(`${apiBaseUrl}/keyword_suggestions.php`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          keyword_id: id,
          suggestions: limitedSuggestions,
          source: usedSource
        })
      })

      await saveResponse.json()
    } catch {
      // 静默处理保存错误
    }

    // 无论保存是否成功，都返回建议给前端
    res.status(200).json({
      success: true,
      message: `成功获取 ${limitedSuggestions.length} 个搜索建议`,
      data: {
        keyword_id: id,
        suggestions: limitedSuggestions,
        source: usedSource,
        total: limitedSuggestions.length
      }
    })

  } catch {
    res.status(500).json({
      success: false,
      message: '服务器错误，请稍后重试'
    })
  }
}
