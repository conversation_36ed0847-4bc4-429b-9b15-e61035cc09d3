/**
 * Google API使用统计追踪器
 * 用于在调用Google搜索API时记录使用次数
 */

class GoogleUsageTracker {
  constructor() {
    this.apiBaseUrl = process.env.NEXT_PUBLIC_PHP_API_URL || 'https://api.xstty.com/api';
  }

  /**
   * 记录Google API使用次数
   * @param {number} keyIndex - API密钥索引 (1-20)
   * @param {number} count - 使用次数，默认1次
   * @param {string} date - 使用日期 (YYYY-MM-DD)，可选，默认今天
   */
  async recordUsage(keyIndex, count = 1, date = null) {
    try {
      if (!keyIndex || keyIndex < 1 || keyIndex > 20) {
        return { success: false, error: '无效的密钥索引' };
      }

      const requestData = {
        key_index: keyIndex,
        count: count,
        date: date || new Date().toISOString().split('T')[0] // YYYY-MM-DD格式
      };

      const response = await fetch(`${this.apiBaseUrl}/google-usage.php`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData)
      });

      if (!response.ok) {
        throw new Error('API请求失败');
      }

      const data = await response.json();
      return data;

    } catch (error) {
      // 静默处理错误，不影响主要功能
      return { success: false, error: error.message };
    }
  }

  /**
   * 获取API使用统计
   * @param {number|null} keyIndex - API密钥索引，可选
   * @param {string} date - 查询日期，可选，默认今天
   */
  async getUsage(keyIndex = null, date = null) {
    try {
      const params = new URLSearchParams();
      if (keyIndex) params.append('key_index', keyIndex);
      if (date) params.append('date', date);
      else params.append('date', new Date().toISOString().split('T')[0]);

      const response = await fetch(`${this.apiBaseUrl}/google-usage.php?${params.toString()}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      if (!response.ok) {
        throw new Error('API请求失败');
      }

      const data = await response.json();
      return data;

    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  /**
   * 获取可用的Google API密钥（使用次数最少的）
   * @param {number} maxUsage - 最大使用次数限制，默认100
   */
  async getAvailableKey(maxUsage = 100) {
    try {
      const usageData = await this.getUsage();
      
      if (!usageData.success || !usageData.data) {
        // 如果获取失败，返回随机密钥
        return Math.floor(Math.random() * 20) + 1;
      }

      // 找到使用次数最少且未超过限制的密钥
      const availableKeys = usageData.data
        .filter(key => key.usage_count < maxUsage)
        .sort((a, b) => a.usage_count - b.usage_count);

      if (availableKeys.length === 0) {
        // 如果所有密钥都超过限制，返回使用次数最少的
        const leastUsedKey = usageData.data.sort((a, b) => a.usage_count - b.usage_count)[0];
        return leastUsedKey.api_key_index;
      }

      return availableKeys[0].api_key_index;

    } catch (error) {
      // 如果获取失败，返回随机密钥
      return Math.floor(Math.random() * 20) + 1;
    }
  }

  /**
   * 包装Google API调用，自动记录使用次数
   * @param {Function} apiCall - API调用函数
   * @param {number} keyIndex - 使用的密钥索引
   */
  async wrapApiCall(apiCall, keyIndex) {
    try {
      // 执行API调用
      const result = await apiCall();
      
      // 记录使用次数（异步，不等待结果）
      this.recordUsage(keyIndex).catch(() => {
        // 静默处理记录错误
      });

      return result;

    } catch (error) {
      // 即使API调用失败，也记录使用次数
      this.recordUsage(keyIndex).catch(() => {
        // 静默处理记录错误
      });

      throw error;
    }
  }
}

// 创建单例实例
const googleUsageTracker = new GoogleUsageTracker();

export default googleUsageTracker;
