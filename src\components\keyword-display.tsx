'use client'

import { useState, useEffect, useCallback } from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from './ui/card'
import { Badge } from './ui/badge'
import { Button } from './ui/button'
import { ExternalLink, MessageSquare, Target, TrendingUp, Globe, Star, Eye, Zap, Loader2, Copy, Check, X, Search } from 'lucide-react'
import Link from 'next/link'
import { toast } from 'sonner'

interface Category {
  id: string
  name: string
  english_name: string
  keyword_count: number
}

type Suggestion = string | {
  id?: string
  suggestion: string
}

interface KeywordDisplayProps {
  keyword: {
    id: string
    keyword: string
    user_intent?: string
    user_pain_point?: string
    competition_level?: 'easy' | 'medium' | 'hard'
    competition_score?: number
    competition_color?: string
    competition_description?: string
    serp_analysis?: string
    user_comment?: string
    category?: string
    recommended_domains?: Array<{
      domain: string
      check_url: string
    }>
    is_analyzed: boolean
    created_at_formatted?: string
    analyzed_at_formatted?: string
    source?: string
  }
  onComment?: (keywordId: string, comment: string) => void
}

export default function KeywordDisplay({ keyword, onComment }: KeywordDisplayProps) {
  const [showComment, setShowComment] = useState(false)
  const [comment, setComment] = useState(keyword.user_comment || '')
  const [isSubmittingComment, setIsSubmittingComment] = useState(false)
  const [categories, setCategories] = useState<Category[]>([])
  const [isExpanding, setIsExpanding] = useState(false)
  const [suggestions, setSuggestions] = useState<Suggestion[]>([])
  const [showSuggestions, setShowSuggestions] = useState(false)
  const [expandedSuggestions, setExpandedSuggestions] = useState<string[]>([])
  const [isExpandingMore, setIsExpandingMore] = useState(false)

  // 加载已有的搜索建议
  const loadExistingSuggestions = useCallback(async () => {
    try {
      const timestamp = Date.now()
      const response = await fetch(`/api/keywords/suggestions?keyword_id=${encodeURIComponent(keyword.id)}&t=${timestamp}`, {
        cache: 'no-store',
        headers: {
          'Cache-Control': 'no-cache'
        }
      })
      const data = await response.json()

      if (data.success && data.data.suggestions && data.data.suggestions.length > 0) {
        setSuggestions(data.data.suggestions)
        setShowSuggestions(true)
      }
    } catch (_error) {
      // 静默处理错误
    }
  }, [keyword.id])

  // 获取分类数据
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const timestamp = Date.now()
        const response = await fetch(`/api/categories/list?t=${timestamp}`, {
          cache: 'no-store',
          headers: {
            'Cache-Control': 'no-cache'
          }
        })
        const data = await response.json()

        if (data.success && data.data.categories) {
          setCategories(data.data.categories)
        }
      } catch (_error) {
        // 静默处理错误
      }
    }

    fetchCategories()
    loadExistingSuggestions()
  }, [loadExistingSuggestions])

  // 获取中文分类名
  const getCategoryName = (englishName: string) => {
    const category = categories.find(c => c.english_name === englishName)
    return category ? category.name : englishName
  }

  // 处理关键词拓展
  const handleExpand = async () => {
    setIsExpanding(true)
    try {
      const timestamp = Date.now()
      const response = await fetch(`/api/keywords/expand-single?t=${timestamp}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache'
        },
        body: JSON.stringify({
          id: keyword.id
        })
      })

      const data = await response.json()

      if (data.success && data.data) {
        if (data.data.suggestions && data.data.suggestions.length > 0) {
          // 重新加载已保存的建议（包含ID等完整信息）
          await loadExistingSuggestions()
          toast.success(`成功获取 ${data.data.suggestions.length} 个相关建议`)
        } else {
          toast.info('未找到相关搜索建议')
        }
      } else {
        toast.error(data.message || '获取搜索建议失败')
      }
    } catch (_error) {
      toast.error('网络错误，请稍后重试')
    } finally {
      setIsExpanding(false)
    }
  }

  // 复制建议到剪贴板
  const handleCopySuggestion = async (suggestion: string) => {
    try {
      await navigator.clipboard.writeText(suggestion)
      toast.success(`已复制: ${suggestion}`)
    } catch (_error) {
      toast.error('复制失败，请手动复制')
    }
  }

  // 一键复制所有建议
  const handleCopyAllSuggestions = async () => {
    try {
      const allSuggestions = suggestions.map(s => typeof s === 'string' ? s : s.suggestion).join('\n')
      await navigator.clipboard.writeText(allSuggestions)
      toast.success(`已复制 ${suggestions.length} 个相关词`)
    } catch (_error) {
      toast.error('复制失败，请手动复制')
    }
  }

  // 一键复制拓展建议
  const handleCopyExpandedSuggestions = async () => {
    try {
      const allExpanded = expandedSuggestions.join('\n')
      await navigator.clipboard.writeText(allExpanded)
      toast.success(`已复制 ${expandedSuggestions.length} 个拓展词`)
    } catch (_error) {
      toast.error('复制失败，请手动复制')
    }
  }

  // Google搜索相关词
  const handleSearchRelatedKeywords = () => {
    if (suggestions.length === 0) return

    suggestions.forEach((suggestion, index) => {
      setTimeout(() => {
        const keyword = typeof suggestion === 'string' ? suggestion : suggestion.suggestion
        const searchUrl = `https://www.google.com/search?q=${encodeURIComponent(keyword)}&hl=en&gl=us&tbs=qdr:w`
        window.open(searchUrl, '_blank')
      }, index * 500) // 每个搜索间隔500ms，避免浏览器阻止弹窗
    })

    toast.success(`正在打开 ${suggestions.length} 个Google搜索页面`)
  }

  // Google搜索拓展关键词
  const handleSearchExpandedKeywords = () => {
    if (expandedSuggestions.length === 0) return

    expandedSuggestions.forEach((keyword, index) => {
      setTimeout(() => {
        const searchUrl = `https://www.google.com/search?q=${encodeURIComponent(keyword)}&hl=en&gl=us&tbs=qdr:w`
        window.open(searchUrl, '_blank')
      }, index * 500) // 每个搜索间隔500ms，避免浏览器阻止弹窗
    })

    toast.success(`正在打开 ${expandedSuggestions.length} 个Google搜索页面`)
  }

  // 继续拓展 - 基于现有建议获取更多相关词
  const handleExpandMore = async () => {
    if (suggestions.length === 0) return

    // 显示提示信息
    toast.info('正在拓展相关词，这可能需要2-3分钟，请稍等...')

    setIsExpandingMore(true)
    try {
      const timestamp = Date.now()
      const response = await fetch(`/api/keywords/expand-more?t=${timestamp}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache'
        },
        body: JSON.stringify({
          keyword_id: keyword.id,
          suggestions: suggestions.map(s => typeof s === 'string' ? s : s.suggestion)
        })
      })

      const data = await response.json()

      if (data.success && data.data && data.data.length > 0) {
        setExpandedSuggestions(data.data)
        toast.success(`成功拓展 ${data.data.length} 个相关词`)
      } else {
        toast.info('未找到更多相关词')
      }
    } catch (_error) {
      toast.error('拓展失败，请稍后重试')
    } finally {
      setIsExpandingMore(false)
    }
  }

  // 获取竞争难度显示信息 - 统一的分数区间标准
  const getCompetitionInfo = () => {
    if (!keyword.competition_score) {
      return { text: '未分析', color: '#B45309', bgColor: '#B45309' }
    }

    // 统一的分数区间标准：简单(1-3)、中等(4-7)、困难(8-10)
    const score = keyword.competition_score
    if (score <= 3) {
      return { text: '简单', color: '#15803d', bgColor: '#15803d' }
    } else if (score <= 7) {
      return { text: '中等', color: '#ca8a04', bgColor: '#ca8a04' }
    } else {
      return { text: '困难', color: '#dc2626', bgColor: '#dc2626' }
    }
  }

  const competitionInfo = getCompetitionInfo()

  // 处理评论提交
  const handleCommentSubmit = async () => {
    if (!comment.trim()) return

    setIsSubmittingComment(true)
    try {
      if (onComment) {
        await onComment(keyword.id, comment.trim())
      }
      setShowComment(false)
    } catch (_error) {
      // 静默处理错误
    } finally {
      setIsSubmittingComment(false)
    }
  }



  return (
    <Card className="idea-card-enhanced group">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <CardTitle className="text-xl font-bold text-foreground mb-3 group-hover:text-primary transition-colors">
              {keyword.keyword}
            </CardTitle>
            <div className="flex items-center gap-2 flex-wrap">
              {/* 分析状态 - 如果未分析，只显示待分析标签 */}
              {!keyword.is_analyzed ? (
                <Badge className="badge-pending">
                  待分析
                </Badge>
              ) : (
                <>
                  {/* 已分析状态 */}
                  <Badge className="badge-analyzed">
                    已分析
                  </Badge>

                  {/* 分类 */}
                  {keyword.category && (
                    <Badge variant="outline" className="text-xs border-primary/20 text-primary">
                      {getCategoryName(keyword.category)}
                    </Badge>
                  )}

                  {/* 竞争难度 - 基于分数区间显示 */}
                  <Badge
                    className={`text-xs font-medium ${
                      (keyword.competition_score || 0) <= 3 ? 'badge-competition-easy' :
                      (keyword.competition_score || 0) <= 7 ? 'badge-competition-medium' :
                      keyword.competition_score ? 'badge-competition-hard' :
                      'badge-pending'
                    }`}
                  >
                    {competitionInfo.text} {keyword.competition_score && `(${keyword.competition_score}/10)`}
                  </Badge>
                </>
              )}

              {/* 数据源 - 去掉google_trends显示 */}
              {keyword.source && keyword.source !== 'google_trends' && (
                <Badge className="badge-trend text-xs">
                  {keyword.source}
                </Badge>
              )}
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="flex gap-2">
            <Button asChild size="sm" className="bg-blue-500 hover:bg-blue-600 text-white border-0 shadow-sm hover:shadow-md transition-all duration-200 hover:scale-105 active:scale-95 text-xs">
              <Link href={`/keyword/${keyword.id}`} target="_blank" rel="noopener noreferrer">
                <Eye className="w-3 h-3 mr-1" />
                查看详情
              </Link>
            </Button>



            {/* 拓展按钮 */}
            <Button
              onClick={handleExpand}
              size="sm"
              className="bg-green-500 hover:bg-green-600 text-white border-0 shadow-sm hover:shadow-md transition-all duration-200 hover:scale-105 active:scale-95 text-xs disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
              disabled={isExpanding}
            >
              {isExpanding ? (
                <>
                  <Loader2 className="w-3 h-3 mr-1 animate-spin" />
                  拓展中...
                </>
              ) : (
                <>
                  <Zap className="w-3 h-3 mr-1" />
                  拓展
                </>
              )}
            </Button>

            {/* 备注按钮或评价显示 */}
            {keyword.user_comment ? (
              <Badge
                className={`font-medium rounded-lg ${keyword.user_comment === '有效'
                  ? 'bg-blue-500 text-white border-blue-500 hover:bg-blue-600 transition-all duration-200'
                  : 'bg-red-500 text-white border-red-500 hover:bg-red-600 transition-all duration-200'
                }`}
              >
                {keyword.user_comment === '有效' ? (
                  <Check className="w-3 h-3 mr-1" />
                ) : (
                  <X className="w-3 h-3 mr-1" />
                )}
                <span className={keyword.user_comment === '无效' ? 'line-through' : ''}>
                  {keyword.user_comment}
                </span>
              </Badge>
            ) : (
              <Button
                onClick={() => setShowComment(!showComment)}
                size="sm"
                className="bg-orange-500/90 hover:bg-orange-500 text-white border-0 shadow-sm hover:shadow-md transition-all duration-200 hover:scale-105 active:scale-95 text-xs"
              >
                <MessageSquare className="w-3 h-3 mr-1" />
                评价
              </Button>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* AI分析结果 */}
        {keyword.is_analyzed && (
          <div className="space-y-3">
            {/* 用户意图 */}
            {keyword.user_intent && (
              <div className="bg-blue-50 p-3 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <Target className="w-4 h-4 text-blue-600" />
                  <span className="font-medium text-blue-900">用户意图</span>
                </div>
                <div className="text-sm text-blue-800">{keyword.user_intent}</div>
              </div>
            )}

            {/* 用户痛点 */}
            {keyword.user_pain_point && (
              <div className="bg-orange-50 p-3 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <TrendingUp className="w-4 h-4 text-orange-600" />
                  <span className="font-medium text-orange-900">用户痛点</span>
                </div>
                <div className="text-sm text-orange-800">{keyword.user_pain_point}</div>
              </div>
            )}

            {/* 竞争分析 */}
            {keyword.competition_description && (
              <div className="bg-purple-50 p-3 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <Star className="w-4 h-4 text-purple-600" />
                  <span className="font-medium text-purple-900">竞争分析</span>
                </div>
                <div className="text-sm text-purple-800">{keyword.competition_description}</div>
              </div>
            )}

            {/* SERP分析 */}
            {keyword.serp_analysis && (
              <div className="bg-green-50 p-3 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <Globe className="w-4 h-4 text-green-600" />
                  <span className="font-medium text-green-900">SERP分析</span>
                </div>
                <p className="text-sm text-green-800">{keyword.serp_analysis}</p>
              </div>
            )}
          </div>
        )}

        {/* 推荐域名 */}
        {keyword.recommended_domains && keyword.recommended_domains.length > 0 && (
          <div className="bg-gray-50 p-3 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <Globe className="w-4 h-4 text-gray-600" />
              <span className="font-medium text-gray-900">推荐域名</span>
            </div>
            <div className="flex flex-wrap gap-2">
              {keyword.recommended_domains.map((domain, index) => (
                <a
                  key={index}
                  href={domain.check_url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center gap-1 px-2 py-1 bg-white border border-gray-200 rounded text-xs hover:bg-gray-100 transition-colors"
                >
                  {domain.domain}
                  <ExternalLink className="w-3 h-3" />
                </a>
              ))}
            </div>
          </div>
        )}

        {/* 相关词操作按钮 */}
        {showSuggestions && suggestions.length > 0 && (
          <div className="flex gap-2 justify-center py-2">
            <Button
              onClick={handleCopyAllSuggestions}
              size="sm"
              variant="outline"
              className="text-xs h-8 border-purple-300 text-purple-700 hover:bg-purple-100 hover:border-purple-400 hover:text-purple-800 hover:shadow-sm hover:scale-105 active:scale-95 transition-all duration-200 rounded-lg"
            >
              <Copy className="w-3 h-3 mr-1" />
              一键复制
            </Button>
            <Button
              onClick={handleExpandMore}
              size="sm"
              variant="outline"
              className="text-xs h-8 border-purple-300 text-purple-700 hover:bg-purple-100 hover:border-purple-400 hover:text-purple-800 hover:shadow-sm hover:scale-105 active:scale-95 transition-all duration-200 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100"
              disabled={isExpandingMore}
            >
              {isExpandingMore ? (
                <>
                  <Loader2 className="w-3 h-3 mr-1 animate-spin" />
                  拓展中...
                </>
              ) : (
                <>
                  <TrendingUp className="w-3 h-3 mr-1" />
                  继续拓展
                </>
              )}
            </Button>
            <Button
              onClick={handleSearchRelatedKeywords}
              size="sm"
              variant="outline"
              className="text-xs h-8 border-purple-300 text-purple-700 hover:bg-purple-100 hover:border-purple-400 hover:text-purple-800 hover:shadow-sm hover:scale-105 active:scale-95 transition-all duration-200 rounded-lg"
              title="在Google中搜索所有相关词"
            >
              <Search className="w-3 h-3 mr-1" />
              查询索引
            </Button>
          </div>
        )}

        {/* 相关词展示 */}
        {showSuggestions && suggestions.length > 0 && (
          <div className="bg-purple-50 p-3 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <Zap className="w-4 h-4 text-purple-600" />
              <span className="font-medium text-purple-900">相关词</span>
              <Badge variant="outline" className="text-xs">
                {suggestions.length} 个
              </Badge>
            </div>
            <div className="flex flex-wrap gap-2">
              {suggestions.map((suggestion, index) => (
                <div
                  key={typeof suggestion === 'string' ? index : (suggestion.id || index)}
                  onClick={() => handleCopySuggestion(typeof suggestion === 'string' ? suggestion : suggestion.suggestion)}
                  className="inline-flex items-center gap-1 px-2 py-1 bg-white border border-purple-200 rounded text-xs text-purple-700 hover:bg-purple-100 cursor-pointer transition-colors"
                  title="点击复制"
                >
                  <span>{typeof suggestion === 'string' ? suggestion : suggestion.suggestion}</span>
                  <Copy className="w-3 h-3 opacity-50" />
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 继续拓展操作按钮 */}
        {expandedSuggestions.length > 0 && (
          <div className="flex gap-2 justify-center py-2">
            <Button
              onClick={handleCopyExpandedSuggestions}
              size="sm"
              variant="outline"
              className="text-xs h-8 border-green-300 text-green-700 hover:bg-green-100 hover:border-green-400 hover:text-green-800 hover:shadow-sm hover:scale-105 active:scale-95 transition-all duration-200 rounded-lg"
            >
              <Copy className="w-3 h-3 mr-1" />
              一键复制
            </Button>
            <Button
              onClick={handleSearchExpandedKeywords}
              size="sm"
              variant="outline"
              className="text-xs h-8 border-green-300 text-green-700 hover:bg-green-100 hover:border-green-400 hover:text-green-800 hover:shadow-sm hover:scale-105 active:scale-95 transition-all duration-200 rounded-lg"
              title="在Google中搜索所有拓展关键词"
            >
              <Search className="w-3 h-3 mr-1" />
              查询索引
            </Button>
          </div>
        )}

        {/* 继续拓展的关键词展示 */}
        {expandedSuggestions.length > 0 && (
          <div className="bg-green-50 p-3 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <TrendingUp className="w-4 h-4 text-green-600" />
              <span className="font-medium text-green-900">继续拓展的关键词</span>
              <Badge variant="outline" className="text-xs">
                {expandedSuggestions.length} 个
              </Badge>
            </div>
            <div className="flex flex-wrap gap-2">
              {expandedSuggestions.map((suggestion, index) => (
                <div
                  key={index}
                  onClick={() => handleCopySuggestion(suggestion)}
                  className="inline-flex items-center gap-1 px-2 py-1 bg-white border border-green-200 rounded text-xs text-green-700 hover:bg-green-100 cursor-pointer transition-colors"
                  title="点击复制"
                >
                  <span>{suggestion}</span>
                  <Copy className="w-3 h-3 opacity-50" />
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 用户评价区域 */}
        {showComment && (
          <div className="bg-yellow-50 p-3 rounded-lg">
            <div className="flex items-center gap-2 mb-3">
              <MessageSquare className="w-4 h-4 text-yellow-600" />
              <span className="font-medium text-yellow-900">关键词评价</span>
            </div>
            <div className="space-y-3">
              <div className="flex gap-4">
                <label className="flex items-center cursor-pointer">
                  <input
                    type="radio"
                    name="comment"
                    value="有效"
                    checked={comment === '有效'}
                    onChange={(e) => setComment(e.target.value)}
                    className="mr-2"
                  />
                  <span className="text-sm text-blue-700 font-medium">有效</span>
                </label>
                <label className="flex items-center cursor-pointer">
                  <input
                    type="radio"
                    name="comment"
                    value="无效"
                    checked={comment === '无效'}
                    onChange={(e) => setComment(e.target.value)}
                    className="mr-2"
                  />
                  <span className="text-sm text-red-700 font-medium">无效</span>
                </label>
              </div>
              <div className="flex gap-2">
                <Button
                  onClick={handleCommentSubmit}
                  disabled={isSubmittingComment || !comment.trim()}
                  size="sm"
                  className="text-xs"
                >
                  {isSubmittingComment ? '提交中...' : '提交评价'}
                </Button>
                <Button
                  onClick={() => setShowComment(false)}
                  size="sm"
                  variant="outline"
                  className="text-xs"
                >
                  取消
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* 现有用户评论 */}
        {keyword.user_comment && !showComment && (
          <div className={`p-3 rounded-lg ${
            keyword.user_comment === '有效'
              ? 'bg-blue-50 border border-blue-200'
              : 'bg-red-50 border border-red-200'
          }`}>
            <div className="flex items-center gap-2 mb-2">
              {keyword.user_comment === '有效' ? (
                <Check className="w-4 h-4 text-blue-600" />
              ) : (
                <X className="w-4 h-4 text-red-600" />
              )}
              <span className={`font-medium ${
                keyword.user_comment === '有效' ? 'text-blue-900' : 'text-red-900'
              }`}>
                用户评价
              </span>
            </div>
            <p className={`text-sm font-medium ${
              keyword.user_comment === '有效'
                ? 'text-blue-800'
                : 'text-red-800 line-through'
            }`}>
              {keyword.user_comment}
            </p>
          </div>
        )}

        {/* 时间信息 */}
        <div className="flex justify-between text-xs text-gray-500 pt-2 border-t">
          <span>创建: {keyword.created_at_formatted || '未知'}</span>
          {keyword.analyzed_at_formatted && (
            <span>分析: {keyword.analyzed_at_formatted}</span>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
