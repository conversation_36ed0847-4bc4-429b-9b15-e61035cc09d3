'use client'

import React from 'react'
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '../../components/ui/card'
import { Badge } from '../../components/ui/badge'
import { Button } from '../../components/ui/button'
import { BookOpen, Target, Search, Code, TrendingUp, CheckCircle, ArrowRight, Lightbulb, Zap, Users } from 'lucide-react'
import FrontendHeader from '../../components/frontend-header'
import FrontendFooter from '../../components/frontend-footer'

export default function LandingPageSEOGuidePage() {
  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId)
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' })
    }
  }

  return (
    <div className="min-h-screen bg-background">
      <FrontendHeader />
      

      <section className="py-12 px-4 bg-gradient-to-r from-blue-50 via-purple-50 to-pink-50 border-b">
        <div className="container mx-auto text-center">
          <div className="flex items-center justify-center mb-6">
            <div className="p-4 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl mr-4">
              <BookOpen className="h-10 w-10 text-white" />
            </div>
            <h1 className="text-5xl font-bold text-gray-900">落地页SEO优化指南</h1>
          </div>
          <p className="text-2xl text-gray-700 max-w-4xl mx-auto mb-8">
            掌握落地页SEO核心策略，通过标题优化、关键词布局和内容架构提升搜索排名，实现流量增长和转化提升
          </p>
          <div className="flex flex-wrap justify-center gap-4 mb-8">
            <Badge className="bg-blue-100 text-blue-800 px-4 py-2 text-lg">标题优化</Badge>
            <Badge className="bg-green-100 text-green-800 px-4 py-2 text-lg">关键词布局</Badge>
            <Badge className="bg-purple-100 text-purple-800 px-4 py-2 text-lg">内容架构</Badge>
            <Badge className="bg-orange-100 text-orange-800 px-4 py-2 text-lg">技术优化</Badge>
          </div>
          <div className="flex flex-wrap justify-center gap-4">
            <Button 
              onClick={() => scrollToSection('title-optimization')}
              className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white px-8 py-3 text-lg"
            >
              <Target className="h-5 w-5 mr-2" />
              开始学习
            </Button>
            <Button 
              onClick={() => scrollToSection('quick-checklist')}
              variant="outline"
              className="border-2 border-gray-300 hover:border-gray-500 px-8 py-3 text-lg"
            >
              <CheckCircle className="h-5 w-5 mr-2" />
              快速检查清单
            </Button>
          </div>
        </div>
      </section>


      <section className="py-6 px-4 bg-white border-b sticky top-0 z-10">
        <div className="container mx-auto">
          <div className="flex flex-wrap justify-center gap-2">
            {[
              { id: 'keyword-research', label: '关键词研究', icon: Search },
              { id: 'title-optimization', label: '标题优化', icon: Target },
              { id: 'description-optimization', label: '描述优化', icon: BookOpen },
              { id: 'keyword-layout', label: '关键词布局', icon: Users },
              { id: 'content-structure', label: '内容架构', icon: BookOpen },
              { id: 'technical-seo', label: '技术优化', icon: Code },
              { id: 'page-architecture', label: '页面架构', icon: TrendingUp },
              { id: 'schema-data', label: 'Schema数据', icon: Code },
              { id: 'landing-page-types', label: '落地页类型', icon: TrendingUp },
              { id: 'quick-checklist', label: '检查清单', icon: CheckCircle }
            ].map(({ id, label, icon: Icon }) => (
              <Button
                key={id}
                onClick={() => scrollToSection(id)}
                variant="ghost"
                className="text-sm hover:bg-blue-50 hover:text-blue-600"
              >
                <Icon className="h-4 w-4 mr-1" />
                {label}
              </Button>
            ))}
          </div>
        </div>
      </section>


      <section className="py-12 px-4">
        <div className="container mx-auto max-w-6xl">


          <div id="keyword-research" className="mb-16">
            <div className="text-center mb-12">
              <h2 className="text-4xl font-bold text-gray-900 mb-4">数据准备与关键词研究</h2>
              <p className="text-xl text-gray-600">SEO优化的第一步是收集和分析关键词数据，建立科学的关键词体系</p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Search className="h-6 w-6 mr-2 text-blue-600" />
                    关键词收集流程
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="p-4 bg-blue-50 rounded-lg">
                      <p className="font-semibold text-blue-800 mb-2">工具使用</p>
                      <p className="text-sm text-gray-700">利用Ahrefs、SEMrush或Ubersuggest等工具收集目标关键词</p>
                    </div>
                    <div className="p-4 bg-green-50 rounded-lg">
                      <p className="font-semibold text-green-800 mb-2">收集范围</p>
                      <p className="text-sm text-gray-700">围绕核心业务功能，收集相关搜索词汇</p>
                    </div>
                    <div className="p-4 bg-purple-50 rounded-lg">
                      <p className="font-semibold text-purple-800 mb-2">数据整理</p>
                      <p className="text-sm text-gray-700">按搜索量、竞争难度、商业价值三个维度排序</p>
                    </div>
                    <div className="p-4 bg-orange-50 rounded-lg">
                      <p className="font-semibold text-orange-800 mb-2">筛选原则</p>
                      <p className="text-sm text-gray-700">选择前50-100个高价值关键词作为核心布局词汇</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Target className="h-6 w-6 mr-2 text-green-600" />
                    关键词优先级
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="p-3 bg-red-50 rounded-lg border-l-4 border-red-400">
                      <p className="font-semibold text-red-800">3级（最高）</p>
                      <p className="text-sm text-gray-700">产品直接解决方案 - "AI image generator"</p>
                    </div>
                    <div className="p-3 bg-orange-50 rounded-lg border-l-4 border-orange-400">
                      <p className="font-semibold text-orange-800">2级（高）</p>
                      <p className="text-sm text-gray-700">产品重要功能 - "text to image converter"</p>
                    </div>
                    <div className="p-3 bg-yellow-50 rounded-lg border-l-4 border-yellow-400">
                      <p className="font-semibold text-yellow-800">1级（中）</p>
                      <p className="text-sm text-gray-700">相关需求 - "digital art creation"</p>
                    </div>
                    <div className="p-3 bg-gray-50 rounded-lg border-l-4 border-gray-400">
                      <p className="font-semibold text-gray-800">0级（低）</p>
                      <p className="text-sm text-gray-700">泛需求 - "creative tools"</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          <div id="title-optimization" className="mb-16">
            <div className="text-center mb-12">
              <h2 className="text-4xl font-bold text-gray-900 mb-4">页面标题(Title)优化策略</h2>
              <p className="text-xl text-gray-600">标题是SEO最重要的排名因素，直接影响点击率和搜索排名</p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Target className="h-6 w-6 mr-2 text-blue-600" />
                    标题结构公式
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="bg-blue-50 p-6 rounded-lg mb-4">
                    <p className="text-lg font-semibold text-blue-800 text-center">
                      [主关键词] + [功能描述] + [差异化特色] + [品牌名]
                    </p>
                  </div>
                  <div className="space-y-3">
                    <div className="flex items-center">
                      <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                      <span>关键词前置：主关键词置于开头</span>
                    </div>
                    <div className="flex items-center">
                      <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                      <span>功能明确：突出核心功能价值</span>
                    </div>
                    <div className="flex items-center">
                      <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                      <span>特色突出：添加独特卖点</span>
                    </div>
                    <div className="flex items-center">
                      <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                      <span>长度控制：50-60个字符以内</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Lightbulb className="h-6 w-6 mr-2 text-yellow-600" />
                    标题优化示例
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="p-4 bg-red-50 rounded-lg border-l-4 border-red-400">
                      <p className="text-sm text-red-600 font-semibold mb-1">优化前 ❌</p>
                      <p className="text-gray-800">Image Generator Tool - Create Pictures Online</p>
                    </div>
                    <div className="p-4 bg-green-50 rounded-lg border-l-4 border-green-400">
                      <p className="text-sm text-green-600 font-semibold mb-1">优化后 ✅</p>
                      <p className="text-gray-800">AI Image Generator - Free HD Art Creator | ImageCraft</p>
                    </div>
                    <div className="p-4 bg-blue-50 rounded-lg">
                      <p className="text-sm text-blue-600 font-semibold mb-2">优化要点分析：</p>
                      <ul className="text-sm text-gray-700 space-y-1">
                        <li>• 主关键词"AI Image Generator"前置</li>
                        <li>• 突出"Free HD"差异化特色</li>
                        <li>• 品牌名"ImageCraft"增强记忆</li>
                        <li>• 总长度控制在合理范围内</li>
                      </ul>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          <div id="description-optimization" className="mb-16">
            <div className="text-center mb-12">
              <h2 className="text-4xl font-bold text-gray-900 mb-4">页面描述(Description)优化策略</h2>
              <p className="text-xl text-gray-600">Meta描述是搜索结果中的重要展示元素，直接影响用户点击率</p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <BookOpen className="h-6 w-6 mr-2 text-purple-600" />
                    描述结构框架
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="bg-purple-50 p-6 rounded-lg mb-4">
                    <p className="text-lg font-semibold text-purple-800 text-center">
                      [核心功能介绍] + [用户价值] + [特色优势] + [行动召唤]
                    </p>
                  </div>
                  <div className="space-y-3">
                    <div className="flex items-center">
                      <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                      <span>功能概述：开头清晰说明网站/产品用途</span>
                    </div>
                    <div className="flex items-center">
                      <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                      <span>价值突出：强调为用户带来的具体好处</span>
                    </div>
                    <div className="flex items-center">
                      <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                      <span>特色展示：突出与竞品的差异化优势</span>
                    </div>
                    <div className="flex items-center">
                      <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                      <span>长度控制：保持在150-160个字符以内</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Lightbulb className="h-6 w-6 mr-2 text-yellow-600" />
                    描述优化示例
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="p-4 bg-green-50 rounded-lg border-l-4 border-green-400">
                      <p className="text-sm text-green-600 font-semibold mb-1">优秀示例 ✅</p>
                      <p className="text-gray-800 text-sm">Generate stunning AI artwork with ImageCraft's free image generator. Transform text into high-quality images instantly. No signup required - start creating now!</p>
                    </div>
                    <div className="p-4 bg-blue-50 rounded-lg">
                      <p className="text-sm text-blue-600 font-semibold mb-2">优化要点分析：</p>
                      <ul className="text-sm text-gray-700 space-y-1">
                        <li>• 开头明确功能："Generate stunning AI artwork"</li>
                        <li>• 突出价值："free image generator"</li>
                        <li>• 强调特色："No signup required"</li>
                        <li>• 行动召唤："start creating now"</li>
                        <li>• 长度适中：符合150-160字符标准</li>
                      </ul>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          <div id="keyword-layout" className="mb-16">
            <div className="text-center mb-12">
              <h2 className="text-4xl font-bold text-gray-900 mb-4">关键词布局与密度控制</h2>
              <p className="text-xl text-gray-600">科学的关键词布局是提升页面相关性和排名的关键</p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Search className="h-6 w-6 mr-2 text-green-600" />
                    关键词分类
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="p-3 bg-blue-50 rounded-lg">
                      <p className="font-semibold text-blue-800">核心关键词</p>
                      <p className="text-sm text-gray-600">AI Image Generator</p>
                    </div>
                    <div className="p-3 bg-green-50 rounded-lg">
                      <p className="font-semibold text-green-800">长尾关键词</p>
                      <p className="text-sm text-gray-600">free AI image generator online</p>
                    </div>
                    <div className="p-3 bg-purple-50 rounded-lg">
                      <p className="font-semibold text-purple-800">品牌关键词</p>
                      <p className="text-sm text-gray-600">ImageCraft AI</p>
                    </div>
                    <div className="p-3 bg-orange-50 rounded-lg">
                      <p className="font-semibold text-orange-800">竞品关键词</p>
                      <p className="text-sm text-gray-600">Midjourney alternative</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <TrendingUp className="h-6 w-6 mr-2 text-purple-600" />
                    密度控制
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                      <span className="font-semibold">主关键词</span>
                      <Badge className="bg-blue-100 text-blue-800">1-2%</Badge>
                    </div>
                    <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                      <span className="font-semibold">相关关键词</span>
                      <Badge className="bg-green-100 text-green-800">适量使用</Badge>
                    </div>
                    <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                      <span className="font-semibold">同义词</span>
                      <Badge className="bg-purple-100 text-purple-800">自然分布</Badge>
                    </div>
                    <div className="p-3 bg-yellow-50 rounded-lg border border-yellow-200">
                      <p className="text-sm text-yellow-800">
                        <strong>注意：</strong>避免关键词堆砌，保持自然语言习惯
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Users className="h-6 w-6 mr-2 text-orange-600" />
                    漏斗关键词
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="p-3 bg-blue-50 rounded-lg">
                      <p className="font-semibold text-blue-800">TOFU (认知层)</p>
                      <p className="text-sm text-gray-600">what is AI art</p>
                    </div>
                    <div className="p-3 bg-green-50 rounded-lg">
                      <p className="font-semibold text-green-800">MOFU (考虑层)</p>
                      <p className="text-sm text-gray-600">best AI image generators</p>
                    </div>
                    <div className="p-3 bg-purple-50 rounded-lg">
                      <p className="font-semibold text-purple-800">BOFU (决策层)</p>
                      <p className="text-sm text-gray-600">free AI image generator</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          <div id="content-structure" className="mb-16">
            <div className="text-center mb-12">
              <h2 className="text-4xl font-bold text-gray-900 mb-4">内容架构与H标签优化</h2>
              <p className="text-xl text-gray-600">清晰的内容层级结构有助于搜索引擎理解页面主题</p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <BookOpen className="h-6 w-6 mr-2 text-blue-600" />
                    H标签层级结构
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="bg-gray-50 p-6 rounded-lg font-mono text-sm">
                    <div className="space-y-2">
                      <div className="text-blue-600 font-bold">H1: AI Image Generator - Create Stunning Artwork</div>
                      <div className="ml-4 text-green-600 font-semibold">H2: How Our AI Image Generator Works</div>
                      <div className="ml-8 text-purple-600">H3: Step 1: Enter Your Text Prompt</div>
                      <div className="ml-8 text-purple-600">H3: Step 2: Choose Art Style</div>
                      <div className="ml-8 text-purple-600">H3: Step 3: Generate & Download</div>
                      <div className="ml-4 text-green-600 font-semibold">H2: Key Features & Benefits</div>
                      <div className="ml-4 text-green-600 font-semibold">H2: Pricing & Plans</div>
                      <div className="ml-4 text-green-600 font-semibold">H2: Frequently Asked Questions</div>
                    </div>
                  </div>
                  <div className="mt-4 p-4 bg-blue-50 rounded-lg">
                    <p className="text-sm text-blue-800">
                      <strong>重要提示：</strong>每个页面只能有一个H1标签，H2-H6按层级递进使用
                    </p>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Zap className="h-6 w-6 mr-2 text-yellow-600" />
                    内容长度标准
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="p-4 bg-yellow-50 rounded-lg border-l-4 border-yellow-400">
                      <p className="font-semibold text-yellow-800 mb-2">最低要求</p>
                      <p className="text-gray-700">每个页面不少于800字</p>
                    </div>
                    <div className="p-4 bg-green-50 rounded-lg border-l-4 border-green-400">
                      <p className="font-semibold text-green-800 mb-2">理想标准</p>
                      <p className="text-gray-700">1200-2000字更佳</p>
                    </div>
                    <div className="p-4 bg-blue-50 rounded-lg border-l-4 border-blue-400">
                      <p className="font-semibold text-blue-800 mb-2">质量优先</p>
                      <p className="text-gray-700">确保内容有价值，避免为了字数而注水</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          <div id="technical-seo" className="mb-16">
            <div className="text-center mb-12">
              <h2 className="text-4xl font-bold text-gray-900 mb-4">技术SEO优化要点</h2>
              <p className="text-xl text-gray-600">技术优化是SEO成功的基础，确保搜索引擎能够正确抓取和理解页面</p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Code className="h-6 w-6 mr-2 text-green-600" />
                    核心元标签配置
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="bg-gray-900 text-green-400 p-4 rounded-lg font-mono text-sm overflow-x-auto">
                      <div className="text-gray-400">{'// Title标签'}</div>
                      <div>{'<title>AI Image Generator - Free HD Art Creator | ImageCraft</title>'}</div>
                      <br />
                      <div className="text-gray-400">{'// Description标签'}</div>
                      <div>{'<meta name="description" content="Generate stunning AI artwork with ImageCraft\'s free image generator. Transform text into high-quality images instantly.">'}</div>
                      <br />
                      <div className="text-gray-400">{'// Canonical标签'}</div>
                      <div>{'<link rel="canonical" href="https://imagecraft.ai/ai-image-generator">'}</div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Target className="h-6 w-6 mr-2 text-purple-600" />
                    图片优化策略
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="p-3 bg-blue-50 rounded-lg">
                      <p className="font-semibold text-blue-800 mb-2">Alt标签优化</p>
                      <div className="bg-gray-100 p-2 rounded text-sm font-mono">
                        alt="AI-generated fantasy landscape artwork created with ImageCraft generator"
                      </div>
                    </div>
                    <div className="space-y-2">
                      <div className="flex items-center">
                        <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                        <span className="text-sm">文件大小控制在100KB以内</span>
                      </div>
                      <div className="flex items-center">
                        <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                        <span className="text-sm">使用WebP格式提升加载速度</span>
                      </div>
                      <div className="flex items-center">
                        <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                        <span className="text-sm">文件名包含关键词</span>
                      </div>
                      <div className="flex items-center">
                        <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                        <span className="text-sm">图片尺寸适配不同设备</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          <div id="page-architecture" className="mb-16">
            <div className="text-center mb-12">
              <h2 className="text-4xl font-bold text-gray-900 mb-4">页面架构与内链策略</h2>
              <p className="text-xl text-gray-600">合理的网站架构和内链布局有助于搜索引擎理解网站结构和页面权重</p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <TrendingUp className="h-6 w-6 mr-2 text-blue-600" />
                    网站信息架构
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="bg-gray-50 p-6 rounded-lg font-mono text-sm">
                    <div className="space-y-1 text-gray-700">
                      <div className="text-blue-600 font-bold">首页（AI Image Generator）</div>
                      <div className="ml-2">├── 功能页面</div>
                      <div className="ml-4">│   ├── Text to Image Generator</div>
                      <div className="ml-4">│   ├── Photo to Artwork Converter</div>
                      <div className="ml-4">│   └── Style Transfer Tool</div>
                      <div className="ml-2">├── 工具页面</div>
                      <div className="ml-4">│   ├── Free Logo Maker</div>
                      <div className="ml-4">│   ├── Background Remover</div>
                      <div className="ml-4">│   └── Image Upscaler</div>
                      <div className="ml-2">├── 资源页面</div>
                      <div className="ml-4">│   ├── Art Style Gallery</div>
                      <div className="ml-4">│   ├── Prompt Templates</div>
                      <div className="ml-4">│   └── User Showcase</div>
                      <div className="ml-2">└── 对比页面</div>
                      <div className="ml-4">├── vs Midjourney</div>
                      <div className="ml-4">├── vs DALL-E</div>
                      <div className="ml-4">└── vs Stable Diffusion</div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Target className="h-6 w-6 mr-2 text-green-600" />
                    内链优化策略
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="p-4 bg-blue-50 rounded-lg">
                      <p className="font-semibold text-blue-800 mb-2">Hub页面</p>
                      <p className="text-sm text-gray-700">主要功能页面获得最多内链支持</p>
                    </div>
                    <div className="p-4 bg-green-50 rounded-lg">
                      <p className="font-semibold text-green-800 mb-2">相关推荐</p>
                      <p className="text-sm text-gray-700">功能页面间相互链接</p>
                    </div>
                    <div className="p-4 bg-purple-50 rounded-lg">
                      <p className="font-semibold text-purple-800 mb-2">面包屑导航</p>
                      <p className="text-sm text-gray-700">清晰的页面层级关系</p>
                    </div>
                    <div className="p-4 bg-orange-50 rounded-lg">
                      <p className="font-semibold text-orange-800 mb-2">锚文本优化</p>
                      <p className="text-sm text-gray-700">使用描述性文字而非"点击这里"</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          <div id="schema-data" className="mb-16">
            <div className="text-center mb-12">
              <h2 className="text-4xl font-bold text-gray-900 mb-4">Schema结构化数据优化</h2>
              <p className="text-xl text-gray-600">结构化数据帮助搜索引擎更好地理解页面内容，提升搜索结果展示效果</p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Code className="h-6 w-6 mr-2 text-purple-600" />
                    产品页面Schema
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="bg-gray-900 text-green-400 p-4 rounded-lg font-mono text-xs overflow-x-auto">
                    <div className="space-y-1">
                      <div>{'{'}</div>
                      <div className="ml-2">"@context": "https://schema.org",</div>
                      <div className="ml-2">"@type": "SoftwareApplication",</div>
                      <div className="ml-2">"name": "ImageCraft AI Image Generator",</div>
                      <div className="ml-2">"description": "AI-powered image generation tool",</div>
                      <div className="ml-2">"applicationCategory": "DesignApplication",</div>
                      <div className="ml-2">"operatingSystem": "Web Browser",</div>
                      <div className="ml-2">"offers": {'{'}</div>
                      <div className="ml-4">"@type": "Offer",</div>
                      <div className="ml-4">"price": "0",</div>
                      <div className="ml-4">"priceCurrency": "USD"</div>
                      <div className="ml-2">{'}'}</div>
                      <div>{'}'}</div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <BookOpen className="h-6 w-6 mr-2 text-orange-600" />
                    FAQ Schema
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="bg-gray-900 text-green-400 p-4 rounded-lg font-mono text-xs overflow-x-auto">
                    <div className="space-y-1">
                      <div>{'{'}</div>
                      <div className="ml-2">"@context": "https://schema.org",</div>
                      <div className="ml-2">"@type": "FAQPage",</div>
                      <div className="ml-2">"mainEntity": [{'{'}</div>
                      <div className="ml-4">"@type": "Question",</div>
                      <div className="ml-4">"name": "How does the AI image generator work?",</div>
                      <div className="ml-4">"acceptedAnswer": {'{'}</div>
                      <div className="ml-6">"@type": "Answer",</div>
                      <div className="ml-6">"text": "Our AI analyzes your text description..."</div>
                      <div className="ml-4">{'}'}</div>
                      <div className="ml-2">{'}]'}</div>
                      <div>{'}'}</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          <div id="landing-page-types" className="mb-16">
            <div className="text-center mb-12">
              <h2 className="text-4xl font-bold text-gray-900 mb-4">落地页类型与实施策略</h2>
              <p className="text-xl text-gray-600">不同类型的落地页面对应不同的用户搜索意图和转化目标</p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <TrendingUp className="h-6 w-6 mr-2 text-blue-600" />
                    核心落地页类型
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="p-4 bg-blue-50 rounded-lg">
                      <p className="font-semibold text-blue-800 mb-2">变体词落地页</p>
                      <ul className="text-sm text-gray-700 space-y-1">
                        <li>• AI Image Generator (主页)</li>
                        <li>• Text to Image Generator</li>
                        <li>• AI Art Creator</li>
                        <li>• Digital Art Generator</li>
                      </ul>
                    </div>
                    <div className="p-4 bg-green-50 rounded-lg">
                      <p className="font-semibold text-green-800 mb-2">竞品对比页</p>
                      <ul className="text-sm text-gray-700 space-y-1">
                        <li>• ImageCraft vs Midjourney</li>
                        <li>• ImageCraft vs DALL-E 2</li>
                        <li>• Best Midjourney Alternatives</li>
                      </ul>
                    </div>
                    <div className="p-4 bg-purple-50 rounded-lg">
                      <p className="font-semibold text-purple-800 mb-2">功能介绍页</p>
                      <ul className="text-sm text-gray-700 space-y-1">
                        <li>• Portrait Generator</li>
                        <li>• Landscape Art Creator</li>
                        <li>• Character Art Generator</li>
                      </ul>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <BookOpen className="h-6 w-6 mr-2 text-orange-600" />
                    页面内容模板
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="bg-gray-50 p-4 rounded-lg font-mono text-sm">
                    <div className="space-y-2 text-gray-700">
                      <div className="text-blue-600 font-bold"># [功能名称] - [核心价值主张]</div>
                      <div className="text-green-600 font-semibold">## 简介（100-150字）</div>
                      <div className="ml-2">[功能描述 + 核心优势]</div>
                      <div className="text-green-600 font-semibold">## 使用方法（300-400字）</div>
                      <div className="ml-2">### 步骤1：[具体操作]</div>
                      <div className="ml-2">### 步骤2：[具体操作]</div>
                      <div className="text-green-600 font-semibold">## 主要特色（200-300字）</div>
                      <div className="ml-2">- 特色1：[具体描述]</div>
                      <div className="ml-2">- 特色2：[具体描述]</div>
                      <div className="text-green-600 font-semibold">## 常见问题（200-300字）</div>
                      <div className="ml-2">Q1: [常见问题]</div>
                      <div className="ml-2">A1: [详细解答]</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          <div id="quick-checklist" className="mb-16">
            <div className="text-center mb-12">
              <h2 className="text-4xl font-bold text-gray-900 mb-4">落地页SEO快速检查清单</h2>
              <p className="text-xl text-gray-600">使用这个清单确保您的落地页符合SEO最佳实践</p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <CheckCircle className="h-6 w-6 mr-2 text-green-600" />
                    基础优化检查
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {[
                      'Title标签包含主关键词且长度适中',
                      'Meta描述吸引人且包含关键词',
                      'H1标签唯一且包含主关键词',
                      'H2-H6标签层级清晰合理',
                      '页面内容不少于800字',
                      '关键词密度控制在1-2%',
                      '内链和外链设置合理',
                      '图片Alt标签优化完整'
                    ].map((item, index) => (
                      <div key={index} className="flex items-center p-2 hover:bg-gray-50 rounded">
                        <input type="checkbox" className="mr-3 h-4 w-4 text-green-600" />
                        <span className="text-sm">{item}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Code className="h-6 w-6 mr-2 text-blue-600" />
                    技术优化检查
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {[
                      'Canonical标签设置正确',
                      'Open Graph标签完整',
                      'Schema结构化数据配置',
                      '页面加载速度优化',
                      '移动端适配完善',
                      'HTTPS安全协议启用',
                      'XML网站地图提交',
                      'robots.txt文件配置'
                    ].map((item, index) => (
                      <div key={index} className="flex items-center p-2 hover:bg-gray-50 rounded">
                        <input type="checkbox" className="mr-3 h-4 w-4 text-blue-600" />
                        <span className="text-sm">{item}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          <div className="text-center py-12 bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl">
            <h3 className="text-3xl font-bold text-gray-900 mb-4">开始优化您的落地页SEO</h3>
            <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
              运用这些策略和技巧，提升您的搜索排名，获得更多有价值的流量
            </p>
            <div className="flex justify-center">
              <Button
                onClick={() => scrollToSection('title-optimization')}
                className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white px-8 py-3"
              >
                <ArrowRight className="h-5 w-5 mr-2" />
                重新学习
              </Button>
            </div>
          </div>
        </div>
      </section>

      <FrontendFooter />
    </div>
  )
}
