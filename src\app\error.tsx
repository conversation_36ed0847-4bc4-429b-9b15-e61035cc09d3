'use client'

import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { RefreshCw, Home, Zap } from 'lucide-react'
import Link from 'next/link'
import FrontendHeader from '@/components/frontend-header'
import FrontendFooter from '@/components/frontend-footer'

export default function Error({
  error: _error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  return (
    <div className="min-h-screen bg-background">
      {/* 使用统一前台顶部组件 */}
      <FrontendHeader />

      {/* 错误页面区域 - 红橙渐变主题 */}
      <section className="relative py-16 px-4 bg-gradient-to-br from-red-50 via-orange-50 to-yellow-50 overflow-hidden">
        {/* 背景装饰 - 错误主题色 */}
        <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
        <div className="absolute top-10 right-10 w-72 h-72 bg-red-200 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob"></div>
        <div className="absolute top-20 left-10 w-72 h-72 bg-orange-200 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob animation-delay-2000"></div>
        <div className="absolute bottom-10 left-20 w-72 h-72 bg-yellow-200 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob animation-delay-4000"></div>

        <div className="container mx-auto relative z-10">
          <div className="text-center mb-12">
            {/* 错误标题 */}
            <div className="mb-8">
              <div className="text-8xl mb-6">⚠️</div>
              <h1 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-red-600 via-orange-600 to-yellow-600 bg-clip-text text-transparent mb-6">
                算法崩溃了！
              </h1>
              <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto leading-relaxed">
                我们的关键词分析引擎遇到了一个<span className="font-semibold text-destructive">意外错误</span>，
                就像遇到了搜索引擎的算法更新一样突然！
              </p>
            </div>

            {/* 技术团队提醒卡片 */}
            <div className="max-w-2xl mx-auto mb-8">
              <Card className="idea-card-enhanced border-destructive/20">
                <CardContent className="p-8">
                  <div className="flex items-center justify-center space-x-3 mb-6">
                    <Zap className="h-6 w-6 text-destructive" />
                    <h3 className="text-xl font-bold text-foreground">技术团队提醒</h3>
                  </div>
                  <div className="text-left space-y-3 text-muted-foreground">
                    <div className="flex items-start space-x-2">
                      <span className="text-destructive">•</span>
                      <span>错误已自动上报到我们的监控系统</span>
                    </div>
                    <div className="flex items-start space-x-2">
                      <span className="text-destructive">•</span>
                      <span>就像优化网站性能一样，我们正在紧急修复</span>
                    </div>
                    <div className="flex items-start space-x-2">
                      <span className="text-destructive">•</span>
                      <span>预计修复时间比百度收录新页面还要快</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* 操作按钮 */}
            <div className="space-y-6">
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button 
                  onClick={() => reset()}
                  size="lg" 
                  className="bg-destructive hover:bg-destructive/90 text-destructive-foreground px-8 py-3 shadow-lg hover:shadow-xl transition-all duration-200"
                >
                  <RefreshCw className="h-5 w-5 mr-2" />
                  重新分析
                </Button>
                <Button asChild variant="outline" size="lg" className="btn-outline-enhanced px-8 py-3">
                  <Link href="/">
                    <Home className="h-5 w-5 mr-2" />
                    返回首页
                  </Link>
                </Button>
              </div>
              
              <div className="text-sm text-muted-foreground">
                如果问题持续存在，请 
                <Link href="/console" className="text-destructive hover:underline font-medium mx-1">
                  联系我们
                </Link>
                或访问
                <Link href="/console/upload" className="text-destructive hover:underline font-medium mx-1">
                  上传页面
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 使用统一前台底部组件 */}
      <FrontendFooter />
    </div>
  )
}
