SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";

--
-- 表的结构 `categories`
--

CREATE TABLE `categories` (
  `id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '分类ID',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '分类中文名',
  `english_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '分类英文名',
  `keyword_count` int DEFAULT '0' COMMENT '关键词数量',
  `last_updated` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='分类管理表';

--
-- 转存表中的数据 `categories`
--

INSERT INTO `categories` (`id`, `name`, `english_name`, `keyword_count`, `last_updated`) VALUES
('all_categories', '所有类别', 'All Categories', 0, '2025-06-25 15:28:56'),
('arts_entertainment', '艺术与娱乐', 'Arts & Entertainment', 0, '2025-06-25 15:28:56'),
('autos_vehicles', '汽车与车辆', 'Autos & Vehicles', 0, '2025-06-25 15:28:56'),
('beauty_fitness', '美容与健身', 'Beauty & Fitness', 0, '2025-06-25 15:28:56'),
('books_literature', '图书与文学', 'Books & Literature', 0, '2025-06-25 15:28:56'),
('business_industrial', '工商业', 'Business & Industrial', 0, '2025-06-25 15:28:56'),
('computers_electronics', '计算机与电子产品', 'Computers & Electronics', 0, '2025-06-25 15:28:56'),
('finance', '金融', 'Finance', 0, '2025-06-25 15:28:56'),
('food_drink', '餐饮', 'Food & Drink', 0, '2025-06-25 15:28:56'),
('games', '游戏', 'Games', 0, '2025-06-25 15:28:56'),
('health', '健康', 'Health', 0, '2025-06-25 15:28:56'),
('hobbies_leisure', '爱好与休闲', 'Hobbies & Leisure', 0, '2025-06-25 15:28:56'),
('home_garden', '家居与园艺', 'Home & Garden', 0, '2025-06-25 15:28:56'),
('internet_telecom', '互联网与电信', 'Internet & Telecom', 0, '2025-06-25 15:28:56'),
('jobs_education', '求职与教育', 'Jobs & Education', 0, '2025-06-25 15:28:56'),
('law_government', '法律和政府', 'Law & Government', 0, '2025-06-25 15:28:56'),
('news', '新闻', 'News', 0, '2025-06-25 15:28:56'),
('online_communities', '在线社区', 'Online Communities', 0, '2025-06-25 15:28:56'),
('people_society', '人与社会', 'People & Society', 0, '2025-06-25 15:28:56'),
('pets_animals', '宠物与动物', 'Pets & Animals', 0, '2025-06-25 15:28:56'),
('real_estate', '房地产', 'Real Estate', 0, '2025-06-25 15:28:56'),
('reference', '参考信息', 'Reference', 0, '2025-06-25 15:28:56'),
('science', '科学', 'Science', 0, '2025-06-25 15:28:56'),
('shopping', '购物', 'Shopping', 0, '2025-06-25 15:28:56'),
('sports', '体育', 'Sports', 0, '2025-06-25 15:28:56'),
('travel', '旅游', 'Travel', 0, '2025-06-25 15:28:56');


--
-- 表的结构 `google_api_usage`
--

CREATE TABLE `google_api_usage` (
  `id` int NOT NULL COMMENT '记录ID',
  `api_key_index` tinyint NOT NULL COMMENT 'API密钥索引(1-20)',
  `usage_date` date NOT NULL COMMENT '使用日期',
  `usage_count` int DEFAULT '0' COMMENT '当日使用次数',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Google API使用统计表';

-- --------------------------------------------------------

--
-- 表的结构 `user_usage_stats`
--

CREATE TABLE `user_usage_stats` (
  `id` int NOT NULL COMMENT '记录ID',
  `user_email` varchar(255) NOT NULL COMMENT '用户邮箱',
  `usage_date` date NOT NULL COMMENT '使用日期',
  `usage_count` int DEFAULT '0' COMMENT '当日使用次数',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户使用统计表';

--
-- 表的结构 `import_logs`
--

CREATE TABLE `import_logs` (
  `id` int NOT NULL COMMENT '日志ID',
  `filename` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '导入文件名',
  `total_lines` int DEFAULT '0' COMMENT '总行数',
  `raw_keywords` int DEFAULT '0' COMMENT '原始关键词数（AI过滤前）',
  `filtered_keywords` int DEFAULT '0' COMMENT '最终成功导入的关键词数',
  `rejected_keywords` int DEFAULT '0' COMMENT '总拒绝数（AI过滤+重复+验证错误）',
  `pass_rate` decimal(5,2) DEFAULT '0.00' COMMENT '通过率（成功导入/原始总数）',
  `rejection_reasons` json DEFAULT NULL COMMENT '详细拒绝原因统计（包含AI分类）',
  `import_date` date NOT NULL COMMENT '导入日期',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='导入日志表';

--
-- 表的结构 `keywords`
--

CREATE TABLE `keywords` (
  `id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '关键词唯一ID（支持SEO友好的slug格式）',
  `keyword` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '关键词内容',
  `user_intent` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT 'AI分析的用户意图',
  `user_pain_point` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT 'AI分析的用户痛点',
  `competition_level` enum('easy','medium','hard') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '竞争难度等级',
  `competition_score` tinyint DEFAULT NULL COMMENT '竞争难度分数(1-10)',
  `competition_color` varchar(7) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '竞争难度颜色代码',
  `competition_description` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '竞争难度描述',
  `serp_analysis` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT 'SERP结构分析(用户评论)',
  `user_comment` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '用户备注评论',
  `category` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Google Trends分类',
  `category_locked` tinyint(1) DEFAULT '0' COMMENT '分类是否已锁定',
  `notes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '用户备注',
  `source` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'google_trends' COMMENT '数据来源',
  `import_date` date DEFAULT NULL COMMENT '导入日期',
  `analyzed_at` timestamp NULL DEFAULT NULL COMMENT 'AI分析时间',
  `category_assigned_at` timestamp NULL DEFAULT NULL COMMENT '分类分配时间',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='关键词主表';

--
-- 表的结构 `keyword_reports`
--

CREATE TABLE `keyword_reports` (
  `id` int NOT NULL COMMENT '报告ID',
  `keyword_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '关键词ID',
  `related_keywords` json DEFAULT NULL COMMENT '相关关键词列表及相似度评分',
  `similarity_analysis` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '相似度分析说明',
  `search_volume_estimate` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '搜索量估算',
  `competition_intensity` enum('low','medium','high') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '竞争强度',
  `market_opportunity` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '市场机会分析',
  `google_search_results` json DEFAULT NULL COMMENT 'Google搜索结果数据',
  `search_metrics` json DEFAULT NULL COMMENT '搜索量和竞争度指标',
  `top_competitors_analysis` json DEFAULT NULL COMMENT '前10名竞争对手详细分析',
  `title_patterns_analysis` json DEFAULT NULL COMMENT '标题模式分析',
  `content_gap_analysis` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '内容空缺分析',
  `ai_analysis` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT 'AI模型深度分析结果',
  `recommendation_score` decimal(3,2) DEFAULT NULL COMMENT '推荐评分(0-10)',
  `action_suggestions` json DEFAULT NULL COMMENT '行动建议列表',
  `top_competitors` json DEFAULT NULL COMMENT '主要竞争对手信息',
  `competitive_advantages` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '竞争优势分析',
  `content_strategy` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '内容策略建议',
  `target_audience` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '目标受众分析',
  `monetization_potential` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '变现潜力分析',
  `seo_difficulty` enum('easy','medium','hard') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'SEO难度',
  `trend_analysis` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '趋势分析',
  `seasonal_patterns` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '季节性模式',
  `analysis_model` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '使用的AI模型',
  `analysis_version` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '1.0' COMMENT '分析版本',
  `confidence_score` decimal(3,2) DEFAULT NULL COMMENT '分析置信度',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='关键词详细分析报告表';

--
-- 表的结构 `keyword_suggestions`
--

CREATE TABLE `keyword_suggestions` (
  `id` int NOT NULL COMMENT '建议ID',
  `keyword_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '关键词ID',
  `suggestion` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '建议关键词',
  `source` enum('google_suggestions_1','google_suggestions_2') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '建议来源API',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='关键词建议拓展表';

-- --------------------------------------------------------

--
-- 表的索引 `categories`
--
ALTER TABLE `categories`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_name` (`name`);

--
-- 表的索引 `google_api_usage`
--
ALTER TABLE `google_api_usage`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_key_date` (`api_key_index`,`usage_date`),
  ADD KEY `idx_usage_date` (`usage_date`),
  ADD KEY `idx_api_key_index` (`api_key_index`);

--
-- 表的索引 `user_usage_stats`
--
ALTER TABLE `user_usage_stats`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_user_date` (`user_email`,`usage_date`),
  ADD KEY `idx_user_email` (`user_email`),
  ADD KEY `idx_usage_date` (`usage_date`);

--
-- 表的索引 `import_logs`
--
ALTER TABLE `import_logs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_import_date` (`import_date`),
  ADD KEY `idx_filename` (`filename`);

--
-- 表的索引 `keywords`
--
ALTER TABLE `keywords`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `keyword` (`keyword`),
  ADD KEY `idx_keyword` (`keyword`),
  ADD KEY `idx_category` (`category`),
  ADD KEY `idx_import_date` (`import_date`),
  ADD KEY `idx_competition_level` (`competition_level`),
  ADD KEY `idx_source` (`source`);

--
-- 表的索引 `keyword_suggestions`
--
ALTER TABLE `keyword_suggestions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_keyword_id` (`keyword_id`),
  ADD KEY `idx_source` (`source`);

--
-- 使用表AUTO_INCREMENT `google_api_usage`
--
ALTER TABLE `google_api_usage`
  MODIFY `id` int NOT NULL AUTO_INCREMENT COMMENT '记录ID', AUTO_INCREMENT=1;

--
-- 使用表AUTO_INCREMENT `user_usage_stats`
--
ALTER TABLE `user_usage_stats`
  MODIFY `id` int NOT NULL AUTO_INCREMENT COMMENT '记录ID', AUTO_INCREMENT=1;

--
-- 使用表AUTO_INCREMENT `import_logs`
--
ALTER TABLE `import_logs`
  MODIFY `id` int NOT NULL AUTO_INCREMENT COMMENT '日志ID';

--
-- 使用表AUTO_INCREMENT `keyword_suggestions`
--
ALTER TABLE `keyword_suggestions`
  MODIFY `id` int NOT NULL AUTO_INCREMENT COMMENT '建议ID', AUTO_INCREMENT=1;

--
-- 限制表 `keyword_suggestions`
--
ALTER TABLE `keyword_suggestions`
  ADD CONSTRAINT `keyword_suggestions_ibfk_1` FOREIGN KEY (`keyword_id`) REFERENCES `keywords` (`id`) ON DELETE CASCADE;

