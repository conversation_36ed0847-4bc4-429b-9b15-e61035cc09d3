'use client'

import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card'
import { Badge } from '../../components/ui/badge'
import { Button } from '../../components/ui/button'
import { Textarea } from '../../components/ui/textarea'
import { Input } from '../../components/ui/input'
import { Alert, AlertDescription } from '../../components/ui/alert'
import { Switch } from '../../components/ui/switch'
import { Label } from '../../components/ui/label'
import {
  Filter,
  Copy,
  Download,
  Trash2,
  CheckCircle,
  XCircle,
  Info,
  Loader2,
  FileText,
  Target,
  Shield,

  Link as LinkIcon,
  AlertTriangle
} from 'lucide-react'
import FrontendHeader from '../../components/frontend-header'
import FrontendFooter from '../../components/frontend-footer'
import { Toaster, toast } from 'sonner'

interface FilterResult {
  accepted: string[]
  rejected: string[]
  duplicates: string[]
  stats: {
    total: number
    accepted: number
    rejected: number
    duplicates: number
    highQuality: number
    mediumQuality: number
    lowQuality: number
  }
  qualityScores: { [domain: string]: 'high' | 'medium' | 'low' }
  rejectionReasons: { [domain: string]: string }
}

export default function BacklinkFilterPage() {
  const [inputText, setInputText] = useState('')
  const [suffixToRemove, setSuffixToRemove] = useState('')
  const [bulkSuffixes, setBulkSuffixes] = useState('')
  const [isProcessing, setIsProcessing] = useState(false)
  const [filterResult, setFilterResult] = useState<FilterResult | null>(null)
  const [enableQualityScoring, setEnableQualityScoring] = useState(true)
  const [enableSpamDetection, setEnableSpamDetection] = useState(true)
  const [enableBulkSuffixFilter, setEnableBulkSuffixFilter] = useState(false)


  // Domain quality scoring logic
  const scoreDomainQuality = (domain: string): 'high' | 'medium' | 'low' => {
    const cleanDomain = domain.toLowerCase().trim()
    
    // High quality indicators
    const highQualityTlds = ['.com', '.org', '.net', '.edu', '.gov', '.mil']
    const hasHighQualityTld = highQualityTlds.some(tld => cleanDomain.endsWith(tld))
    
    // Low quality indicators
    const lowQualityTlds = ['.tk', '.ml', '.ga', '.cf', '.xyz', '.top', '.click', '.download']
    const hasLowQualityTld = lowQualityTlds.some(tld => cleanDomain.endsWith(tld))
    
    // Spam patterns
    const hasExcessiveNumbers = (cleanDomain.match(/\d/g) || []).length > 3
    const hasExcessiveHyphens = (cleanDomain.match(/-/g) || []).length > 2
    const isVeryShort = cleanDomain.length < 6
    const isVeryLong = cleanDomain.length > 50
    const hasRandomPattern = /[a-z]{1,2}\d{3,}|[a-z]\d[a-z]\d/.test(cleanDomain)
    
    if (hasLowQualityTld || hasExcessiveNumbers || hasExcessiveHyphens || isVeryShort || isVeryLong || hasRandomPattern) {
      return 'low'
    }
    
    if (hasHighQualityTld && cleanDomain.length >= 8 && cleanDomain.length <= 25) {
      return 'high'
    }
    
    return 'medium'
  }

  // Enhanced spam detection logic
  const isSpamDomain = (domain: string): { isSpam: boolean; reason: string } => {
    const cleanDomain = domain.toLowerCase().trim()

    // Known spam TLDs (expanded list)
    const spamTlds = [
      '.tk', '.ml', '.ga', '.cf', '.pw', '.top', '.click', '.download', '.stream', '.science',
      '.party', '.racing', '.review', '.trade', '.webcam', '.win', '.loan', '.cricket',
      '.date', '.faith', '.men', '.accountant', '.bid', '.country', '.kim', '.gq'
    ]
    for (const tld of spamTlds) {
      if (cleanDomain.endsWith(tld)) {
        return { isSpam: true, reason: `垃圾域名后缀 (${tld})` }
      }
    }

    // Suspicious number patterns
    if (/\d{4,}/.test(cleanDomain)) {
      return { isSpam: true, reason: '包含过多数字' }
    }

    // Excessive hyphens
    if ((cleanDomain.match(/-/g) || []).length > 3) {
      return { isSpam: true, reason: '包含过多连字符' }
    }

    // Random generation patterns
    if (/^[a-z]{1,2}\d+/.test(cleanDomain) || /\d[a-z]\d/.test(cleanDomain)) {
      return { isSpam: true, reason: '随机生成模式' }
    }

    // Very short domains (likely auto-generated)
    if (cleanDomain.replace(/\.[a-z]+$/, '').length < 4) {
      return { isSpam: true, reason: '域名过短' }
    }

    // Excessive subdomain levels
    const parts = cleanDomain.split('.')
    if (parts.length > 4) {
      return { isSpam: true, reason: '子域名层级过多' }
    }

    // CDN and proxy patterns (expanded)
    const cdnPatterns = [
      'cdn', 'cache', 'static', 'assets', 'media', 'img', 'images', 'js', 'css',
      'api', 'www1', 'www2', 'www3', 'staging', 'dev', 'test', 'demo',
      'proxy', 'mirror', 'backup', 'temp', 'tmp'
    ]
    if (cdnPatterns.some(pattern => cleanDomain.includes(pattern))) {
      return { isSpam: true, reason: 'CDN/技术域名' }
    }

    // Parking and placeholder domains
    const parkingPatterns = ['parked', 'parking', 'placeholder', 'coming-soon', 'under-construction']
    if (parkingPatterns.some(pattern => cleanDomain.includes(pattern))) {
      return { isSpam: true, reason: '停放/占位域名' }
    }

    // Suspicious character patterns
    if (/[0-9]{2,}[a-z]{1,2}[0-9]{2,}/.test(cleanDomain)) {
      return { isSpam: true, reason: '可疑字符模式' }
    }

    // Repeated character patterns
    if (/(.)\1{3,}/.test(cleanDomain)) {
      return { isSpam: true, reason: '重复字符模式' }
    }

    // Common spam keywords
    const spamKeywords = ['free', 'cheap', 'buy', 'sale', 'promo', 'deal', 'offer', 'bonus']
    if (spamKeywords.some(keyword => cleanDomain.includes(keyword))) {
      return { isSpam: true, reason: '包含垃圾关键词' }
    }

    return { isSpam: false, reason: '' }
  }

  // Main filtering logic
  const filterDomains = (text: string, suffixFilter: string, bulkSuffixFilter: string): FilterResult => {
    if (!text.trim()) {
      return {
        accepted: [],
        rejected: [],
        duplicates: [],
        stats: { total: 0, accepted: 0, rejected: 0, duplicates: 0, highQuality: 0, mediumQuality: 0, lowQuality: 0 },
        qualityScores: {},
        rejectionReasons: {}
      }
    }

    // Parse input text
    const lines = text.split('\n')
      .map(line => line.trim())
      .filter(line => line.length > 0)
      .map(line => {
        // Extract domain from URL if needed
        try {
          if (line.startsWith('http://') || line.startsWith('https://')) {
            return new URL(line).hostname
          }
          return line
        } catch {
          return line
        }
      })

    const allDomains = new Set<string>()
    const duplicates: string[] = []
    const uniqueDomains: string[] = []

    // Remove duplicates
    lines.forEach(domain => {
      if (allDomains.has(domain.toLowerCase())) {
        duplicates.push(domain)
      } else {
        allDomains.add(domain.toLowerCase())
        uniqueDomains.push(domain)
      }
    })

    const accepted: string[] = []
    const rejected: string[] = []
    const qualityScores: { [domain: string]: 'high' | 'medium' | 'low' } = {}
    const rejectionReasons: { [domain: string]: string } = {}

    uniqueDomains.forEach(domain => {
      const cleanDomain = domain.trim().toLowerCase()
      
      // Basic validation
      if (!cleanDomain || cleanDomain.length < 3) {
        rejected.push(domain)
        rejectionReasons[domain] = '域名过短'
        return
      }

      // Check for valid domain format
      if (!/^[a-z0-9.-]+\.[a-z]{2,}$/i.test(cleanDomain)) {
        rejected.push(domain)
        rejectionReasons[domain] = '无效域名格式'
        return
      }

      // Single suffix filtering
      if (suffixFilter && cleanDomain.endsWith(suffixFilter.toLowerCase())) {
        rejected.push(domain)
        rejectionReasons[domain] = `匹配过滤后缀 (${suffixFilter})`
        return
      }

      // Bulk suffix filtering
      if (enableBulkSuffixFilter && bulkSuffixFilter) {
        const suffixes = bulkSuffixFilter.split(',').map(s => s.trim().toLowerCase()).filter(s => s)
        for (const suffix of suffixes) {
          if (cleanDomain.endsWith(suffix)) {
            rejected.push(domain)
            rejectionReasons[domain] = `匹配批量过滤后缀 (${suffix})`
            return
          }
        }
      }

      // Spam detection
      if (enableSpamDetection) {
        const spamCheck = isSpamDomain(cleanDomain)
        if (spamCheck.isSpam) {
          rejected.push(domain)
          rejectionReasons[domain] = spamCheck.reason
          return
        }
      }

      // Quality scoring
      if (enableQualityScoring) {
        qualityScores[domain] = scoreDomainQuality(cleanDomain)
      }

      accepted.push(domain)
    })

    // Calculate quality stats
    const qualityStats = accepted.reduce(
      (stats, domain) => {
        const quality = qualityScores[domain] || 'medium'
        stats[`${quality}Quality`]++
        return stats
      },
      { highQuality: 0, mediumQuality: 0, lowQuality: 0 }
    )

    return {
      accepted,
      rejected,
      duplicates,
      stats: {
        total: lines.length,
        accepted: accepted.length,
        rejected: rejected.length,
        duplicates: duplicates.length,
        ...qualityStats
      },
      qualityScores,
      rejectionReasons
    }
  }

  // Handle filtering
  const handleFilter = async () => {
    if (!inputText.trim()) {
      toast.error('请输入要过滤的域名列表')
      return
    }

    setIsProcessing(true)
    
    try {
      // Simulate processing time
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      const result = filterDomains(inputText, suffixToRemove, bulkSuffixes)
      setFilterResult(result)
      
      toast.success(`过滤完成！获得 ${result.stats.accepted} 个有效域名`)
    } catch {
      toast.error('过滤过程出错，请重试')
    } finally {
      setIsProcessing(false)
    }
  }

  // Copy to clipboard
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text).then(() => {
      toast.success(`复制成功！${filterResult?.accepted.length || 0}个域名已复制到剪贴板`)
    }).catch(() => {
      toast.error('复制失败，请重试')
    })
  }

  // Export CSV with enhanced data
  const exportCSV = () => {
    if (!filterResult || filterResult.accepted.length === 0) {
      toast.error('没有可导出的数据')
      return
    }

    const csvContent = [
      'Domain,Quality Score,Status,Export Date',
      ...filterResult.accepted.map(domain =>
        `${domain},${filterResult.qualityScores[domain] || 'medium'},Accepted,${new Date().toISOString().split('T')[0]}`
      )
    ].join('\n')

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', `filtered_domains_${new Date().getTime()}.csv`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    toast.success(`CSV导出成功！${filterResult.accepted.length}个域名已保存`)
  }

  // Export detailed CSV with rejected domains
  const exportDetailedCSV = () => {
    if (!filterResult) {
      toast.error('没有可导出的数据')
      return
    }

    const csvContent = [
      'Domain,Status,Quality Score,Rejection Reason,Export Date',
      ...filterResult.accepted.map(domain =>
        `${domain},Accepted,${filterResult.qualityScores[domain] || 'medium'},,${new Date().toISOString().split('T')[0]}`
      ),
      ...filterResult.rejected.map(domain =>
        `${domain},Rejected,,${filterResult.rejectionReasons[domain] || 'Unknown'},${new Date().toISOString().split('T')[0]}`
      ),
      ...filterResult.duplicates.map(domain =>
        `${domain},Duplicate,,,${new Date().toISOString().split('T')[0]}`
      )
    ].join('\n')

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', `detailed_domains_${new Date().getTime()}.csv`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    toast.success(`详细CSV导出成功！${filterResult.stats.total}个域名已保存`)
  }

  // Export TXT
  const exportTXT = () => {
    if (!filterResult || filterResult.accepted.length === 0) {
      toast.error('没有可导出的数据')
      return
    }

    const txtContent = filterResult.accepted.join('\n')
    const blob = new Blob([txtContent], { type: 'text/plain;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', `filtered_domains_${new Date().getTime()}.txt`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    toast.success(`TXT导出成功！${filterResult.accepted.length}个域名已保存`)
  }

  // Export JSON with complete analysis
  const exportJSON = () => {
    if (!filterResult) {
      toast.error('没有可导出的数据')
      return
    }

    const jsonData = {
      exportDate: new Date().toISOString(),
      filterSettings: {
        qualityScoring: enableQualityScoring,
        spamDetection: enableSpamDetection,
        bulkSuffixFilter: enableBulkSuffixFilter,
        singleSuffix: suffixToRemove,
        bulkSuffixes: bulkSuffixes
      },
      statistics: filterResult.stats,
      domains: {
        accepted: filterResult.accepted.map(domain => ({
          domain,
          qualityScore: filterResult.qualityScores[domain] || 'medium'
        })),
        rejected: filterResult.rejected.map(domain => ({
          domain,
          reason: filterResult.rejectionReasons[domain] || 'Unknown'
        })),
        duplicates: filterResult.duplicates
      }
    }

    const jsonContent = JSON.stringify(jsonData, null, 2)
    const blob = new Blob([jsonContent], { type: 'application/json;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', `domain_analysis_${new Date().getTime()}.json`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    toast.success(`JSON导出成功！完整分析数据已保存`)
  }

  // Clear all data
  const clearAll = () => {
    setInputText('')
    setSuffixToRemove('')
    setBulkSuffixes('')
    setFilterResult(null)
    toast.success('已清空所有数据')
  }

  return (
    <div className="min-h-screen bg-background">
      <FrontendHeader />

      <section className="py-4 px-4">
        <div className="container mx-auto">
          <div className="max-w-6xl mx-auto">

            {/* Input Area */}
            <div className="mb-6">
              <Card className="idea-card-enhanced">
                <CardHeader className="pb-3">
                  <CardTitle className="flex items-center space-x-2 text-lg">
                    <LinkIcon className="h-5 w-5 text-primary" />
                    <span>反向链接域名过滤</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="relative">
                    <Textarea
                      placeholder="请粘贴域名列表，每行一个域名或URL（支持从SEMrush导出的反向链接数据）"
                      value={inputText}
                      onChange={(e) => setInputText(e.target.value)}
                      className="!min-h-[120px] !max-h-80 !h-auto text-base resize-none overflow-y-auto border-2 focus:border-primary/50 [field-sizing:initial]"
                      disabled={isProcessing}
                      style={{
                        scrollbarWidth: 'thin',
                        scrollbarColor: 'rgb(203 213 225) transparent',
                        height: 'auto',
                        minHeight: '120px',
                        maxHeight: '320px'
                      }}
                    />
                  </div>

                  {/* Suffix Filter Inputs */}
                  <div className="space-y-3">
                    <div className="flex flex-col sm:flex-row gap-3">
                      <div className="flex-1">
                        <Label htmlFor="suffix-filter" className="text-sm font-medium mb-2 block">
                          单个后缀过滤 (可选)
                        </Label>
                        <Input
                          id="suffix-filter"
                          placeholder="例如: .xyz (将移除所有.xyz域名)"
                          value={suffixToRemove}
                          onChange={(e) => setSuffixToRemove(e.target.value)}
                          disabled={isProcessing || enableBulkSuffixFilter}
                          className="border-2 focus:border-primary/50"
                        />
                      </div>
                    </div>

                    {/* Bulk Suffix Filter */}
                    <Card className="data-card-enhanced border-secondary/20 bg-gradient-to-r from-secondary/5 to-blue-500/5">
                      <CardContent className="p-4">
                        <div className="space-y-3">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-3">
                              <div className="flex items-center justify-center w-10 h-10 rounded-full bg-secondary/10 border border-secondary/20">
                                <Filter className="h-5 w-5 text-secondary" />
                              </div>
                              <div>
                                <div className="flex items-center space-x-3">
                                  <Label htmlFor="bulk-suffix-filter" className="text-sm font-semibold text-foreground cursor-pointer">
                                    批量后缀过滤
                                  </Label>
                                  <Switch
                                    id="bulk-suffix-filter"
                                    checked={enableBulkSuffixFilter}
                                    onCheckedChange={setEnableBulkSuffixFilter}
                                    disabled={isProcessing}
                                  />
                                </div>
                                <p className="text-xs text-muted-foreground mt-1">
                                  同时过滤多个域名后缀，用逗号分隔
                                </p>
                              </div>
                            </div>
                          </div>

                          {enableBulkSuffixFilter && (
                            <div>
                              <Input
                                placeholder="例如: .xyz, .tk, .ml, .ga (用逗号分隔多个后缀)"
                                value={bulkSuffixes}
                                onChange={(e) => setBulkSuffixes(e.target.value)}
                                disabled={isProcessing}
                                className="border-2 focus:border-secondary/50 bg-white/80"
                              />
                            </div>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  </div>

                  {/* Filter Options */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Card className="data-card-enhanced border-primary/20 bg-gradient-to-r from-primary/5 to-purple-500/5">
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <div className="flex items-center justify-center w-10 h-10 rounded-full bg-primary/10 border border-primary/20">
                              <Shield className="h-5 w-5 text-primary" />
                            </div>
                            <div>
                              <div className="flex items-center space-x-3">
                                <Label htmlFor="quality-scoring" className="text-sm font-semibold text-foreground cursor-pointer">
                                  域名质量评分
                                </Label>
                                <Switch
                                  id="quality-scoring"
                                  checked={enableQualityScoring}
                                  onCheckedChange={setEnableQualityScoring}
                                  disabled={isProcessing}
                                />
                              </div>
                              <p className="text-xs text-muted-foreground mt-1">
                                根据域名后缀、长度、模式等评估质量
                              </p>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    <Card className="data-card-enhanced border-warning/20 bg-gradient-to-r from-warning/5 to-orange-500/5">
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <div className="flex items-center justify-center w-10 h-10 rounded-full bg-warning/10 border border-warning/20">
                              <AlertTriangle className="h-5 w-5 text-warning" />
                            </div>
                            <div>
                              <div className="flex items-center space-x-3">
                                <Label htmlFor="spam-detection" className="text-sm font-semibold text-foreground cursor-pointer">
                                  垃圾域名检测
                                </Label>
                                <Switch
                                  id="spam-detection"
                                  checked={enableSpamDetection}
                                  onCheckedChange={setEnableSpamDetection}
                                  disabled={isProcessing}
                                />
                              </div>
                              <p className="text-xs text-muted-foreground mt-1">
                                自动识别和过滤垃圾、CDN、代理域名
                              </p>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </div>

                  <div className="flex items-center justify-between flex-wrap gap-3">
                    <div className="text-sm text-muted-foreground">
                      已输入 {inputText.split('\n').filter(line => line.trim()).length} 个域名
                      {suffixToRemove && !enableBulkSuffixFilter && (
                        <span className="ml-2 text-destructive">
                          (将过滤 {suffixToRemove} 后缀)
                        </span>
                      )}
                      {enableBulkSuffixFilter && bulkSuffixes && (
                        <span className="ml-2 text-destructive">
                          (批量过滤: {bulkSuffixes.split(',').length} 个后缀)
                        </span>
                      )}
                    </div>

                    <div className="flex space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={clearAll}
                        disabled={isProcessing || !inputText.trim()}
                        className="bg-white/80 backdrop-blur-sm border-gray-200 text-gray-700 hover:bg-red-50 hover:text-red-600 hover:border-red-300"
                      >
                        <Trash2 className="h-4 w-4 mr-1" />
                        清空
                      </Button>

                      <Button
                        size="sm"
                        onClick={handleFilter}
                        disabled={isProcessing || !inputText.trim()}
                        className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white border-0"
                      >
                        {isProcessing ? (
                          <>
                            <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                            处理中...
                          </>
                        ) : (
                          <>
                            <Filter className="h-4 w-4 mr-1" />
                            开始过滤
                          </>
                        )}
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Filter Rules Description */}
              <Alert className="border-primary/30 bg-card border-2 mt-4">
                <Info className="h-4 w-4 text-primary" />
                <AlertDescription className="text-foreground text-sm">
                  <strong>过滤规则：</strong>自动排除无效域名格式、垃圾域名后缀、CDN域名、随机生成域名、停放域名等，专为清理SEMrush反向链接数据设计。
                  {enableQualityScoring && (
                    <span className="block mt-2 text-primary">
                      <strong>质量评分：</strong>高质量(.com/.org/.net)、中等质量(其他常见后缀)、低质量(.tk/.ml/.xyz等)。
                    </span>
                  )}
                  {enableBulkSuffixFilter && (
                    <span className="block mt-2 text-secondary">
                      <strong>批量过滤：</strong>同时过滤多个指定的域名后缀，提高处理效率。
                    </span>
                  )}
                </AlertDescription>
              </Alert>
            </div>

            {/* Results Display Area */}
            {filterResult && (
              <div className="mt-6 space-y-6">
                {/* Statistics Cards */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <Card className="data-card-enhanced stats-card-info">
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                      <CardTitle className="text-sm font-medium text-info/80">总数量</CardTitle>
                      <FileText className="h-5 w-5 text-info" />
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">{filterResult.stats.total}</div>
                      <p className="text-xs text-muted-foreground">输入域名</p>
                    </CardContent>
                  </Card>

                  <Card className="data-card-enhanced stats-card-success">
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                      <CardTitle className="text-sm font-medium text-success/80">通过</CardTitle>
                      <CheckCircle className="h-5 w-5 text-success" />
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">{filterResult.stats.accepted}</div>
                      <p className="text-xs text-muted-foreground">有效域名</p>
                    </CardContent>
                  </Card>

                  <Card className="data-card-enhanced stats-card-warning">
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                      <CardTitle className="text-sm font-medium text-warning/80">拒绝</CardTitle>
                      <XCircle className="h-5 w-5 text-warning" />
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">{filterResult.stats.rejected}</div>
                      <p className="text-xs text-muted-foreground">无效域名</p>
                    </CardContent>
                  </Card>

                  <Card className="data-card-enhanced stats-card-info">
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                      <CardTitle className="text-sm font-medium text-info/80">重复</CardTitle>
                      <Target className="h-5 w-5 text-info" />
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">{filterResult.stats.duplicates}</div>
                      <p className="text-xs text-muted-foreground">重复域名</p>
                    </CardContent>
                  </Card>
                </div>

                {/* Quality Distribution (if enabled) */}
                {enableQualityScoring && (
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <Card className="data-card-enhanced stats-card-success">
                      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium text-success/80">高质量</CardTitle>
                        <Shield className="h-5 w-5 text-success" />
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold">{filterResult.stats.highQuality}</div>
                        <p className="text-xs text-muted-foreground">.com/.org/.net等</p>
                      </CardContent>
                    </Card>

                    <Card className="data-card-enhanced stats-card-secondary">
                      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium text-secondary/80">中等质量</CardTitle>
                        <Shield className="h-5 w-5 text-secondary" />
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold">{filterResult.stats.mediumQuality}</div>
                        <p className="text-xs text-muted-foreground">其他常见后缀</p>
                      </CardContent>
                    </Card>

                    <Card className="data-card-enhanced stats-card-warning">
                      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium text-warning/80">低质量</CardTitle>
                        <Shield className="h-5 w-5 text-warning" />
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold">{filterResult.stats.lowQuality}</div>
                        <p className="text-xs text-muted-foreground">.tk/.ml/.xyz等</p>
                      </CardContent>
                    </Card>
                  </div>
                )}

                {/* Main Content Area */}
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  {/* Accepted Domains */}
                  <Card className="lg:col-span-2">
                    <CardHeader>
                      <CardTitle className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
                        <div className="flex items-center space-x-2">
                          <CheckCircle className="h-5 w-5 text-success" />
                          <span>通过过滤 ({filterResult.stats.accepted})</span>
                        </div>
                        <div className="flex flex-wrap gap-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => copyToClipboard(filterResult.accepted.join('\n'))}
                            disabled={filterResult.accepted.length === 0}
                            className="bg-white/80 backdrop-blur-sm border-gray-200 text-gray-700 hover:bg-blue-50 hover:text-blue-600 hover:border-blue-300"
                          >
                            <Copy className="h-4 w-4 mr-1" />
                            <span className="hidden sm:inline">复制</span>
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={exportCSV}
                            disabled={filterResult.accepted.length === 0}
                            className="bg-white/80 backdrop-blur-sm border-gray-200 text-gray-700 hover:bg-green-50 hover:text-green-600 hover:border-green-300"
                          >
                            <Download className="h-4 w-4 mr-1" />
                            <span className="hidden sm:inline">CSV</span>
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={exportTXT}
                            disabled={filterResult.accepted.length === 0}
                            className="bg-white/80 backdrop-blur-sm border-gray-200 text-gray-700 hover:bg-purple-50 hover:text-purple-600 hover:border-purple-300"
                          >
                            <Download className="h-4 w-4 mr-1" />
                            <span className="hidden sm:inline">TXT</span>
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={exportDetailedCSV}
                            disabled={filterResult.stats.total === 0}
                            className="bg-white/80 backdrop-blur-sm border-gray-200 text-gray-700 hover:bg-orange-50 hover:text-orange-600 hover:border-orange-300"
                          >
                            <Download className="h-4 w-4 mr-1" />
                            <span className="hidden sm:inline">详细CSV</span>
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={exportJSON}
                            disabled={filterResult.stats.total === 0}
                            className="bg-white/80 backdrop-blur-sm border-gray-200 text-gray-700 hover:bg-indigo-50 hover:text-indigo-600 hover:border-indigo-300"
                          >
                            <Download className="h-4 w-4 mr-1" />
                            <span className="hidden sm:inline">JSON</span>
                          </Button>
                        </div>
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      {filterResult.accepted.length > 0 ? (
                        <div className="max-h-96 overflow-y-auto space-y-2">
                          {filterResult.accepted.map((domain, index) => (
                            <div key={index} className="p-3 bg-success/10 border border-success/20 rounded-lg">
                              <div className="flex items-center justify-between">
                                <span className="text-sm font-medium text-success-foreground">{domain}</span>
                                {enableQualityScoring && filterResult.qualityScores[domain] && (
                                  <Badge
                                    variant={
                                      filterResult.qualityScores[domain] === 'high' ? 'default' :
                                      filterResult.qualityScores[domain] === 'medium' ? 'secondary' : 'outline'
                                    }
                                    className={
                                      filterResult.qualityScores[domain] === 'high' ? 'bg-success text-white' :
                                      filterResult.qualityScores[domain] === 'medium' ? 'bg-secondary text-white' : 'border-warning text-warning'
                                    }
                                  >
                                    {filterResult.qualityScores[domain] === 'high' ? '高' :
                                     filterResult.qualityScores[domain] === 'medium' ? '中' : '低'}
                                  </Badge>
                                )}
                              </div>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <div className="text-center py-8 text-muted-foreground">
                          没有通过过滤的域名
                        </div>
                      )}
                    </CardContent>
                  </Card>

                  {/* Right Side Area */}
                  <div className="space-y-6">
                    {/* Rejected Domains */}
                    <Card>
                      <CardHeader>
                        <CardTitle className="flex items-center space-x-2">
                          <XCircle className="h-5 w-5 text-destructive" />
                          <span>被过滤 ({filterResult.stats.rejected + filterResult.stats.duplicates})</span>
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        {(filterResult.rejected.length > 0 || filterResult.duplicates.length > 0) ? (
                          <div className="max-h-96 overflow-y-auto space-y-2">
                            {/* Rejected domains */}
                            {filterResult.rejected.map((domain, index) => (
                              <div key={`rejected-${index}`} className="p-3 bg-destructive/10 border border-destructive/20 rounded-lg">
                                <div className="flex flex-col space-y-1">
                                  <div className="flex items-center justify-between">
                                    <span className="text-sm text-destructive-foreground truncate">{domain}</span>
                                    <Badge variant="destructive" className="text-xs">
                                      无效
                                    </Badge>
                                  </div>
                                  {filterResult.rejectionReasons[domain] && (
                                    <p className="text-xs text-muted-foreground">
                                      {filterResult.rejectionReasons[domain]}
                                    </p>
                                  )}
                                </div>
                              </div>
                            ))}

                            {/* Duplicate domains */}
                            {filterResult.duplicates.map((domain, index) => (
                              <div key={`duplicate-${index}`} className="p-3 bg-warning/10 border border-warning/20 rounded-lg">
                                <div className="flex items-center justify-between">
                                  <span className="text-sm text-warning-foreground truncate">{domain}</span>
                                  <Badge variant="outline" className="text-xs border-warning text-warning">
                                    重复
                                  </Badge>
                                </div>
                              </div>
                            ))}
                          </div>
                        ) : (
                          <div className="text-center py-8 text-muted-foreground">
                            没有被过滤的域名
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  </div>
                </div>
              </div>
            )}

            {/* Usage Instructions */}
            {!filterResult && (
              <Card className="idea-card-enhanced">
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Info className="h-6 w-6 text-primary" />
                    <span>使用说明</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h4 className="font-semibold text-foreground mb-3">过滤标准</h4>
                      <ul className="space-y-2 text-sm text-muted-foreground">
                        <li className="flex items-start space-x-2">
                          <span className="text-destructive">✗</span>
                          <span>无效域名格式</span>
                        </li>
                        <li className="flex items-start space-x-2">
                          <span className="text-destructive">✗</span>
                          <span>垃圾域名后缀 (.tk, .ml, .ga等)</span>
                        </li>
                        <li className="flex items-start space-x-2">
                          <span className="text-destructive">✗</span>
                          <span>CDN和静态资源域名</span>
                        </li>
                        <li className="flex items-start space-x-2">
                          <span className="text-destructive">✗</span>
                          <span>随机生成模式域名</span>
                        </li>
                        <li className="flex items-start space-x-2">
                          <span className="text-destructive">✗</span>
                          <span>过多数字或连字符</span>
                        </li>
                        <li className="flex items-start space-x-2">
                          <span className="text-destructive">✗</span>
                          <span>自定义后缀过滤</span>
                        </li>
                        <li className="flex items-start space-x-2">
                          <span className="text-success">✓</span>
                          <span>高质量反向链接域名</span>
                        </li>
                      </ul>
                    </div>

                    <div>
                      <h4 className="font-semibold text-foreground mb-3">智能功能</h4>
                      <ul className="space-y-2 text-sm text-muted-foreground">
                        <li className="flex items-start space-x-2">
                          <span className="text-primary">•</span>
                          <span>域名质量评分：高/中/低三级评分</span>
                        </li>
                        <li className="flex items-start space-x-2">
                          <span className="text-primary">•</span>
                          <span>垃圾域名检测：自动识别低质量域名</span>
                        </li>
                        <li className="flex items-start space-x-2">
                          <span className="text-primary">•</span>
                          <span>URL解析：自动从完整URL提取域名</span>
                        </li>
                        <li className="flex items-start space-x-2">
                          <span className="text-primary">•</span>
                          <span>重复检测：自动去除重复域名</span>
                        </li>
                        <li className="flex items-start space-x-2">
                          <span className="text-primary">•</span>
                          <span>批量处理：支持大量域名同时处理</span>
                        </li>
                      </ul>
                    </div>

                    <div>
                      <h4 className="font-semibold text-foreground mb-3">导出功能</h4>
                      <ul className="space-y-2 text-sm text-muted-foreground">
                        <li className="flex items-start space-x-2">
                          <span className="text-primary">•</span>
                          <span>CSV格式：包含域名和质量评分</span>
                        </li>
                        <li className="flex items-start space-x-2">
                          <span className="text-primary">•</span>
                          <span>详细CSV：包含所有域名和过滤原因</span>
                        </li>
                        <li className="flex items-start space-x-2">
                          <span className="text-primary">•</span>
                          <span>TXT格式：纯文本，每行一个域名</span>
                        </li>
                        <li className="flex items-start space-x-2">
                          <span className="text-primary">•</span>
                          <span>JSON格式：完整分析数据和设置</span>
                        </li>
                        <li className="flex items-start space-x-2">
                          <span className="text-primary">•</span>
                          <span>一键复制：快速复制到剪贴板</span>
                        </li>
                      </ul>
                    </div>

                    <div>
                      <h4 className="font-semibold text-foreground mb-3">使用场景</h4>
                      <ul className="space-y-2 text-sm text-muted-foreground">
                        <li className="flex items-start space-x-2">
                          <span className="text-primary">•</span>
                          <span>SEMrush反向链接数据清理</span>
                        </li>
                        <li className="flex items-start space-x-2">
                          <span className="text-primary">•</span>
                          <span>竞争对手反向链接分析</span>
                        </li>
                        <li className="flex items-start space-x-2">
                          <span className="text-primary">•</span>
                          <span>链接建设机会筛选</span>
                        </li>
                        <li className="flex items-start space-x-2">
                          <span className="text-primary">•</span>
                          <span>域名质量评估和分类</span>
                        </li>
                      </ul>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

          </div>
        </div>
      </section>

      <FrontendFooter />
      <Toaster position="top-right" richColors closeButton duration={5000} />
    </div>
  )
}
