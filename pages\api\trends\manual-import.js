/**
 * 手动导入API - 处理TXT文件和文本粘贴导入
 */

import { saveManualToAPI } from '../../../lib/csv-parser.js'

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      error: 'Method not allowed. Use POST.'
    })
  }

  try {
    // 获取请求数据
    const { keywords, source = 'manual_import' } = req.body

    if (!keywords || !Array.isArray(keywords)) {
      return res.status(400).json({
        success: false,
        error: '请提供关键词数组'
      })
    }

    if (keywords.length === 0) {
      return res.status(400).json({
        success: false,
        error: '关键词列表不能为空'
      })
    }

    if (keywords.length > 1000) {
      return res.status(400).json({
        success: false,
        error: '关键词数量超过限制（最多1000个）'
      })
    }

    // 解析统计
    const parseResult = {
      totalLines: keywords.length,
      totalKeywords: keywords.length
    }

    // 导入信息
    const importInfo = {
      source: source,
      totalLines: keywords.length,
      rawKeywords: keywords.length,
      importedAt: new Date().toISOString()
    }

    // 基础过滤和去重
    const filteredKeywords = []
    const seenKeywords = new Set()
    let duplicateCount = 0

    for (const keyword of keywords) {
      const normalizedKeyword = keyword.keyword.toLowerCase().trim()

      // 基础过滤：去除空白和过短的关键词
      if (normalizedKeyword.length < 2) {
        continue
      }

      // 去重
      if (seenKeywords.has(normalizedKeyword)) {
        duplicateCount++
        continue
      }

      seenKeywords.add(normalizedKeyword)
      filteredKeywords.push(keyword)
    }

    // 简化的过滤结果
    const filterResult = {
      total: keywords.length,
      accepted: filteredKeywords.length,
      rejected: keywords.length - filteredKeywords.length,
      passRate: `${Math.round((filteredKeywords.length / keywords.length) * 100)}%`,
      duplicateCount: duplicateCount,
      acceptedKeywords: filteredKeywords
    }

    // 保存到数据库
    const saveResult = await saveManualToAPI(filteredKeywords, importInfo)

    // 返回完整结果
    const result = {
      success: true,
      data: {
        parse: parseResult,
        import: importInfo,
        aiFilter: filterResult,
        database: saveResult
      }
    }



    res.status(200).json(result)

  } catch (error) {
    console.error('手动导入API错误:', error)
    
    // 根据错误类型返回不同的错误信息
    let errorMessage = '导入过程中发生错误'
    let statusCode = 500

    if (error.message.includes('数据库')) {
      errorMessage = '数据库操作失败，请稍后重试'
      statusCode = 503
    } else if (error.message.includes('网络')) {
      errorMessage = '网络连接失败，请检查网络后重试'
      statusCode = 503
    }

    res.status(statusCode).json({
      success: false,
      error: errorMessage,
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    })
  }
}
