import { withAuth } from "next-auth/middleware"

export default withAuth(
  function middleware() {
    // 中间件逻辑
  },
  {
    callbacks: {
      authorized: ({ token, req }) => {
        const { pathname } = req.nextUrl
        
        // 如果访问console路径，需要登录
        if (pathname.startsWith('/console')) {
          if (!token) return false // 未登录
          
          // 检查管理员专用页面
          const adminOnlyPaths = ['/console/keywords', '/console$']
          const isAdminPath = adminOnlyPaths.some(path => 
            pathname === '/console' || pathname.match(new RegExp(path))
          )
          
          if (isAdminPath && token.role !== 'admin') {
            return false // 普通用户无法访问管理员页面
          }
          
          return true // 允许访问
        }
        
        return true // 其他页面允许访问
      },
    },
  }
)

export const config = {
  matcher: ['/console/:path*']
}
