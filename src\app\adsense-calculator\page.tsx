'use client'

import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card'
import { Badge } from '../../components/ui/badge'
import { Button } from '../../components/ui/button'
import { Input } from '../../components/ui/input'
import { Label } from '../../components/ui/label'
import { Calculator, TrendingUp, DollarSign, Eye, MousePointer, BarChart3 } from 'lucide-react'
import FrontendHeader from '../../components/frontend-header'
import FrontendFooter from '../../components/frontend-footer'

export default function AdSenseCalculatorPage() {
  // 计算器状态
  const [impressions, setImpressions] = useState<string>('')
  const [clicks, setClicks] = useState<string>('')
  const [cpc, setCpc] = useState<string>('')
  const [cpm, setCpm] = useState<string>('')
  
  // 计算结果状态
  const [results, setResults] = useState<{
    totalRevenue: number
    clickRevenue: number
    impressionRevenue: number
    ctr: number
  } | null>(null)

  // 计算函数
  const calculateRevenue = () => {
    const impressionsNum = parseFloat(impressions) || 0
    const clicksNum = parseFloat(clicks) || 0
    const cpcNum = parseFloat(cpc) || 0
    const cpmNum = parseFloat(cpm) || 0

    if (impressionsNum <= 0) {
      alert('请输入有效的展示次数')
      return
    }

    // 计算点击收益
    const clickRevenue = clicksNum * cpcNum
    
    // 计算展示收益
    const impressionRevenue = (impressionsNum * cpmNum) / 1000
    
    // 总收益
    const totalRevenue = clickRevenue + impressionRevenue
    
    // 点击率
    const ctr = impressionsNum > 0 ? (clicksNum / impressionsNum) * 100 : 0

    setResults({
      totalRevenue,
      clickRevenue,
      impressionRevenue,
      ctr
    })
  }

  // 重置函数
  const resetCalculator = () => {
    setImpressions('')
    setClicks('')
    setCpc('')
    setCpm('')
    setResults(null)
  }

  return (
    <div className="min-h-screen bg-background">
      {/* 使用统一前台顶部组件 */}
      <FrontendHeader />

      {/* 页面标题区域 */}
      <section className="py-6 px-4 bg-background border-b">
        <div className="container mx-auto">
          <div className="max-w-4xl mx-auto text-center">
            <div className="bg-card rounded-xl shadow-sm border p-6">
              <div className="flex items-center justify-center mb-4">
                <div className="p-3 bg-gradient-to-r from-green-500 to-blue-600 rounded-lg mr-3">
                  <Calculator className="h-8 w-8 text-white" />
                </div>
                <h1 className="text-3xl font-bold text-foreground">Google AdSense 收入计算器</h1>
              </div>
              <p className="text-muted-foreground text-lg">
                基于官方公式精确计算AdSense收入，支持CPC和CPM双重收益模式，提供完整的网站审核指南
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* 计算器主体区域 */}
      <section className="py-8 px-4">
        <div className="container mx-auto">
          <div className="max-w-6xl mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              
              {/* 左侧：输入区域 */}
              <Card className="shadow-lg border-2 border-blue-100">
                <CardHeader className="bg-gradient-to-r from-blue-50 to-purple-50">
                  <CardTitle className="flex items-center text-xl">
                    <BarChart3 className="h-5 w-5 mr-2 text-blue-600" />
                    输入广告数据
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-6 space-y-6">
                  
                  {/* 展示次数 */}
                  <div className="space-y-2">
                    <Label htmlFor="impressions" className="text-sm font-semibold flex items-center">
                      <Eye className="h-4 w-4 mr-2 text-blue-500" />
                      展示次数 (Impressions)
                    </Label>
                    <Input
                      id="impressions"
                      type="number"
                      placeholder="例如：1000"
                      value={impressions}
                      onChange={(e) => setImpressions(e.target.value)}
                      className="h-12 text-lg border-2 border-blue-200 focus:border-blue-500"
                    />
                    <p className="text-xs text-muted-foreground">广告被用户浏览的总次数</p>
                  </div>

                  {/* 点击次数 */}
                  <div className="space-y-2">
                    <Label htmlFor="clicks" className="text-sm font-semibold flex items-center">
                      <MousePointer className="h-4 w-4 mr-2 text-green-500" />
                      点击次数 (Clicks)
                    </Label>
                    <Input
                      id="clicks"
                      type="number"
                      placeholder="例如：20"
                      value={clicks}
                      onChange={(e) => setClicks(e.target.value)}
                      className="h-12 text-lg border-2 border-green-200 focus:border-green-500"
                    />
                    <p className="text-xs text-muted-foreground">用户实际点击广告的次数</p>
                  </div>

                  {/* CPC */}
                  <div className="space-y-2">
                    <Label htmlFor="cpc" className="text-sm font-semibold flex items-center">
                      <DollarSign className="h-4 w-4 mr-2 text-yellow-500" />
                      平均每次点击费用 (CPC)
                    </Label>
                    <Input
                      id="cpc"
                      type="number"
                      step="0.01"
                      placeholder="例如：0.5"
                      value={cpc}
                      onChange={(e) => setCpc(e.target.value)}
                      className="h-12 text-lg border-2 border-yellow-200 focus:border-yellow-500"
                    />
                    <p className="text-xs text-muted-foreground">单次点击产生的平均收入（美元）</p>
                  </div>

                  {/* CPM */}
                  <div className="space-y-2">
                    <Label htmlFor="cpm" className="text-sm font-semibold flex items-center">
                      <TrendingUp className="h-4 w-4 mr-2 text-purple-500" />
                      每千次展示费用 (CPM)
                    </Label>
                    <Input
                      id="cpm"
                      type="number"
                      step="0.01"
                      placeholder="例如：0.2"
                      value={cpm}
                      onChange={(e) => setCpm(e.target.value)}
                      className="h-12 text-lg border-2 border-purple-200 focus:border-purple-500"
                    />
                    <p className="text-xs text-muted-foreground">每1000次展示获得的费用（美元）</p>
                  </div>

                  {/* 操作按钮 */}
                  <div className="flex gap-3 pt-4">
                    <Button 
                      onClick={calculateRevenue}
                      className="flex-1 h-12 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white font-semibold"
                    >
                      <Calculator className="h-4 w-4 mr-2" />
                      计算收入
                    </Button>
                    <Button 
                      onClick={resetCalculator}
                      variant="outline"
                      className="h-12 border-2 border-gray-300 hover:border-gray-500"
                    >
                      重置
                    </Button>
                  </div>
                </CardContent>
              </Card>

              {/* 右侧：结果区域 */}
              <Card className="shadow-lg border-2 border-green-100">
                <CardHeader className="bg-gradient-to-r from-green-50 to-blue-50">
                  <CardTitle className="flex items-center text-xl">
                    <DollarSign className="h-5 w-5 mr-2 text-green-600" />
                    计算结果
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-6">
                  {results ? (
                    <div className="space-y-6">
                      
                      {/* 总收入 */}
                      <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg p-4 border-2 border-green-200">
                        <div className="flex items-center justify-between">
                          <span className="text-lg font-semibold text-green-800">总收入</span>
                          <span className="text-2xl font-bold text-green-600">
                            ${results.totalRevenue.toFixed(2)}
                          </span>
                        </div>
                        <p className="text-sm text-green-600 mt-1">点击收益 + 展示收益</p>
                      </div>

                      {/* 详细分解 */}
                      <div className="grid grid-cols-1 gap-4">
                        
                        {/* 点击收益 */}
                        <div className="bg-blue-50 rounded-lg p-4 border border-blue-200">
                          <div className="flex items-center justify-between">
                            <span className="font-semibold text-blue-800">点击收益 (CPC)</span>
                            <span className="text-xl font-bold text-blue-600">
                              ${results.clickRevenue.toFixed(2)}
                            </span>
                          </div>
                          <p className="text-sm text-blue-600 mt-1">
                            {clicks} 次点击 × ${cpc} CPC
                          </p>
                        </div>

                        {/* 展示收益 */}
                        <div className="bg-purple-50 rounded-lg p-4 border border-purple-200">
                          <div className="flex items-center justify-between">
                            <span className="font-semibold text-purple-800">展示收益 (CPM)</span>
                            <span className="text-xl font-bold text-purple-600">
                              ${results.impressionRevenue.toFixed(2)}
                            </span>
                          </div>
                          <p className="text-sm text-purple-600 mt-1">
                            {impressions} 次展示 × ${cpm} CPM ÷ 1000
                          </p>
                        </div>

                        {/* 点击率 */}
                        <div className="bg-yellow-50 rounded-lg p-4 border border-yellow-200">
                          <div className="flex items-center justify-between">
                            <span className="font-semibold text-yellow-800">点击率 (CTR)</span>
                            <span className="text-xl font-bold text-yellow-600">
                              {results.ctr.toFixed(2)}%
                            </span>
                          </div>
                          <p className="text-sm text-yellow-600 mt-1">
                            {clicks} 点击 ÷ {impressions} 展示 × 100%
                          </p>
                        </div>
                      </div>

                      {/* 性能评估 */}
                      <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                        <h4 className="font-semibold text-gray-800 mb-2">性能评估</h4>
                        <div className="space-y-2 text-sm">
                          <div className="flex justify-between">
                            <span>CTR 表现:</span>
                            <Badge variant={results.ctr >= 2 ? "default" : results.ctr >= 1 ? "secondary" : "destructive"}>
                              {results.ctr >= 2 ? "优秀" : results.ctr >= 1 ? "良好" : "需优化"}
                            </Badge>
                          </div>
                          <div className="flex justify-between">
                            <span>收入构成:</span>
                            <span className="text-gray-600">
                              点击 {((results.clickRevenue / results.totalRevenue) * 100).toFixed(1)}% | 
                              展示 {((results.impressionRevenue / results.totalRevenue) * 100).toFixed(1)}%
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="text-center py-12">
                      <Calculator className="h-16 w-16 mx-auto text-gray-400 mb-4" />
                      <h3 className="text-xl font-semibold text-foreground mb-2">等待计算</h3>
                      <p className="text-muted-foreground">
                        请在左侧输入广告数据，然后点击"计算收入"按钮
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* 公式说明区域 */}
      <section className="py-8 px-4 bg-gray-50">
        <div className="container mx-auto">
          <div className="max-w-4xl mx-auto">
            <Card className="shadow-lg">
              <CardHeader className="bg-gradient-to-r from-blue-500 to-purple-600 text-white">
                <CardTitle className="text-2xl flex items-center">
                  <TrendingUp className="h-6 w-6 mr-3" />
                  Google AdSense 收入计算公式详解
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6">

                {/* 基础公式 */}
                <div className="mb-8">
                  <h3 className="text-xl font-bold text-foreground mb-4">基础计算公式</h3>
                  <div className="bg-blue-50 rounded-lg p-4 border-l-4 border-blue-500 mb-4">
                    <p className="font-semibold text-blue-800 mb-2">完整公式：</p>
                    <code className="text-lg bg-white px-3 py-2 rounded border">
                      总收入 = 点击次数 × CPC + 展示次数 × CPM ÷ 1000
                    </code>
                  </div>
                  <div className="bg-green-50 rounded-lg p-4 border-l-4 border-green-500">
                    <p className="font-semibold text-green-800 mb-2">简化公式（按CTR推导）：</p>
                    <code className="text-lg bg-white px-3 py-2 rounded border">
                      收入 = 展示次数 × CTR × CPC
                    </code>
                    <p className="text-sm text-green-600 mt-2">注：此公式未包含CPM收入部分</p>
                  </div>
                </div>

                {/* 核心指标说明 */}
                <div className="mb-8">
                  <h3 className="text-xl font-bold text-foreground mb-4">核心指标说明</h3>
                  <div className="overflow-x-auto">
                    <table className="w-full border-collapse border border-gray-300">
                      <thead>
                        <tr className="bg-gray-100">
                          <th className="border border-gray-300 px-4 py-2 text-left font-semibold">指标</th>
                          <th className="border border-gray-300 px-4 py-2 text-left font-semibold">全称</th>
                          <th className="border border-gray-300 px-4 py-2 text-left font-semibold">定义</th>
                          <th className="border border-gray-300 px-4 py-2 text-left font-semibold">作用</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr>
                          <td className="border border-gray-300 px-4 py-2 font-semibold text-blue-600">CPC</td>
                          <td className="border border-gray-300 px-4 py-2">Cost Per Click</td>
                          <td className="border border-gray-300 px-4 py-2">广告主每次点击广告的平均费用</td>
                          <td className="border border-gray-300 px-4 py-2">直接决定单次点击产生的收入，受网站内容质量、用户群体等影响</td>
                        </tr>
                        <tr className="bg-gray-50">
                          <td className="border border-gray-300 px-4 py-2 font-semibold text-purple-600">CPM</td>
                          <td className="border border-gray-300 px-4 py-2">Cost Per Mille</td>
                          <td className="border border-gray-300 px-4 py-2">每1000次广告展示获得的费用</td>
                          <td className="border border-gray-300 px-4 py-2">反映广告展示的潜在收益，无需用户点击即可产生</td>
                        </tr>
                        <tr>
                          <td className="border border-gray-300 px-4 py-2 font-semibold text-green-600">CTR</td>
                          <td className="border border-gray-300 px-4 py-2">Click-Through Rate</td>
                          <td className="border border-gray-300 px-4 py-2">点击次数与展示次数的比率（CTR=点击次数/展示次数×100%）</td>
                          <td className="border border-gray-300 px-4 py-2">衡量广告吸引力，与收入成正比</td>
                        </tr>
                        <tr className="bg-gray-50">
                          <td className="border border-gray-300 px-4 py-2 font-semibold text-yellow-600">展示次数</td>
                          <td className="border border-gray-300 px-4 py-2">Impressions</td>
                          <td className="border border-gray-300 px-4 py-2">广告被用户浏览的总次数</td>
                          <td className="border border-gray-300 px-4 py-2">收入计算的基础数据之一</td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>

                {/* 实例计算 */}
                <div className="mb-8">
                  <h3 className="text-xl font-bold text-foreground mb-4">实例计算</h3>
                  <div className="bg-yellow-50 rounded-lg p-4 border border-yellow-200">
                    <p className="font-semibold text-yellow-800 mb-3">假设某网站24小时内广告数据如下：</p>
                    <ul className="list-disc list-inside text-yellow-700 mb-4 space-y-1">
                      <li>展示次数 = 1000次</li>
                      <li>点击次数 = 20次</li>
                      <li>CPC = 0.5美元</li>
                      <li>CPM = 0.2美元</li>
                    </ul>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="bg-white rounded p-3 border">
                        <p className="font-semibold text-blue-800 mb-2">按完整公式计算：</p>
                        <p className="text-sm">总收入 = 20×0.5 + 1000×0.2÷1000</p>
                        <p className="text-sm">= 10 + 0.2 = <span className="font-bold text-green-600">10.2美元</span></p>
                      </div>
                      <div className="bg-white rounded p-3 border">
                        <p className="font-semibold text-green-800 mb-2">按简化公式推导：</p>
                        <p className="text-sm">CTR = 20÷1000 = 2%</p>
                        <p className="text-sm">收入 = 1000×2%×0.5 = <span className="font-bold text-blue-600">10美元</span></p>
                        <p className="text-xs text-gray-500">（未包含CPM收入部分）</p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* 关键结论 */}
                <div>
                  <h3 className="text-xl font-bold text-foreground mb-4">关键结论</h3>
                  <div className="space-y-4">
                    <div className="bg-green-50 rounded-lg p-4 border-l-4 border-green-500">
                      <h4 className="font-semibold text-green-800 mb-2">1. 收入组成</h4>
                      <p className="text-green-700">AdSense收入由点击收益（CPC）和展示收益（CPM）共同构成，两者需分别计算后相加。</p>
                    </div>
                    <div className="bg-blue-50 rounded-lg p-4 border-l-4 border-blue-500">
                      <h4 className="font-semibold text-blue-800 mb-2">2. 优化方向</h4>
                      <p className="text-blue-700">提高点击率（CTR）和平均点击费用（CPC）可直接提升收入，同时增加展示次数（如通过优化流量和广告位置）也是重要手段。</p>
                    </div>
                    <div className="bg-purple-50 rounded-lg p-4 border-l-4 border-purple-500">
                      <h4 className="font-semibold text-purple-800 mb-2">3. 数据参考</h4>
                      <p className="text-purple-700">实际计算中需结合后台提供的点击次数、展示次数、CPC、CPM等具体数据，公式为灵活计算工具。</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* 优化策略区域 */}
      <section className="py-8 px-4">
        <div className="container mx-auto">
          <div className="max-w-4xl mx-auto">
            <Card className="shadow-lg">
              <CardHeader className="bg-gradient-to-r from-green-500 to-teal-600 text-white">
                <CardTitle className="text-2xl flex items-center">
                  <TrendingUp className="h-6 w-6 mr-3" />
                  Google AdSense 网站审核要点
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6">

                {/* 内容质量要求 */}
                <div className="mb-8">
                  <h3 className="text-xl font-bold text-foreground mb-4 flex items-center">
                    <span className="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent text-white hover:bg-primary/80 mr-2 bg-blue-500">一</span>
                    内容质量要求
                  </h3>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="bg-blue-50 rounded-lg p-4 border border-blue-200">
                      <h4 className="font-semibold text-blue-800 mb-3">📝 原创性</h4>
                      <ul className="text-blue-600 text-sm space-y-2">
                        <li>• 禁止搬运、抄袭或聚合第三方内容</li>
                        <li>• 需提供原创文章/视频/图片等</li>
                        <li>• 图文需保持合理比例，避免文字过少</li>
                      </ul>
                    </div>

                    <div className="bg-green-50 rounded-lg p-4 border border-green-200">
                      <h4 className="font-semibold text-green-800 mb-3">⚖️ 合规性</h4>
                      <ul className="text-green-600 text-sm space-y-2">
                        <li>• 禁止成人内容、暴力、赌博、侵权等</li>
                        <li>• 版权声明需清晰标注原创或授权来源</li>
                        <li>• 避免敏感题材内容</li>
                      </ul>
                    </div>

                    <div className="bg-purple-50 rounded-lg p-4 border border-purple-200">
                      <h4 className="font-semibold text-purple-800 mb-3">💡 实用性</h4>
                      <ul className="text-purple-600 text-sm space-y-2">
                        <li>• 内容需具备用户价值</li>
                        <li>• 如教程、资讯、工具等</li>
                        <li>• 避免纯广告或低质量内容</li>
                      </ul>
                    </div>
                  </div>
                </div>

                {/* 广告展示规范 */}
                <div className="mb-8">
                  <h3 className="text-xl font-bold text-foreground mb-4 flex items-center">
                    <span className="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent text-white hover:bg-primary/80 mr-2 bg-green-500">二</span>
                    广告展示规范
                  </h3>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="bg-orange-50 rounded-lg p-4 border border-orange-200">
                      <h4 className="font-semibold text-orange-800 mb-3">📍 广告位置</h4>
                      <ul className="text-orange-600 text-sm space-y-2">
                        <li>• 广告不得遮挡核心内容</li>
                        <li>• 禁止弹窗、全屏广告</li>
                        <li>• 移动端需适配屏幕</li>
                        <li>• 避免广告挤压文字</li>
                      </ul>
                    </div>

                    <div className="bg-blue-50 rounded-lg p-4 border border-blue-200">
                      <h4 className="font-semibold text-blue-800 mb-3">📊 广告数量</h4>
                      <ul className="text-blue-600 text-sm space-y-2">
                        <li>• 单页面广告数量需适度</li>
                        <li>• 避免过度密集影响体验</li>
                        <li>• 推荐比例：广告面积不超过页面内容的30%</li>
                      </ul>
                    </div>

                    <div className="bg-red-50 rounded-lg p-4 border border-red-200">
                      <h4 className="font-semibold text-red-800 mb-3">🎯 广告类型</h4>
                      <ul className="text-red-600 text-sm space-y-2">
                        <li>• 禁止使用第三方非AdSense广告</li>
                        <li>• 某些行业需遵守额外广告限制</li>
                        <li>• 如金融、健康等领域</li>
                      </ul>
                    </div>
                  </div>
                </div>

                {/* 法律与隐私合规 */}
                <div className="mb-8">
                  <h3 className="text-xl font-bold text-foreground mb-4 flex items-center">
                    <span className="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent text-white hover:bg-primary/80 mr-2 bg-purple-500">三</span>
                    法律与隐私合规
                  </h3>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="bg-indigo-50 rounded-lg p-4 border border-indigo-200">
                      <h4 className="font-semibold text-indigo-800 mb-3">🔒 隐私政策</h4>
                      <ul className="text-indigo-600 text-sm space-y-2">
                        <li>• 必须在网站显著位置链接至隐私政策页面</li>
                        <li>• 说明数据收集与使用方式</li>
                        <li>• 需符合GDPR等地区性法规</li>
                      </ul>
                    </div>

                    <div className="bg-yellow-50 rounded-lg p-4 border border-yellow-200">
                      <h4 className="font-semibold text-yellow-800 mb-3">👶 年龄限制</h4>
                      <ul className="text-yellow-600 text-sm space-y-2">
                        <li>• 网站需声明目标用户年龄≥13岁</li>
                        <li>• 部分国家可能要求更高年龄</li>
                        <li>• 需在隐私政策中明确说明</li>
                      </ul>
                    </div>

                    <div className="bg-teal-50 rounded-lg p-4 border border-teal-200">
                      <h4 className="font-semibold text-teal-800 mb-3">©️ 版权声明</h4>
                      <ul className="text-teal-600 text-sm space-y-2">
                        <li>• 需明确标注内容版权归属</li>
                        <li>• 禁止未经授权使用素材</li>
                        <li>• 原创内容需标明版权信息</li>
                      </ul>
                    </div>
                  </div>
                </div>

                {/* 技术基础要求 */}
                <div className="mb-8">
                  <h3 className="text-xl font-bold text-foreground mb-4 flex items-center">
                    <span className="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent text-white hover:bg-primary/80 mr-2 bg-red-500">四</span>
                    技术基础要求
                  </h3>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="bg-cyan-50 rounded-lg p-4 border border-cyan-200">
                      <h4 className="font-semibold text-cyan-800 mb-3">🌐 网站可访问性</h4>
                      <ul className="text-cyan-600 text-sm space-y-2">
                        <li>• 网站需公开可访问，且无地区访问限制</li>
                        <li>• 移动端需确保页面加载速度与适配性</li>
                        <li>• 确保网站稳定运行</li>
                      </ul>
                    </div>

                    <div className="bg-emerald-50 rounded-lg p-4 border border-emerald-200">
                      <h4 className="font-semibold text-emerald-800 mb-3">📅 内容更新频率</h4>
                      <ul className="text-emerald-600 text-sm space-y-2">
                        <li>• 新站建议至少发布3-5篇内容后再申请</li>
                        <li>• 避免审核时内容过少</li>
                        <li>• 保持定期内容更新</li>
                      </ul>
                    </div>
                  </div>
                </div>

                {/* 常见拒绝原因 */}
                <div>
                  <h3 className="text-xl font-bold text-foreground mb-4 flex items-center">
                    <span className="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent text-white hover:bg-primary/80 mr-2 bg-gray-600">五</span>
                    常见拒绝原因
                  </h3>

                  <div className="space-y-4">
                    <div className="bg-red-50 rounded-lg p-4 border-l-4 border-red-500">
                      <h4 className="font-semibold text-red-800 mb-3">⚠️ 主要拒绝原因</h4>
                      <ul className="text-red-700 text-sm space-y-2">
                        <li>• 广告侵入式展示（如遮挡内容、频繁弹窗）</li>
                        <li>• 内容涉及敏感话题或版权争议</li>
                        <li>• 隐私政策缺失或不符合法规</li>
                        <li>• 网站未完成ICP备案（针对国内网站）</li>
                      </ul>
                    </div>

                    <div className="bg-blue-50 rounded-lg p-4 border-l-4 border-blue-500">
                      <h4 className="font-semibold text-blue-800 mb-2">💡 重要提示</h4>
                      <p className="text-blue-700 text-sm">审核通过后需持续优化用户体验，避免后期政策违规。建议定期参考AdSense官方指南更新策略。</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* 使用统一前台底部组件 */}
      <FrontendFooter />
    </div>
  )
}
