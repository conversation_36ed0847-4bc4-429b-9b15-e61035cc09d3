/**
 * 关键词拓展API
 * 实现关键词拓展、重复词过滤和存储管理
 */

const googleAPI = require('../../../lib/google-api');

// 简单的关键词过滤函数 - 过滤包含特殊符号的关键词
function shouldFilterKeyword(keyword) {
  if (!keyword || typeof keyword !== 'string') return true;

  const keywordText = keyword.trim();

  // 过滤包含特殊符号的关键词（保留字母、数字、空格、连字符）
  if (/[^\w\s-]/.test(keywordText)) return true;

  return false;
}

export default async function handler(req, res) {
  // 设置CORS头
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');

  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  if (req.method === 'POST') {
    return handleExpandKeywords(req, res);
  }

  return res.status(405).json({
    success: false,
    error: '不支持的请求方法，请使用POST'
  });
}

/**
 * 处理关键词拓展
 */
async function handleExpandKeywords(req, res) {
  try {
    const { 
      keywords, 
      suggestions_per_keyword = 10,
      save_to_database = true,
      filter_duplicates = true,
      min_length = 2,
      max_length = 100
    } = req.body;

    // 验证输入
    if (!keywords || !Array.isArray(keywords) || keywords.length === 0) {
      return res.status(400).json({
        success: false,
        error: '请提供有效的关键词数组'
      });
    }

    if (keywords.length > 20) {
      return res.status(400).json({
        success: false,
        error: '单次拓展最多支持20个关键词'
      });
    }

    const maxSuggestions = Math.min(parseInt(suggestions_per_keyword) || 10, 20);



    // 获取搜索建议
    const batchResult = await googleAPI.getBatchSuggestions(keywords, maxSuggestions);

    if (!batchResult.success) {
      return res.status(500).json({
        success: false,
        error: '获取搜索建议失败'
      });
    }

    // 处理和过滤结果
    const processedResults = await processExpandResults(
      batchResult.results, 
      {
        filter_duplicates,
        min_length,
        max_length,
        original_keywords: keywords
      }
    );

    // 保存到数据库（如果需要）
    let saveResults = null;
    if (save_to_database && processedResults.new_keywords.length > 0) {
      saveResults = await saveExpandedKeywords(processedResults.new_keywords);
    }

    // 返回结果
    return res.status(200).json({
      success: true,
      data: {
        summary: {
          original_keywords: keywords.length,
          total_suggestions: processedResults.total_suggestions,
          unique_suggestions: processedResults.unique_suggestions,
          new_keywords: processedResults.new_keywords.length,
          duplicates_filtered: processedResults.duplicates_filtered,
          saved_to_database: save_to_database ? (saveResults?.saved || 0) : 0
        },
        expansion_results: processedResults.expansion_results,
        new_keywords: processedResults.new_keywords,
        save_results: saveResults
      },
      message: `成功拓展 ${keywords.length} 个关键词，获得 ${processedResults.new_keywords.length} 个新关键词`
    });

  } catch {
    return res.status(500).json({
      success: false,
      error: '服务器内部错误: ' + error.message
    });
  }
}

/**
 * 处理拓展结果
 */
async function processExpandResults(batchResults, options) {
  const {
    filter_duplicates = true,
    min_length = 2,
    max_length = 100,
    original_keywords = []
  } = options;

  const allSuggestions = [];
  const expansionResults = [];
  let totalSuggestions = 0;
  let duplicatesFiltered = 0;

  // 处理每个关键词的建议
  for (const result of batchResults) {
    const keywordResult = {
      original_keyword: result.query,
      success: result.success,
      suggestions: result.suggestions || [],
      source: result.source,
      error: result.error
    };

    if (result.success && result.suggestions) {
      totalSuggestions += result.suggestions.length;
      
      // 过滤建议 - 应用更严格的质量过滤
      const filteredSuggestions = result.suggestions.filter(suggestion => {
        // 长度过滤
        if (suggestion.length < min_length || suggestion.length > max_length) {
          return false;
        }

        // 排除原始关键词
        if (original_keywords.some(orig =>
          orig.toLowerCase() === suggestion.toLowerCase()
        )) {
          duplicatesFiltered++;
          return false;
        }

        // 过滤包含特殊符号的关键词
        if (shouldFilterKeyword(suggestion)) {
          return false;
        }

        return true;
      });

      keywordResult.filtered_suggestions = filteredSuggestions;
      allSuggestions.push(...filteredSuggestions);
    }

    expansionResults.push(keywordResult);
  }

  // 去重处理
  let uniqueSuggestions = allSuggestions;
  if (filter_duplicates) {
    const beforeCount = allSuggestions.length;
    uniqueSuggestions = [...new Set(allSuggestions.map(s => s.toLowerCase()))]
      .map(s => allSuggestions.find(orig => orig.toLowerCase() === s));
    duplicatesFiltered += beforeCount - uniqueSuggestions.length;
  }

  // 检查数据库中的重复词
  const newKeywords = await filterExistingKeywords(uniqueSuggestions);

  return {
    expansion_results: expansionResults,
    total_suggestions: totalSuggestions,
    unique_suggestions: uniqueSuggestions.length,
    new_keywords: newKeywords,
    duplicates_filtered: duplicatesFiltered
  };
}

/**
 * 过滤数据库中已存在的关键词
 */
async function filterExistingKeywords(suggestions) {
  try {
    // 调用PHP API检查重复
    const response = await fetch(`${process.env.NEXT_PUBLIC_PHP_API_URL}/check-duplicates.php`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        keywords: suggestions
      })
    });

    if (!response.ok) {
      return suggestions;
    }

    const data = await response.json();
    
    if (data.success) {
      return data.data.new_keywords || suggestions;
    } else {
      return suggestions;
    }

  } catch {
    return suggestions; // 出错时返回所有建议
  }
}

/**
 * 保存拓展的关键词到数据库
 */
async function saveExpandedKeywords(keywords) {
  try {
    if (!keywords || keywords.length === 0) {
      return { success: true, saved: 0, errors: [] };
    }

    // 调用PHP API保存关键词
    const response = await fetch(`${process.env.NEXT_PUBLIC_PHP_API_URL}/keywords.php`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        keywords: keywords.map(keyword => ({
          keyword: keyword,
          source: 'google_suggestions',
          import_date: new Date().toISOString().split('T')[0]
        }))
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    
    return {
      success: data.success,
      saved: data.data?.saved || 0,
      errors: data.data?.errors || [],
      message: data.message
    };

  } catch (error) {
    console.error('保存拓展关键词失败:', error);
    return {
      success: false,
      saved: 0,
      errors: [error.message],
      message: '保存失败: ' + error.message
    };
  }
}
