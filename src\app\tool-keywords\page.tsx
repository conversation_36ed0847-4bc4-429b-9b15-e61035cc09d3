'use client'

import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card'
import { Badge } from '../../components/ui/badge'
import { Button } from '../../components/ui/button'
import { Input } from '../../components/ui/input'
import { Search, Lightbulb, TrendingUp, Target, Zap, Shield, Settings, BookOpen, ShoppingCart, BarChart3, Sparkles, Smartphone, Cloud, Camera, ExternalLink as LinkIcon } from 'lucide-react'
import FrontendHeader from '../../components/frontend-header'
import FrontendFooter from '../../components/frontend-footer'

// 关键词数据结构
interface KeywordItem {
  keyword: string
  meaning: string
  userNeed: string
  examples: string[]
}

interface KeywordCategory {
  name: string
  icon: React.ReactNode
  color: string
  keywords: KeywordItem[]
}

export default function ToolKeywordsPage() {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('all')

  // 生成Google Trends链接
  const generateTrendsUrl = (keyword: string) => {
    return `https://trends.google.com/trends/explore?date=now%207-d&geo=US&q=${encodeURIComponent(keyword)},gpts&hl=en-US`
  }

  // 批量打开分类下所有关键词的Google Trends链接
  const openAllTrendsInCategory = (category: KeywordCategory) => {
    category.keywords.forEach((item, index) => {
      // 随机间隔500ms-1500ms，避免触发谷歌频率限制
      const randomDelay = Math.random() * 1000 + 500 + (index * 200) // 基础随机500-1500ms + 递增间隔
      setTimeout(() => {
        window.open(generateTrendsUrl(item.keyword), '_blank', 'noopener,noreferrer')
      }, randomDelay)
    })
  }

  // 关键词分类数据
  const keywordCategories: KeywordCategory[] = [
    {
      name: '内容创作与处理类',
      icon: <Lightbulb className="h-5 w-5" />,
      color: 'from-blue-500 to-cyan-600',
      keywords: [
        {
          keyword: 'Translator',
          meaning: '语言翻译工具',
          userNeed: '翻译文本、句子、文件',
          examples: ['Google Translator', 'Online Translator']
        },
        {
          keyword: 'Generator',
          meaning: '内容生成工具',
          userNeed: '生成密码、文本、图像等',
          examples: ['Password Generator', 'QR Code Generator']
        },
        {
          keyword: 'Editor',
          meaning: '内容编辑工具',
          userNeed: '编辑文本、图像、视频',
          examples: ['Photo Editor', 'Video Editor']
        },
        {
          keyword: 'Creator',
          meaning: '创意内容创作工具',
          userNeed: '创作图像、视频、音乐',
          examples: ['Logo Creator', 'Content Creator']
        },
        {
          keyword: 'Rewriter',
          meaning: '内容改写工具',
          userNeed: 'AI内容改写、语句重构',
          examples: ['AI Article Rewriter', 'Paraphrasing Tool']
        },
        {
          keyword: 'Summarizer',
          meaning: '文本摘要生成工具',
          userNeed: '自动生成摘要、要点提取',
          examples: ['AI Text Summarizer', 'Meeting Notes Summarizer']
        },
        {
          keyword: 'Maker',
          meaning: '制作工具',
          userNeed: '快速制作图像、模板',
          examples: ['Meme Maker', 'Avatar Maker', 'Banner Maker']
        },
        {
          keyword: 'Builder',
          meaning: '构建工具',
          userNeed: '零代码建站、表单构建',
          examples: ['Website Builder', 'App Builder', 'Form Builder']
        },
        {
          keyword: 'Designer',
          meaning: '设计工具',
          userNeed: '专业设计创作',
          examples: ['Logo Designer', 'Layout Designer', 'Color Designer']
        }
      ]
    },
    {
      name: '数据与格式处理类',
      icon: <BarChart3 className="h-5 w-5" />,
      color: 'from-green-500 to-emerald-600',
      keywords: [
        {
          keyword: 'Converter',
          meaning: '文件/格式转换工具',
          userNeed: '格式兼容性解决',
          examples: ['PDF Converter', 'Currency Converter']
        },
        {
          keyword: 'Analyzer',
          meaning: '数据分析工具',
          userNeed: '提取数据洞察',
          examples: ['Text Analyzer', 'Performance Analyzer']
        },
        {
          keyword: 'Extractor',
          meaning: '信息提取工具',
          userNeed: '从文本、图像中提取数据',
          examples: ['Text Extractor', 'Image Extractor']
        },
        {
          keyword: 'Parser',
          meaning: '解析工具',
          userNeed: '数据结构解析',
          examples: ['JSON Parser', 'XML Parser', 'Log Parser']
        },
        {
          keyword: 'Formatter',
          meaning: '格式化工具',
          userNeed: '代码、文本美化',
          examples: ['JSON Formatter', 'HTML Formatter', 'Code Formatter']
        },
        {
          keyword: 'Validator',
          meaning: '验证工具',
          userNeed: '数据格式校验',
          examples: ['Email Validator', 'HTML Validator', 'IBAN Validator']
        },
        {
          keyword: 'Merger',
          meaning: '文件合并工具',
          userNeed: '多文件合并处理',
          examples: ['PDF Merger', 'Video Merger', 'CSV Merger']
        },
        {
          keyword: 'Splitter',
          meaning: '文件分割工具',
          userNeed: '大文件分割处理',
          examples: ['Audio Splitter', 'PDF Splitter']
        }
      ]
    },
    {
      name: '实用工具与系统辅助类',
      icon: <Settings className="h-5 w-5" />,
      color: 'from-purple-500 to-pink-600',
      keywords: [
        {
          keyword: 'Calculator',
          meaning: '计算工具',
          userNeed: '数学/科学计算',
          examples: ['Mortgage Calculator', 'ROI Calculator']
        },
        {
          keyword: 'Downloader',
          meaning: '下载工具',
          userNeed: '媒体文件下载',
          examples: ['YouTube Downloader', 'Video Downloader']
        },
        {
          keyword: 'Uploader',
          meaning: '上传工具',
          userNeed: '文件上传处理',
          examples: ['File Uploader', 'Image Uploader']
        },
        {
          keyword: 'Viewer',
          meaning: '文件查看器',
          userNeed: '文件预览查看',
          examples: ['PDF Viewer', 'JSON Viewer', 'STL Viewer']
        },
        {
          keyword: 'Reader',
          meaning: '阅读器',
          userNeed: '文档阅读',
          examples: ['EPUB Reader', 'RSS Reader']
        },
        {
          keyword: 'Recorder',
          meaning: '录制工具',
          userNeed: '屏幕、音频录制',
          examples: ['Screen Recorder', 'Voice Recorder']
        },
        {
          keyword: 'Manager',
          meaning: '管理工具',
          userNeed: '任务/项目管理',
          examples: ['Project Manager', 'Task Manager']
        },
        {
          keyword: 'Monitor',
          meaning: '监控工具',
          userNeed: '系统/活动监控',
          examples: ['Performance Monitor', 'Network Monitor']
        },
        {
          keyword: 'Scheduler',
          meaning: '调度工具',
          userNeed: '时间规划、任务调度',
          examples: ['Meeting Scheduler', 'Task Scheduler', 'Content Scheduler']
        },
        {
          keyword: 'Organizer',
          meaning: '组织工具',
          userNeed: '信息整理归类',
          examples: ['File Organizer', 'Contact Organizer', 'Photo Organizer']
        },
        {
          keyword: 'Tracker',
          meaning: '跟踪工具',
          userNeed: '数据跟踪记录',
          examples: ['Time Tracker', 'Habit Tracker', 'Expense Tracker']
        },
        {
          keyword: 'Planner',
          meaning: '规划工具',
          userNeed: '计划制定',
          examples: ['Trip Planner', 'Meal Planner', 'Project Planner']
        }
      ]
    },
    {
      name: '安全与隐私类',
      icon: <Shield className="h-5 w-5" />,
      color: 'from-red-500 to-orange-600',
      keywords: [
        {
          keyword: 'Checker',
          meaning: '检测工具',
          userNeed: '安全检测、语法检查',
          examples: ['Password Checker', 'Grammar Checker', 'Link Checker']
        },
        {
          keyword: 'Scanner',
          meaning: '扫描工具',
          userNeed: '安全扫描、文档扫描',
          examples: ['Virus Scanner', 'QR Scanner', 'Port Scanner']
        },
        {
          keyword: 'Protector',
          meaning: '保护工具',
          userNeed: '隐私保护、文件保护',
          examples: ['Privacy Protector', 'File Protector']
        },
        {
          keyword: 'Detector',
          meaning: '检测工具',
          userNeed: '内容检测识别',
          examples: ['AI Face Detector', 'Plagiarism Detector']
        }
      ]
    },
    {
      name: '优化与性能类',
      icon: <Zap className="h-5 w-5" />,
      color: 'from-yellow-500 to-orange-600',
      keywords: [
        {
          keyword: 'Optimizer',
          meaning: '优化工具',
          userNeed: '性能优化提升',
          examples: ['Image Optimizer', 'SEO Optimizer', 'Code Optimizer']
        },
        {
          keyword: 'Compressor',
          meaning: '压缩工具',
          userNeed: '文件体积压缩',
          examples: ['Image Compressor', 'Video Compressor', 'PDF Compressor']
        },
        {
          keyword: 'Enhancer',
          meaning: '增强工具',
          userNeed: '质量提升',
          examples: ['Photo Enhancer', 'Audio Enhancer', 'AI Enhancer']
        },
        {
          keyword: 'Cleaner',
          meaning: '清理工具',
          userNeed: '冗余数据清理',
          examples: ['Disk Cleaner', 'Registry Cleaner', 'Cache Cleaner']
        },
        {
          keyword: 'Resizer',
          meaning: '尺寸调整工具',
          userNeed: '图片/视频尺寸调整',
          examples: ['Image Resizer', 'Video Resizer', 'Icon Resizer']
        },
        {
          keyword: 'Cropper',
          meaning: '裁剪工具',
          userNeed: '精准裁剪',
          examples: ['Photo Cropper', 'PDF Cropper', 'GIF Cropper']
        }
      ]
    },
    {
      name: '专业技术类',
      icon: <Settings className="h-5 w-5" />,
      color: 'from-indigo-500 to-purple-600',
      keywords: [
        {
          keyword: 'Debugger',
          meaning: '调试工具',
          userNeed: '代码调试',
          examples: ['Code Debugger', 'Network Debugger', 'AI Debugger']
        },
        {
          keyword: 'Tester',
          meaning: '测试工具',
          userNeed: '功能测试',
          examples: ['Website Tester', 'API Tester', 'Load Tester', 'Regex Tester']
        },
        {
          keyword: 'Emulator',
          meaning: '模拟器',
          userNeed: '系统/硬件模拟',
          examples: ['Android Emulator', 'Game Boy Emulator']
        },
        {
          keyword: 'Sandbox',
          meaning: '沙箱环境',
          userNeed: '安全测试环境',
          examples: ['JS Sandbox', 'Python Sandbox', 'SQL Sandbox']
        },
        {
          keyword: 'Wrapper',
          meaning: '封装工具',
          userNeed: 'API封装、软件打包',
          examples: ['REST API Wrapper', 'Docker Wrapper']
        },
        {
          keyword: 'Launcher',
          meaning: '启动器',
          userNeed: '应用启动管理',
          examples: ['App Launcher', 'Command Launcher']
        }
      ]
    },
    {
      name: '内容处理进阶类',
      icon: <Target className="h-5 w-5" />,
      color: 'from-pink-500 to-rose-600',
      keywords: [
        {
          keyword: 'Annotator',
          meaning: '标注工具',
          userNeed: '内容标注批注',
          examples: ['PDF Annotator', 'Image Annotator', 'Video Annotator']
        },
        {
          keyword: 'Labeler',
          meaning: '标签工具',
          userNeed: '数据标注',
          examples: ['Image Labeler', 'AI Dataset Labeler']
        },
        {
          keyword: 'Remover',
          meaning: '移除工具',
          userNeed: '对象移除',
          examples: ['Background Remover', 'Watermark Remover', 'Object Eraser']
        },
        {
          keyword: 'Filler',
          meaning: '填充工具',
          userNeed: '自动填表',
          examples: ['Form Filler', 'Auto Form Filler']
        },
        {
          keyword: 'Mixer',
          meaning: '混合工具',
          userNeed: '内容混合',
          examples: ['Audio Mixer', 'Color Mixer', 'Content Mixer']
        }
      ]
    },
    {
      name: '学习与教育类',
      icon: <BookOpen className="h-5 w-5" />,
      color: 'from-emerald-500 to-teal-600',
      keywords: [
        {
          keyword: 'Trainer',
          meaning: '训练工具',
          userNeed: '技能训练',
          examples: ['Typing Trainer', 'Memory Trainer', 'Language Trainer']
        },
        {
          keyword: 'Tutor',
          meaning: '教学工具',
          userNeed: '个性化教学',
          examples: ['Math Tutor', 'Code Tutor', 'Language Tutor']
        },
        {
          keyword: 'Quiz',
          meaning: '测验工具',
          userNeed: '知识测试',
          examples: ['Knowledge Quiz', 'Skill Quiz', 'Fun Quiz']
        },
        {
          keyword: 'Flashcard',
          meaning: '记忆卡片',
          userNeed: '记忆训练',
          examples: ['Study Flashcard', 'Language Flashcard']
        }
      ]
    },
    {
      name: '电商与商业类',
      icon: <ShoppingCart className="h-5 w-5" />,
      color: 'from-orange-500 to-red-600',
      keywords: [
        {
          keyword: 'Pricer',
          meaning: '定价工具',
          userNeed: '价格设定',
          examples: ['Product Pricer', 'Service Pricer', 'Dynamic Pricer']
        },
        {
          keyword: 'Estimator',
          meaning: '估算工具',
          userNeed: '成本估算',
          examples: ['Cost Estimator', 'Time Estimator', 'Budget Estimator']
        },
        {
          keyword: 'Comparator',
          meaning: '比较工具',
          userNeed: '产品对比',
          examples: ['Price Comparator', 'Product Comparator', 'Service Comparator']
        },
        {
          keyword: 'Aggregator',
          meaning: '聚合工具',
          userNeed: '数据聚合',
          examples: ['News Aggregator', 'Price Aggregator', 'Job Aggregator']
        }
      ]
    },
    {
      name: '性能与分析类',
      icon: <BarChart3 className="h-5 w-5" />,
      color: 'from-slate-500 to-gray-600',
      keywords: [
        {
          keyword: 'Benchmark',
          meaning: '基准测试',
          userNeed: '性能评估',
          examples: ['CPU Benchmark', 'Disk Benchmark', 'LLM Benchmark']
        },
        {
          keyword: 'Predictor',
          meaning: '预测工具',
          userNeed: '数据预测',
          examples: ['Stock Predictor', 'Trend Predictor', 'Weather Predictor']
        },
        {
          keyword: 'Simulator',
          meaning: '模拟器',
          userNeed: '情况模拟',
          examples: ['Flight Simulator', 'Trading Simulator', 'Physics Simulator']
        }
      ]
    }
  ]

  // 新兴趋势关键词
  const trendingKeywords = [
    {
      category: 'AI驱动类',
      icon: <Sparkles className="h-4 w-4" />,
      color: 'from-violet-500 to-purple-600',
      items: ['AI Assistant', 'AI Copilot', 'AI Automation', 'Smart Tool', 'Cloner']
    },
    {
      category: '移动端特化',
      icon: <Smartphone className="h-4 w-4" />,
      color: 'from-blue-500 to-indigo-600',
      items: ['Mobile Tool', 'App Tool', 'Responsive Tool']
    },
    {
      category: '云端与协作',
      icon: <Cloud className="h-4 w-4" />,
      color: 'from-cyan-500 to-blue-600',
      items: ['Cloud Tool', 'Collaborative Tool', 'Remote Tool']
    },
    {
      category: '截图与捕获类',
      icon: <Camera className="h-4 w-4" />,
      color: 'from-green-500 to-teal-600',
      items: ['Screenshot', 'Capture', 'Grabber']
    }
  ]

  // 搜索过滤逻辑
  const filteredCategories = keywordCategories.filter(category => {
    if (selectedCategory !== 'all' && category.name !== selectedCategory) return false
    
    if (searchTerm) {
      return category.keywords.some(keyword => 
        keyword.keyword.toLowerCase().includes(searchTerm.toLowerCase()) ||
        keyword.meaning.toLowerCase().includes(searchTerm.toLowerCase()) ||
        keyword.userNeed.toLowerCase().includes(searchTerm.toLowerCase()) ||
        keyword.examples.some(example => example.toLowerCase().includes(searchTerm.toLowerCase()))
      )
    }
    
    return true
  })

  const categories = ['all', ...keywordCategories.map(cat => cat.name)]

  return (
    <div className="min-h-screen bg-background">
      {/* 使用统一前台顶部组件 */}
      <FrontendHeader />

      {/* 页面标题区域 */}
      <section className="py-6 px-4 bg-background border-b">
        <div className="container mx-auto">
          <div className="max-w-4xl mx-auto text-center">
            <div className="bg-card rounded-xl shadow-sm border p-6">
              <div className="flex items-center justify-center mb-4">
                <div className="p-3 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg mr-3">
                  <Target className="h-8 w-8 text-white" />
                </div>
                <h1 className="text-3xl font-bold text-foreground">工具关键词大全</h1>
              </div>
              <p className="text-muted-foreground text-lg">
                130+工具站关键词完整清单，涵盖10大分类与新兴趋势，助力精准需求挖掘
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* 搜索和筛选区域 */}
      <section className="py-6 px-4 bg-gray-50">
        <div className="container mx-auto">
          <div className="max-w-4xl mx-auto">
            <div className="bg-card rounded-xl shadow-sm border p-6">
              {/* 搜索框 */}
              <div className="flex justify-center mb-6">
                <div className="relative w-full max-w-md">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-muted-foreground" />
                  <Input
                    type="text"
                    placeholder="搜索关键词、含义或示例..."
                    className="pl-10 h-12 text-lg border-input focus:border-primary focus:ring-primary"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
              </div>

              {/* 分类筛选标签 */}
              <div className="flex flex-wrap justify-center gap-2">
                {categories.map((category) => (
                  <Button
                    key={category}
                    variant={selectedCategory === category ? "default" : "outline"}
                    size="sm"
                    onClick={() => setSelectedCategory(category)}
                    className={selectedCategory === category
                      ? "bg-gradient-to-r from-blue-500 to-purple-600 text-white hover:from-blue-600 hover:to-purple-700 border-0"
                      : "bg-white/80 backdrop-blur-sm border-gray-200 text-gray-700 hover:bg-blue-50 hover:text-blue-600 hover:border-blue-300 transition-all duration-200"
                    }
                  >
                    {category === 'all' ? '全部分类' : category}
                  </Button>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 关键词内容区域 */}
      <section className="py-8 px-4">
        <div className="container mx-auto">
          {filteredCategories.length === 0 ? (
            <Card className="text-center py-12">
              <CardContent>
                <Search className="h-16 w-16 mx-auto text-gray-400 mb-4" />
                <h3 className="text-xl font-semibold text-foreground mb-2">未找到相关关键词</h3>
                <p className="text-muted-foreground">
                  试试调整搜索关键词或选择其他分类
                </p>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-8">
              {filteredCategories.map((category) => (
                <div key={category.name}>
                  <div className="flex items-center mb-6">
                    <div className={`p-2 bg-gradient-to-r ${category.color} rounded-lg mr-3 text-white`}>
                      {category.icon}
                    </div>
                    <div className="flex-1">
                      <h2 className="text-2xl font-bold text-foreground">{category.name}</h2>
                    </div>
                    <div className="flex items-center gap-2 ml-auto">
                      <Badge className="bg-gray-100 text-gray-700">
                        {category.keywords.length} 个关键词
                      </Badge>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => openAllTrendsInCategory(category)}
                        className="text-xs border-2 border-purple-200 hover:border-purple-500 hover:bg-purple-50 transition-colors flex items-center gap-1"
                      >
                        <LinkIcon className="h-3 w-3" />
                        新窗口
                      </Button>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {category.keywords
                      .filter(keyword => {
                        if (!searchTerm) return true
                        return keyword.keyword.toLowerCase().includes(searchTerm.toLowerCase()) ||
                               keyword.meaning.toLowerCase().includes(searchTerm.toLowerCase()) ||
                               keyword.userNeed.toLowerCase().includes(searchTerm.toLowerCase()) ||
                               keyword.examples.some(example => example.toLowerCase().includes(searchTerm.toLowerCase()))
                      })
                      .map((keyword, index) => (
                        <Card key={index} className="group hover:shadow-lg transition-all duration-200 hover:scale-105 border-border hover:border-primary/30">
                          <CardHeader className="pb-3">
                            <CardTitle className="text-lg font-bold text-foreground group-hover:text-primary transition-colors flex items-center">
                              <div className={`p-1.5 bg-gradient-to-r ${category.color} rounded-md mr-2`}>
                                <Zap className="h-4 w-4 text-white" />
                              </div>
                              <span className="truncate">{keyword.keyword}</span>
                            </CardTitle>
                          </CardHeader>
                          <CardContent className="pt-0">
                            <div className="space-y-3">
                              <div>
                                <p className="text-sm font-semibold text-blue-800 mb-1">含义</p>
                                <p className="text-sm text-muted-foreground">{keyword.meaning}</p>
                              </div>

                              <div>
                                <p className="text-sm font-semibold text-green-800 mb-1">用户需求</p>
                                <p className="text-sm text-muted-foreground">{keyword.userNeed}</p>
                              </div>

                              <div>
                                <p className="text-sm font-semibold text-purple-800 mb-2">常见搭配</p>
                                <div className="flex flex-wrap gap-1">
                                  {keyword.examples.map((example, idx) => (
                                    <Badge key={idx} variant="secondary" className="text-xs bg-gray-100 text-gray-700 hover:bg-gray-200">
                                      {example}
                                    </Badge>
                                  ))}
                                </div>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </section>

      {/* 新兴趋势关键词区域 */}
      <section className="py-8 px-4 bg-gradient-to-r from-blue-50 to-purple-50">
        <div className="container mx-auto">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-8">
              <div className="flex items-center justify-center mb-4">
                <div className="p-3 bg-gradient-to-r from-violet-500 to-purple-600 rounded-lg mr-3">
                  <TrendingUp className="h-8 w-8 text-white" />
                </div>
                <h2 className="text-3xl font-bold text-foreground">新兴趋势关键词</h2>
              </div>
              <p className="text-muted-foreground text-lg">
                把握技术发展脉搏，发现下一个热门工具关键词
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {trendingKeywords.map((trend, index) => (
                <Card key={index} className="group hover:shadow-xl transition-all duration-300 hover:scale-105 border-2 border-transparent hover:border-primary/20">
                  <CardHeader className="pb-4">
                    <CardTitle className="text-lg font-bold text-foreground group-hover:text-primary transition-colors flex items-center">
                      <div className={`p-2 bg-gradient-to-r ${trend.color} rounded-lg mr-3 text-white`}>
                        {trend.icon}
                      </div>
                      <span className="text-base">{trend.category}</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="pt-0">
                    <div className="space-y-2">
                      {trend.items.map((item, idx) => (
                        <div key={idx} className="flex items-center p-2 rounded-lg bg-white/60 hover:bg-white/80 transition-colors">
                          <div className={`w-2 h-2 bg-gradient-to-r ${trend.color} rounded-full mr-3`}></div>
                          <span className="text-sm font-medium text-gray-700">{item}</span>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* 高价值关键词案例区域 */}
      <section className="py-8 px-4 bg-gray-50">
        <div className="container mx-auto">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-8">
              <div className="flex items-center justify-center mb-4">
                <div className="p-3 bg-gradient-to-r from-emerald-500 to-teal-600 rounded-lg mr-3">
                  <TrendingUp className="h-8 w-8 text-white" />
                </div>
                <h2 className="text-3xl font-bold text-foreground">高价值关键词案例</h2>
              </div>
              <p className="text-muted-foreground text-lg">
                基于市场验证的热门工具关键词，具备高流量和商业价值
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
              {[
                {
                  keyword: 'AI Code Generator',
                  description: '结合编程需求，开发智能代码生成工具',
                  value: '高',
                  color: 'from-blue-500 to-cyan-600'
                },
                {
                  keyword: 'Background Remover',
                  description: '图像处理高频需求，适合SaaS模式',
                  value: '极高',
                  color: 'from-green-500 to-emerald-600'
                },
                {
                  keyword: 'PDF Merger',
                  description: '办公场景刚需，用户付费意愿强',
                  value: '高',
                  color: 'from-purple-500 to-pink-600'
                },
                {
                  keyword: 'QR Code Generator',
                  description: '简单工具，流量转化率高',
                  value: '中高',
                  color: 'from-orange-500 to-red-600'
                }
              ].map((item, index) => (
                <Card key={index} className="group hover:shadow-xl transition-all duration-300 hover:scale-105">
                  <CardHeader className="pb-3">
                    <CardTitle className="text-lg font-bold text-foreground group-hover:text-primary transition-colors">
                      <div className={`p-2 bg-gradient-to-r ${item.color} rounded-lg mb-2 text-white`}>
                        <Sparkles className="h-5 w-5 mx-auto" />
                      </div>
                      <span className="text-base">{item.keyword}</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="pt-0">
                    <p className="text-sm text-muted-foreground mb-3">{item.description}</p>
                    <div className="flex justify-between items-center">
                      <span className="text-xs text-gray-500">商业价值</span>
                      <Badge className={`${item.value === '极高' ? 'bg-red-500' : item.value === '高' ? 'bg-orange-500' : 'bg-yellow-500'} text-white`}>
                        {item.value}
                      </Badge>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            <div className="text-center mb-8">
              <h3 className="text-2xl font-bold text-foreground mb-4">新兴机会关键词</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {[
                  'AI Voice Cloner',
                  'Smart Screenshot',
                  'Collaborative Planner',
                  'Mobile App Builder'
                ].map((keyword, index) => (
                  <div key={index} className="p-4 bg-white rounded-lg border-2 border-dashed border-blue-300 hover:border-blue-500 transition-colors">
                    <div className="flex items-center justify-center mb-2">
                      <div className="p-2 bg-gradient-to-r from-violet-500 to-purple-600 rounded-full">
                        <Sparkles className="h-4 w-4 text-white" />
                      </div>
                    </div>
                    <p className="font-semibold text-gray-800 text-sm">{keyword}</p>
                    <p className="text-xs text-gray-500 mt-1">新兴趋势</p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 使用指南区域 */}
      <section className="py-8 px-4">
        <div className="container mx-auto">
          <div className="max-w-4xl mx-auto">
            <Card className="shadow-lg">
              <CardHeader className="bg-gradient-to-r from-green-500 to-teal-600 text-white">
                <CardTitle className="text-2xl flex items-center">
                  <BookOpen className="h-6 w-6 mr-3" />
                  关键词扩展策略
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                  <div className="text-center">
                    <div className="p-4 bg-blue-50 rounded-lg mb-4">
                      <Target className="h-8 w-8 mx-auto text-blue-600 mb-2" />
                      <h3 className="font-bold text-blue-800">基础扩展</h3>
                    </div>
                    <p className="text-sm text-muted-foreground mb-3">
                      基于核心词延伸长尾词
                    </p>
                    <div className="text-xs text-gray-600 space-y-1">
                      <p>Generator → AI Essay Generator</p>
                      <p>Editor → Social Media Post Editor</p>
                      <p>Converter → Batch File Converter</p>
                    </div>
                  </div>

                  <div className="text-center">
                    <div className="p-4 bg-green-50 rounded-lg mb-4">
                      <Zap className="h-8 w-8 mx-auto text-green-600 mb-2" />
                      <h3 className="font-bold text-green-800">前缀组合</h3>
                    </div>
                    <p className="text-sm text-muted-foreground mb-3">
                      使用热门前缀提升关键词价值
                    </p>
                    <div className="flex flex-wrap gap-1 justify-center">
                      {['AI', 'Smart', 'Auto', 'Online', 'Cloud', 'Mobile'].map((prefix, idx) => (
                        <Badge key={idx} variant="secondary" className="text-xs">
                          {prefix}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  <div className="text-center">
                    <div className="p-4 bg-purple-50 rounded-lg mb-4">
                      <Settings className="h-8 w-8 mx-auto text-purple-600 mb-2" />
                      <h3 className="font-bold text-purple-800">后缀变化</h3>
                    </div>
                    <p className="text-sm text-muted-foreground mb-3">
                      不同后缀适应不同场景
                    </p>
                    <div className="flex flex-wrap gap-1 justify-center">
                      {['Tool', 'App', 'Platform', 'Suite', 'Studio', 'Hub'].map((suffix, idx) => (
                        <Badge key={idx} variant="outline" className="text-xs">
                          {suffix}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </div>

                <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-6">
                  <h4 className="font-bold text-gray-800 mb-4 text-center">扩展公式示例</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div className="bg-white rounded-lg p-4">
                      <p className="font-semibold text-blue-800 mb-2">核心词扩展：</p>
                      <p className="text-gray-600">Generator → Password Generator → AI Password Generator → Smart Password Generator Tool</p>
                    </div>
                    <div className="bg-white rounded-lg p-4">
                      <p className="font-semibold text-green-800 mb-2">场景细分：</p>
                      <p className="text-gray-600">Editor → Photo Editor → Online Photo Editor → AI Photo Editor Studio</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* 使用统一前台底部组件 */}
      <FrontendFooter />
    </div>
  )
}
