<?php
require_once '../utils/Response.php';
require_once '../models/ImportLog.php';

/**
 * 导入日志API
 * 处理导入日志的查询操作
 */

// 处理CORS预检请求
Response::handleOptions();

// 验证请求方法
$method = Response::validateMethod(['GET']);

try {
    $importLogModel = new ImportLog();
    
    switch ($method) {
        case 'GET':
            handleGet($importLogModel);
            break;
    }
    
} catch (Exception $e) {
    Response::serverError('服务器内部错误: ' . $e->getMessage());
}

/**
 * 处理GET请求 - 获取导入日志
 */
function handleGet($importLogModel) {
    $action = $_GET['action'] ?? 'recent';
    
    switch ($action) {
        case 'recent':
            handleRecentImports($importLogModel);
            break;
            
        case 'stats':
            handleImportStats($importLogModel);
            break;
            
        default:
            Response::badRequest('无效的action参数');
            break;
    }
}

/**
 * 处理最近导入请求
 */
function handleRecentImports($importLogModel) {
    $days = $_GET['days'] ?? 7;
    
    try {
        $imports = $importLogModel->getRecentImports($days);
        Response::success($imports);
    } catch (Exception $e) {
        Response::error('获取最近导入失败: ' . $e->getMessage());
    }
}

/**
 * 处理导入统计请求
 */
function handleImportStats($importLogModel) {
    $days = $_GET['days'] ?? 7;
    
    try {
        $stats = $importLogModel->getImportStats($days);
        Response::success($stats);
    } catch (Exception $e) {
        Response::error('获取导入统计失败: ' . $e->getMessage());
    }
}
?>
