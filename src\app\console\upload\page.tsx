'use client'

import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import ConsoleLayout from '../../../components/console-layout'
import CSVUploadTab from '../../../components/csv-upload-tab'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../../components/ui/card'
import { Alert, AlertDescription } from '../../../components/ui/alert'
import { Badge } from '../../../components/ui/badge'

import {
  Upload,
  Info,
  FileText,

  AlertTriangle,
  Zap,
  Target
} from 'lucide-react'

export default function UploadPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [activeTab, setActiveTab] = useState('upload')

  // 设置页面标题
  useEffect(() => {
    document.title = '数据导入 - CatchIdeas'
  }, [])

  useEffect(() => {
    if (status === 'loading') return // 还在加载中

    if (!session) {
      router.push('/auth/signin') // 未登录跳转到登录页
      return
    }

    // 所有已登录用户都可以访问upload页面
    // 不再限制只有管理员才能访问
  }, [session, status, router])

  if (status === 'loading') {
    return <div className="min-h-screen flex items-center justify-center">加载中...</div>
  }

  if (!session) {
    return null // 正在重定向
  }

  return (
    <ConsoleLayout
      title="智能数据导入"
    >
      <div className="space-y-8">
        {/* 功能概览 - 统一卡片风格 */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card className="data-card-enhanced stats-card-primary">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-primary/80">智能上传</CardTitle>
              <Upload className="h-5 w-5 text-primary" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">CSV导入</div>
              <p className="text-xs text-muted-foreground">
                支持拖拽和点击上传
              </p>
            </CardContent>
          </Card>

          <Card className="data-card-enhanced stats-card-success">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-success/80">自动过滤</CardTitle>
              <Target className="h-5 w-5 text-success" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">智能清理</div>
              <p className="text-xs text-muted-foreground">
                智能去重和数据清洗
              </p>
            </CardContent>
          </Card>

          <Card className="data-card-enhanced stats-card-secondary">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-secondary/80">快速处理</CardTitle>
              <Zap className="h-5 w-5 text-secondary" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">实时反馈</div>
              <p className="text-xs text-muted-foreground">
                实时进度和结果反馈
              </p>
            </CardContent>
          </Card>
        </div>

        {/* 使用指南和上传区域 */}
        <div className="space-y-6">
          {/* 自定义切换按钮 */}
          <div className="flex justify-center mb-6">
            <div className="inline-flex bg-slate-100/50 p-1 rounded-2xl">
              <button
                onClick={() => setActiveTab('upload')}
                className={`flex items-center space-x-2 px-8 py-3 mx-1 rounded-xl font-medium transition-all duration-300 hover:scale-[1.02] active:scale-[0.98] ${
                  activeTab === 'upload'
                    ? 'bg-primary text-primary-foreground shadow-lg shadow-primary/25'
                    : 'text-muted-foreground hover:text-foreground hover:bg-white/70'
                }`}
              >
                <Upload className="h-4 w-4" />
                <span>文件上传</span>
              </button>
              <button
                onClick={() => setActiveTab('guide')}
                className={`flex items-center space-x-2 px-8 py-3 mx-1 rounded-xl font-medium transition-all duration-300 hover:scale-[1.02] active:scale-[0.98] ${
                  activeTab === 'guide'
                    ? 'bg-secondary text-secondary-foreground shadow-lg shadow-secondary/25'
                    : 'text-muted-foreground hover:text-foreground hover:bg-white/70'
                }`}
              >
                <Info className="h-4 w-4" />
                <span>使用指南</span>
              </button>
            </div>
          </div>

          {/* 内容区域 */}
          {activeTab === 'upload' && (
            <div className="space-y-6">
              {/* 重要提示 */}
              <Alert className="border-primary/30 bg-card border-2">
                <Info className="h-4 w-4 text-primary" />
                <AlertDescription className="text-foreground">
                  <strong>支持格式：</strong>Google Trends导出的CSV文件，使用AI智能筛选高质量关键词。
                  <br />
                  <strong>处理规则：</strong>读取第一列数据（从第3行开始），最大文件大小10MB，AI自动过滤品牌词、通用词等。
                </AlertDescription>
              </Alert>

              {/* CSV上传组件 */}
              <CSVUploadTab />
            </div>
          )}

          {activeTab === 'guide' && (
            <div className="space-y-6">
            <Card className="data-card-enhanced">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <FileText className="h-5 w-5 text-primary" />
                  <span>使用指南</span>
                </CardTitle>
                <CardDescription>
                  了解如何正确使用CSV导入功能
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <div className="flex items-start space-x-3">
                    <Badge className="badge-analyzed mt-1">1</Badge>
                    <div>
                      <h4 className="font-medium text-foreground">准备CSV文件</h4>
                      <p className="text-sm text-muted-foreground mt-1">
                        从Google Trends导出CSV文件，确保关键词数据在第一列
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-3">
                    <Badge className="badge-analyzing mt-1">2</Badge>
                    <div>
                      <h4 className="font-medium text-foreground">上传文件</h4>
                      <p className="text-sm text-muted-foreground mt-1">
                        拖拽文件到上传区域或点击选择文件，支持最大10MB的CSV文件
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-3">
                    <Badge className="badge-trend mt-1">3</Badge>
                    <div>
                      <h4 className="font-medium text-foreground">自动处理</h4>
                      <p className="text-sm text-muted-foreground mt-1">
                        系统自动解析、过滤和去重，实时显示处理进度
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-3">
                    <Badge className="bg-success/10 text-success border-success/20 mt-1">4</Badge>
                    <div>
                      <h4 className="font-medium text-foreground">查看结果</h4>
                      <p className="text-sm text-muted-foreground mt-1">
                        查看详细的导入统计和过滤结果，了解数据质量
                      </p>
                    </div>
                  </div>
                </div>

                <Alert className="border-warning/30 bg-card border-2">
                  <AlertTriangle className="h-4 w-4 text-warning" />
                  <AlertDescription className="text-foreground">
                    <strong>注意事项：</strong>
                    <ul className="list-disc list-inside mt-2 space-y-1 text-sm">
                      <li>系统会自动跳过前2行（通常是标题行）</li>
                      <li>AI会智能过滤品牌词、通用词、人名、非英文内容等</li>
                      <li>重复的关键词会被自动去重</li>
                      <li>建议文件大小不超过10MB以确保处理速度</li>
                      <li>AI筛选标准严格，确保关键词具有SEO价值</li>
                    </ul>
                  </AlertDescription>
                </Alert>
              </CardContent>
            </Card>
            </div>
          )}
        </div>
      </div>
    </ConsoleLayout>
  )
}
