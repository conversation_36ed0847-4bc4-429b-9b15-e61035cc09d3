import { NextRequest, NextResponse } from 'next/server'
import { promises as dns } from 'dns'

export async function POST(request: NextRequest) {
  try {
    const { domain } = await request.json()

    if (!domain) {
      return NextResponse.json({
        success: false,
        error: '缺少域名参数'
      }, { status: 400 })
    }

    // 验证域名格式（只允许.com域名）
    if (!domain.endsWith('.com')) {
      return NextResponse.json({
        success: false,
        error: '只支持.com域名查询'
      }, { status: 400 })
    }

    // 简单的域名格式验证
    const domainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]*[a-zA-Z0-9]*\.com$/
    if (!domainRegex.test(domain)) {
      return NextResponse.json({
        success: false,
        error: '域名格式无效'
      }, { status: 400 })
    }

    try {
      // 尝试查询A记录
      await dns.resolve(domain, 'A')
      
      return NextResponse.json({
        success: true,
        data: {
          domain,
          registered: true,
          status: 'has_records',
          message: '域名已注册'
        }
      })
    } catch (dnsError: any) {
      if (dnsError.code === 'ENOTFOUND') {
        // 域名未找到，可能未注册
        return NextResponse.json({
          success: true,
          data: {
            domain,
            registered: false,
            status: 'no_records',
            message: '域名可能可用'
          }
        })
      } else if (dnsError.code === 'ENODATA') {
        // 域名存在但没有A记录，尝试其他记录类型
        try {
          // 尝试查询CNAME记录
          await dns.resolve(domain, 'CNAME')
          return NextResponse.json({
            success: true,
            data: {
              domain,
              registered: true,
              status: 'has_cname',
              message: '域名已注册'
            }
          })
        } catch {
          // 尝试查询MX记录
          try {
            await dns.resolve(domain, 'MX')
            return NextResponse.json({
              success: true,
              data: {
                domain,
                registered: true,
                status: 'has_mx',
                message: '域名已注册'
              }
            })
          } catch {
            return NextResponse.json({
              success: true,
              data: {
                domain,
                registered: false,
                status: 'no_records',
                message: '域名可能可用'
              }
            })
          }
        }
      } else if (dnsError.code === 'ETIMEOUT') {
        return NextResponse.json({
          success: true,
          data: {
            domain,
            registered: null,
            status: 'timeout',
            message: '查询超时'
          }
        })
      } else {
        return NextResponse.json({
          success: true,
          data: {
            domain,
            registered: null,
            status: 'query_failed',
            message: '查询失败'
          }
        })
      }
    }

  } catch (error) {
    return NextResponse.json({
      success: false,
      error: '服务器内部错误'
    }, { status: 500 })
  }
}
