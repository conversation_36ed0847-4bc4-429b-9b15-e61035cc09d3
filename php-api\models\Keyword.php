<?php
require_once '../config/database.php';

/**
 * 关键词模型类
 * 处理关键词相关的数据库操作
 */
class Keyword {
    private $conn;
    private $table_name = "keywords";

    public function __construct() {
        $database = new Database();
        $this->conn = $database->getConnection();
    }

    /**
     * 创建关键词
     */
    public function create($data) {
        $query = "INSERT INTO " . $this->table_name . "
                  (id, keyword, user_intent, user_pain_point, competition_level,
                   competition_score, competition_color, competition_description,
                   serp_analysis, category, source, import_date, analyzed_at)
                  VALUES
                  (:id, :keyword, :user_intent, :user_pain_point, :competition_level,
                   :competition_score, :competition_color, :competition_description,
                   :serp_analysis, :category, :source, :import_date, :analyzed_at)";

        $stmt = $this->conn->prepare($query);

        // 绑定参数
        $stmt->bindParam(":id", $data['id']);
        $stmt->bindParam(":keyword", $data['keyword']);
        $stmt->bindParam(":user_intent", $data['user_intent']);
        $stmt->bindParam(":user_pain_point", $data['user_pain_point']);
        $stmt->bindParam(":competition_level", $data['competition_level']);
        $stmt->bindParam(":competition_score", $data['competition_score']);
        $stmt->bindParam(":competition_color", $data['competition_color']);
        $stmt->bindParam(":competition_description", $data['competition_description']);
        $stmt->bindParam(":serp_analysis", $data['serp_analysis']);
        $stmt->bindParam(":category", $data['category']);
        $stmt->bindParam(":source", $data['source']);
        $stmt->bindParam(":import_date", $data['import_date']);
        $stmt->bindParam(":analyzed_at", $data['analyzed_at']);

        return $stmt->execute();
    }

    /**
     * 批量创建关键词 (使用INSERT IGNORE避免重复错误)
     */
    public function batchCreate($keywords) {
        if (empty($keywords)) {
            return ['inserted' => 0, 'skipped' => 0];
        }

        $this->conn->beginTransaction();

        try {
            // 插入基本字段，支持created_at
            $query = "INSERT IGNORE INTO " . $this->table_name . "
                      (id, keyword, source, import_date, created_at)
                      VALUES (?, ?, ?, ?, ?)";

            $stmt = $this->conn->prepare($query);
            $insertedCount = 0;
            $skippedCount = 0;

            foreach ($keywords as $keyword) {
                $stmt->execute([
                    $keyword['id'],
                    $keyword['keyword'],
                    $keyword['source'] ?? 'google_trends',
                    $keyword['import_date'] ?? date('Y-m-d'),
                    $keyword['created_at'] ?? date('Y-m-d H:i:s')
                ]);

                // 检查是否真正插入了数据
                if ($stmt->rowCount() > 0) {
                    $insertedCount++;
                } else {
                    $skippedCount++;
                }
            }

            $this->conn->commit();
            return ['inserted' => $insertedCount, 'skipped' => $skippedCount];
        } catch (Exception $e) {
            $this->conn->rollback();
            throw $e;
        }
    }

    /**
     * 获取关键词列表
     */
    public function getList($filters = [], $limit = 20, $offset = 0, $sort_by = 'created_at', $sort_order = 'desc') {
        $where_conditions = [];
        $params = [];

        // 构建WHERE条件
        if (!empty($filters['category'])) {
            $where_conditions[] = "category = :category";
            $params[':category'] = $filters['category'];
        }

        if (!empty($filters['source'])) {
            $where_conditions[] = "source = :source";
            $params[':source'] = $filters['source'];
        }

        if (!empty($filters['import_date'])) {
            $where_conditions[] = "import_date = :import_date";
            $params[':import_date'] = $filters['import_date'];
        }

        if (!empty($filters['search'])) {
            $where_conditions[] = "keyword LIKE :search";
            $params[':search'] = '%' . $filters['search'] . '%';
        }

        // 分析状态筛选 - 修复逻辑，与前端保持一致
        if (isset($filters['analyzed_only'])) {
            if ($filters['analyzed_only'] === 'true') {
                // 已分析：任意一个分析字段不为空
                $where_conditions[] = "(user_intent IS NOT NULL OR user_pain_point IS NOT NULL OR competition_level IS NOT NULL)";
            } elseif ($filters['analyzed_only'] === 'false') {
                // 未分析：所有分析字段都为空
                $where_conditions[] = "(user_intent IS NULL AND user_pain_point IS NULL AND competition_level IS NULL)";
            }
        }

        // 竞争难度筛选
        if (!empty($filters['competition_level'])) {
            $where_conditions[] = "competition_level = :competition_level";
            $params[':competition_level'] = $filters['competition_level'];
        }

        // 日期范围筛选
        if (!empty($filters['date_range'])) {
            switch ($filters['date_range']) {
                case 'today':
                    $where_conditions[] = "DATE(created_at) = CURDATE()";
                    break;
                case 'week':
                    $where_conditions[] = "created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)";
                    break;
                case 'month':
                    $where_conditions[] = "YEAR(created_at) = YEAR(NOW()) AND MONTH(created_at) = MONTH(NOW())";
                    break;
            }
        }

        $where_clause = !empty($where_conditions) ? "WHERE " . implode(" AND ", $where_conditions) : "";

        // 构建排序子句
        $allowed_sort_fields = ['created_at', 'analyzed_at', 'keyword', 'competition_score', 'category'];
        $sort_field = in_array($sort_by, $allowed_sort_fields) ? $sort_by : 'created_at';
        $sort_direction = strtoupper($sort_order) === 'ASC' ? 'ASC' : 'DESC';

        $query = "SELECT * FROM " . $this->table_name . "
                  " . $where_clause . "
                  ORDER BY " . $sort_field . " " . $sort_direction . "
                  LIMIT :limit OFFSET :offset";

        $stmt = $this->conn->prepare($query);
        
        // 绑定参数
        foreach ($params as $key => $value) {
            $stmt->bindValue($key, $value);
        }
        $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);

        $stmt->execute();
        return $stmt->fetchAll();
    }

    /**
     * 获取关键词总数
     */
    public function getCount($filters = []) {
        $where_conditions = [];
        $params = [];

        // 构建WHERE条件 (与getList相同)
        if (!empty($filters['category'])) {
            $where_conditions[] = "category = :category";
            $params[':category'] = $filters['category'];
        }

        if (!empty($filters['source'])) {
            $where_conditions[] = "source = :source";
            $params[':source'] = $filters['source'];
        }

        if (!empty($filters['import_date'])) {
            $where_conditions[] = "import_date = :import_date";
            $params[':import_date'] = $filters['import_date'];
        }

        if (!empty($filters['search'])) {
            $where_conditions[] = "keyword LIKE :search";
            $params[':search'] = '%' . $filters['search'] . '%';
        }

        // 分析状态筛选 - 修复逻辑，与前端保持一致
        if (isset($filters['analyzed_only'])) {
            if ($filters['analyzed_only'] === 'true') {
                // 已分析：任意一个分析字段不为空
                $where_conditions[] = "(user_intent IS NOT NULL OR user_pain_point IS NOT NULL OR competition_level IS NOT NULL)";
            } elseif ($filters['analyzed_only'] === 'false') {
                // 未分析：所有分析字段都为空
                $where_conditions[] = "(user_intent IS NULL AND user_pain_point IS NULL AND competition_level IS NULL)";
            }
        }

        // 竞争难度筛选
        if (!empty($filters['competition_level'])) {
            $where_conditions[] = "competition_level = :competition_level";
            $params[':competition_level'] = $filters['competition_level'];
        }

        // 日期范围筛选
        if (!empty($filters['date_range'])) {
            switch ($filters['date_range']) {
                case 'today':
                    $where_conditions[] = "DATE(created_at) = CURDATE()";
                    break;
                case 'week':
                    $where_conditions[] = "created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)";
                    break;
                case 'month':
                    $where_conditions[] = "YEAR(created_at) = YEAR(NOW()) AND MONTH(created_at) = MONTH(NOW())";
                    break;
            }
        }

        $where_clause = !empty($where_conditions) ? "WHERE " . implode(" AND ", $where_conditions) : "";

        $query = "SELECT COUNT(*) as total FROM " . $this->table_name . " " . $where_clause;
        $stmt = $this->conn->prepare($query);
        
        foreach ($params as $key => $value) {
            $stmt->bindValue($key, $value);
        }

        $stmt->execute();
        $result = $stmt->fetch();
        return $result['total'];
    }

    /**
     * 根据ID获取关键词
     */
    public function getById($id) {
        $query = "SELECT * FROM " . $this->table_name . " WHERE id = :id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":id", $id);
        $stmt->execute();
        return $stmt->fetch();
    }

    /**
     * 根据关键词文本获取关键词
     */
    public function getByKeyword($keyword) {
        $query = "SELECT * FROM " . $this->table_name . " WHERE keyword = :keyword";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":keyword", $keyword);
        $stmt->execute();
        return $stmt->fetch();
    }

    /**
     * 更新关键词
     */
    public function update($id, $data) {
        $set_clauses = [];
        $params = [':id' => $id];

        // 动态构建SET子句
        $allowed_fields = [
            'user_intent', 'user_pain_point', 'competition_level', 
            'competition_score', 'competition_color', 'competition_description',
            'serp_analysis', 'user_comment', 'category', 'notes'
        ];

        foreach ($allowed_fields as $field) {
            if (isset($data[$field])) {
                $set_clauses[] = "$field = :$field";
                $params[":$field"] = $data[$field];
            }
        }

        if (empty($set_clauses)) {
            return false;
        }

        // 添加分类锁定逻辑
        if (isset($data['category'])) {
            $set_clauses[] = "category_locked = :category_locked";
            $set_clauses[] = "category_assigned_at = :category_assigned_at";
            $params[':category_locked'] = true;
            $params[':category_assigned_at'] = date('Y-m-d H:i:s');
        }

        $query = "UPDATE " . $this->table_name . " 
                  SET " . implode(", ", $set_clauses) . " 
                  WHERE id = :id";

        $stmt = $this->conn->prepare($query);
        return $stmt->execute($params);
    }

    /**
     * 删除关键词
     */
    public function delete($id) {
        $query = "DELETE FROM " . $this->table_name . " WHERE id = :id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":id", $id);
        return $stmt->execute();
    }

    /**
     * 检查关键词是否存在
     */
    public function exists($keyword) {
        $query = "SELECT COUNT(*) as count FROM " . $this->table_name . " WHERE keyword = :keyword";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":keyword", $keyword);
        $stmt->execute();
        $result = $stmt->fetch();
        return $result['count'] > 0;
    }

    /**
     * 批量检查关键词是否存在
     */
    public function batchExists($keywords) {
        if (empty($keywords)) {
            return [];
        }

        // 构建IN查询
        $placeholders = str_repeat('?,', count($keywords) - 1) . '?';
        $query = "SELECT keyword FROM " . $this->table_name . " WHERE keyword IN ($placeholders)";

        $stmt = $this->conn->prepare($query);
        $stmt->execute($keywords);

        $existingKeywords = [];
        while ($row = $stmt->fetch()) {
            $existingKeywords[] = $row['keyword'];
        }

        return $existingKeywords;
    }

    /**
     * 获取未分析的关键词 - 修复逻辑，与前端保持一致
     */
    public function getUnanalyzedKeywords($limit = 50) {
        // 修复逻辑：所有分析字段都为空才算未分析
        $query = "SELECT id, keyword, created_at, source FROM " . $this->table_name . "
                  WHERE (user_intent IS NULL AND user_pain_point IS NULL AND competition_level IS NULL)
                  ORDER BY created_at ASC
                  LIMIT :limit";

        $stmt = $this->conn->prepare($query);
        $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
        $stmt->execute();

        $results = $stmt->fetchAll();

        return $results;
    }

    /**
     * 获取统计信息 - 修复逻辑，与前端保持一致
     */
    public function getStats() {
        $query = "SELECT
                    COUNT(*) as total,
                    COUNT(CASE WHEN category IS NOT NULL THEN 1 END) as categorized,
                    COUNT(CASE WHEN (user_intent IS NOT NULL OR user_pain_point IS NOT NULL OR competition_level IS NOT NULL) THEN 1 END) as analyzed,
                    COUNT(CASE WHEN import_date = CURDATE() THEN 1 END) as today_imported,
                    COUNT(CASE WHEN (user_intent IS NULL AND user_pain_point IS NULL AND competition_level IS NULL) THEN 1 END) as unanalyzed
                  FROM " . $this->table_name;

        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        $stats = $stmt->fetch();

        // 获取categories表的数量
        $categoryCountQuery = "SELECT COUNT(*) as category_count FROM categories";
        $categoryCountStmt = $this->conn->prepare($categoryCountQuery);
        $categoryCountStmt->execute();
        $categoryStats = $categoryCountStmt->fetch();
        $stats['category_count'] = (int)$categoryStats['category_count'];

        return $stats;
    }

    /**
     * 检查关键词重复
     */
    public function checkDuplicates($keywords) {
        if (empty($keywords)) {
            return ['existing' => [], 'new' => []];
        }

        // 构建IN查询的占位符
        $placeholders = str_repeat('?,', count($keywords) - 1) . '?';

        $query = "SELECT keyword FROM " . $this->table_name . "
                  WHERE keyword IN ($placeholders)";

        $stmt = $this->conn->prepare($query);
        $stmt->execute($keywords);

        $existingKeywords = $stmt->fetchAll(PDO::FETCH_COLUMN);

        // 区分已存在和新关键词
        $existing = [];
        $new = [];

        foreach ($keywords as $keyword) {
            if (in_array($keyword, $existingKeywords)) {
                $existing[] = $keyword;
            } else {
                $new[] = $keyword;
            }
        }

        return [
            'existing' => $existing,
            'new' => $new
        ];
    }

    /**
     * 获取每日统计数据
     */
    public function getDailyStats($date) {
        // 获取当日导入的关键词数量
        $importQuery = "SELECT COUNT(*) as imports FROM " . $this->table_name . "
                       WHERE DATE(created_at) = :date";
        $importStmt = $this->conn->prepare($importQuery);
        $importStmt->bindParam(':date', $date);
        $importStmt->execute();
        $importResult = $importStmt->fetch(PDO::FETCH_ASSOC);

        // 获取当日分析的关键词数量
        $analyzeQuery = "SELECT COUNT(*) as analyzes FROM " . $this->table_name . "
                        WHERE DATE(analyzed_at) = :date AND analyzed_at IS NOT NULL";
        $analyzeStmt = $this->conn->prepare($analyzeQuery);
        $analyzeStmt->bindParam(':date', $date);
        $analyzeStmt->execute();
        $analyzeResult = $analyzeStmt->fetch(PDO::FETCH_ASSOC);

        return [
            'imports' => (int)$importResult['imports'],
            'analyzes' => (int)$analyzeResult['analyzes']
        ];
    }

    /**
     * 获取最近活动记录
     */
    public function getRecentActivity($limit = 5) {
        $activities = [];

        // 获取最近的导入活动（按日期分组）
        $importQuery = "SELECT
                           'import' as type,
                           CONCAT('导入了 ', COUNT(*), ' 个关键词') as description,
                           DATE(created_at) as activity_date,
                           COUNT(*) as count,
                           MAX(created_at) as created_at
                       FROM " . $this->table_name . "
                       WHERE created_at IS NOT NULL
                       GROUP BY DATE(created_at)
                       ORDER BY activity_date DESC
                       LIMIT 3";

        $importStmt = $this->conn->prepare($importQuery);
        $importStmt->execute();

        while ($row = $importStmt->fetch(PDO::FETCH_ASSOC)) {
            $activities[] = [
                'id' => uniqid(),
                'type' => $row['type'],
                'description' => $row['description'],
                'created_at' => $row['created_at'],
                'count' => (int)$row['count']
            ];
        }

        // 获取最近的分析活动（按日期分组）
        $analyzeQuery = "SELECT
                            'analyze' as type,
                            CONCAT('完成了 ', COUNT(*), ' 个关键词的AI分析') as description,
                            DATE(analyzed_at) as activity_date,
                            COUNT(*) as count,
                            MAX(analyzed_at) as created_at
                        FROM " . $this->table_name . "
                        WHERE analyzed_at IS NOT NULL
                        GROUP BY DATE(analyzed_at)
                        ORDER BY activity_date DESC
                        LIMIT 3";

        $analyzeStmt = $this->conn->prepare($analyzeQuery);
        $analyzeStmt->execute();

        while ($row = $analyzeStmt->fetch(PDO::FETCH_ASSOC)) {
            $activities[] = [
                'id' => uniqid(),
                'type' => $row['type'],
                'description' => $row['description'],
                'created_at' => $row['created_at'],
                'count' => (int)$row['count']
            ];
        }

        // 按时间排序并限制数量
        usort($activities, function($a, $b) {
            return strtotime($b['created_at']) - strtotime($a['created_at']);
        });

        return array_slice($activities, 0, (int)$limit);
    }

    /**
     * 开始事务
     */
    public function beginTransaction() {
        return $this->conn->beginTransaction();
    }

    /**
     * 提交事务
     */
    public function commit() {
        return $this->conn->commit();
    }

    /**
     * 回滚事务
     */
    public function rollback() {
        return $this->conn->rollback();
    }
}
?>
