import type { Metadata } from "next";

export const metadata: Metadata = {
  title: "App Store分析工具 | CatchIdeas",
  description: "将App Store链接转换为SensorTower分析链接的专业工具，支持单个和批量处理，快速获取应用数据分析，助力移动应用市场研究。",
  authors: [{ name: "CatchIdeas团队" }],
  creator: "CatchIdeas",
  publisher: "CatchIdeas",
  robots: "index, follow",
  keywords: [
    "App Store分析",
    "SensorTower",
    "应用分析工具",
    "移动应用数据",
    "App Store链接转换",
    "应用市场研究",
    "iOS应用分析",
    "应用数据分析",
    "移动应用工具",
    "App ID提取"
  ],
  openGraph: {
    type: "website",
    locale: "zh_CN",
    url: "https://catchideas.com/app-analyzer",
    title: "App Store分析工具 | CatchIdeas",
    description: "将App Store链接转换为SensorTower分析链接的专业工具，支持单个和批量处理，快速获取应用数据分析，助力移动应用市场研究。",
    siteName: "CatchIdeas",
  },
  twitter: {
    card: "summary_large_image",
    title: "App Store分析工具 | CatchIdeas",
    description: "将App Store链接转换为SensorTower分析链接的专业工具，支持单个和批量处理，快速获取应用数据分析，助力移动应用市场研究。",
  },
  alternates: {
    canonical: "https://catchideas.com/app-analyzer",
  },
};

export default function AppAnalyzerLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}
